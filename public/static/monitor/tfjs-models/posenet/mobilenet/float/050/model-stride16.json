{"format": "graph-model", "generatedBy": "2.0.0-dev20190603", "convertedBy": "TensorFlow.js Converter v1.1.2", "modelTopology": {"node": [{"name": "sub_2", "op": "Placeholder", "attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "1"}, {"size": "-1"}, {"size": "-1"}, {"size": "3"}]}}}}, {"name": "MobilenetV1/offset_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/offset_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_0/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/heatmap_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "17"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/heatmap_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "17"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_fwd_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_fwd_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_0/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["sub_2", "MobilenetV1/Conv2d_0/weights"], "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_0/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_0/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_0/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_0/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_0/Relu6", "MobilenetV1/Conv2d_1_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_depthwise/Relu6", "MobilenetV1/Conv2d_1_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Relu6", "MobilenetV1/Conv2d_2_depthwise/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_depthwise/Relu6", "MobilenetV1/Conv2d_2_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Relu6", "MobilenetV1/Conv2d_3_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_depthwise/Relu6", "MobilenetV1/Conv2d_3_pointwise/weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Relu6", "MobilenetV1/Conv2d_4_depthwise/depthwise_weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_depthwise/Relu6", "MobilenetV1/Conv2d_4_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Relu6", "MobilenetV1/Conv2d_5_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_depthwise/Relu6", "MobilenetV1/Conv2d_5_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Relu6", "MobilenetV1/Conv2d_6_depthwise/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_depthwise/Relu6", "MobilenetV1/Conv2d_6_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Relu6", "MobilenetV1/Conv2d_7_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_depthwise/Relu6", "MobilenetV1/Conv2d_7_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Relu6", "MobilenetV1/Conv2d_8_depthwise/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_depthwise/Relu6", "MobilenetV1/Conv2d_8_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Relu6", "MobilenetV1/Conv2d_9_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_depthwise/Relu6", "MobilenetV1/Conv2d_9_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Relu6", "MobilenetV1/Conv2d_10_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_depthwise/Relu6", "MobilenetV1/Conv2d_10_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Relu6", "MobilenetV1/Conv2d_11_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_depthwise/Relu6", "MobilenetV1/Conv2d_11_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Relu6", "MobilenetV1/Conv2d_12_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_depthwise/Relu6", "MobilenetV1/Conv2d_12_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Relu6", "MobilenetV1/Conv2d_13_depthwise/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_depthwise/Relu6", "MobilenetV1/Conv2d_13_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm/add_1", "op": "BiasAdd", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm/add_1/conv", "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/offset_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/offset_2/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/heatmap_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/heatmap_2/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/displacement_fwd_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/displacement_fwd_2/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/displacement_bwd_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/displacement_bwd_2/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "MobilenetV1/offset_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/offset_2/BiasAdd/conv", "MobilenetV1/offset_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/heatmap_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/heatmap_2/BiasAdd/conv", "MobilenetV1/heatmap_2/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_fwd_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/displacement_fwd_2/BiasAdd/conv", "MobilenetV1/displacement_fwd_2/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/displacement_bwd_2/BiasAdd/conv", "MobilenetV1/displacement_bwd_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}], "library": {}, "versions": {"producer": 54}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "MobilenetV1/offset_2/Conv2D_bias", "shape": [34], "dtype": "float32"}, {"name": "MobilenetV1/offset_2/weights", "shape": [1, 1, 256, 34], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_13_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_12_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_11_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_10_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_9_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_8_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_7_pointwise/weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bias", "shape": [256], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_6_pointwise/weights", "shape": [1, 1, 128, 256], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bias", "shape": [128], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_weights", "shape": [3, 3, 128, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bias", "shape": [128], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_5_pointwise/weights", "shape": [1, 1, 128, 128], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bias", "shape": [128], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_weights", "shape": [3, 3, 128, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bias", "shape": [128], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_4_pointwise/weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bias", "shape": [64], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_weights", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bias", "shape": [64], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_3_pointwise/weights", "shape": [1, 1, 64, 64], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bias", "shape": [64], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_weights", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bias", "shape": [64], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_2_pointwise/weights", "shape": [1, 1, 32, 64], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bias", "shape": [32], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_weights", "shape": [3, 3, 32, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bias", "shape": [32], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_1_pointwise/weights", "shape": [1, 1, 16, 32], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bias", "shape": [16], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_weights", "shape": [3, 3, 16, 1], "dtype": "float32"}, {"name": "MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bias", "shape": [16], "dtype": "float32"}, {"name": "MobilenetV1/Conv2d_0/weights", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "MobilenetV1/heatmap_2/Conv2D_bias", "shape": [17], "dtype": "float32"}, {"name": "MobilenetV1/heatmap_2/weights", "shape": [1, 1, 256, 17], "dtype": "float32"}, {"name": "MobilenetV1/displacement_fwd_2/Conv2D_bias", "shape": [32], "dtype": "float32"}, {"name": "MobilenetV1/displacement_fwd_2/weights", "shape": [1, 1, 256, 32], "dtype": "float32"}, {"name": "MobilenetV1/displacement_bwd_2/Conv2D_bias", "shape": [32], "dtype": "float32"}, {"name": "MobilenetV1/displacement_bwd_2/weights", "shape": [1, 1, 256, 32], "dtype": "float32"}]}]}