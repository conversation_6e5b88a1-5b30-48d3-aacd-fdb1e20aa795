var exec = window.cordova.require("cordova/exec");

function BasePlugin(pluginName) {
  //插件名称对应原生配置插件名称  config plugin=name
  this.pluginName = pluginName;
  //methodName原生方法名
  this.execNativeMethod = function(methodName, args, success, fail) {
    exec(success, fail, this.pluginName, methodName, args);
  }
}

/**
 *分享
 */
function Share() {
  this.method = BasePlugin;
  this.method("UShare");
  delete this.method;

  /**
   * 分享/复制
   * shareContent jsonString：{"title":"标题", "summary":"内容", "image_url":"图片远程地址", "target_url":"点开分享跳转地址", "type":"1/2/3/4/5"}
   * type：1（复制target_url）、2（分享QQ）、3（分享QQ空间）、4（分享给微信好友）、5（分享给微信朋友圈）
   **/
  this.share = function(shareContent, success, fail) {
    this.execNativeMethod("share", [shareContent], success, fail);
  }
}

// 基础的和原生交互的工具
function BaseTool() {
  this.method = BasePlugin;
  this.method("BaseTool");
  delete this.method;
  // 返回原生方法（退出当前H5页面）
  this.goToNative = function() {
    if (ionic.Platform.platforms[2] == "android") {
      window.ULplugin.UApp.finish();
    } else {
      this.execNativeMethod("goToNative", [], null, null);
    }
  }
}

function UApp() {
  this.method = BasePlugin;
  this.method('UApp');
  delete this.method;
  // 返回原生方法（退出当前H5页面）
  this.finish = function() {
    var platform = navigator.userAgent.toLowerCase();
    if (/iphone|ipad|ipod/.test(platform)) {
      this.execNativeMethod("goToNative", [], null, null);
    } else if (/android/.test(platform)) {
      this.execNativeMethod('finish', [], null, null);
    }
  }
  // 后台
  this.back = function() {
    this.execNativeMethod('back', [], null, null);
  }
  // 在新页面打开指定地址
  // external: true/false 是否通过系统浏览器打开
  this.openUrl = function(url, external) {
    this.execNativeMethod('openUrl', [url, external], null, null);
  }
  /**
   * 打开新页面
   * url：地址
   * hideClientNavigation：是否隐藏客户端导航栏。 true:隐藏、false：不隐藏
   **/
  this.openNewWindow = function(url, hideClientNavigation) {
    this.execNativeMethod('openNewWindow', [{ 'url': url }, hideClientNavigation], null, null);
  }
  /**
   * 网络状态
   * 成功回调：success = function(state){}  state:-1（未知网络）、0（无网络）、1（Wi-Fi）、2（移动网络）
   **/
  this.networkState = function(success) {
    this.execNativeMethod('networkState', [], success, null);
  }
}

function Umessage() {
  this.method = BasePlugin;
  this.method('UMessage');
  delete this.method;
  /**
   * 打开聊天页面
   * userName：当前用户用户名（账号）
   * name：聊天目标用户用户名（账号）
   **/
  this.goToChat = function(userName, name) {
    this.execNativeMethod('goToChat', [{ "userName": userName, 'name': name }], null, null);
  }
}

function User() {
  this.method = BasePlugin;
  this.method('User');
  delete this.method;
  /**
   * 获取当前登陆用户的信息
   * success = function(jsonString){}
   * jsonString：{"userID":1, "loginName":"登陆账号", "name":"姓名", "telphone":"电话", "email":"邮箱", "token":"token", "role":8/9, "sex":true/false, "avatar":"头像url"}
   * role： 8（教师） 9（学生）
   * sex： true（男） false(女)
   **/
  this.getUser = function(success) {
    this.execNativeMethod('getUser', [], success, null);
  }
}

function Recorder() {
  this.method = BasePlugin;
  this.method('URecorder');
  delete this.method;
  this.isRecord = function() {
    return this.recording;
  }
  /**
   * 开始录音
   * 开始录音回调：startRecordFunc = function(stringData){}    stringData空内容
   * 停止录音回调：stopRecordFunc = function(stringData){}     stringData空内容
   * 录音文件回调：recordFileFunc = function(filePath){}       filePath录音文件本地地址
   * 录音音量大小回调：volumeFunc = function(volume){}             声音大小
   **/
  this.startRecord = function(startRecordFunc, stopRecordFunc, recordFileFunc, volumeFunc) {
    window.startRecord = startRecordFunc;
    window.stopRecord = stopRecordFunc;
    window.recordFile = recordFileFunc;
    window.volume = volumeFunc;
    this.execNativeMethod('startRecord', [], null, null);
  }
  /**
   * 停止录音
   **/
  this.stopRecord = function() {
    this.execNativeMethod('stopRecord', [], null, null);
  }
  /**
   * 检查录音权限
   * success 检查录音权限可以正常使用
   * fail 检查录音权限不可以正常使用。权限被禁止或者录音资源被占用
   **/
  this.checkPermission = function(success, fail) {
    this.execNativeMethod('checkPermission', [], success, fail);
  }
  /**
   * 录音插件版本
   * success = function(versionJsonObj){}    {'version':2}
   **/
  this.version = function(success) {
    this.execNativeMethod('version', [], success, null);
  }
  /**
   * 启动评分录音
   * speechType: read_word（单词）、read_sentence（句子）、read_pred(段落)、exam（开放式练习，多个参考答案）
   * content： 评分内容
   * category: 模块。 1（课程）、2（单词听写）、3（口语测评）
   * 测评录音开始回调：   onBeginOfSpeech = function(dataString){}  dataString 空内容
   * 测评录音音量大小回调： onVolumeChanged = function(volumeString){} volumeString 声音音量。 
   * 测评录音结束回调：   onEndOfSpeech = function(recordFilePath){} recordFilePath 本地录音文件地址。 录音结束。
   * 测评录音结果回调：   onResult = function(jsonArrayString){} jsonArrayString 录音评分结果。 开放式 练习返回结果：[{"score":100}]，其他返回结果：[{"hello": 50, "word": 50}]
   * 测评录音错误回调：   onError = function(errorJsonObject){} errorJsonObject 录音错误反馈信息。{"ErrorCode", intValue, "ErrorDescription":"错误信息"}
   **/
  this.startEvaluator = function(speechType, content, category, onBeginOfSpeech, onVolumeChanged, onEndOfSpeech, onResult, onError) {
    window.onBeginOfSpeech = onBeginOfSpeech;
    window.onVolumeChanged = onVolumeChanged;
    window.onEndOfSpeech = onEndOfSpeech;
    window.onResult = onResult;
    window.onError = onError;
    this.execNativeMethod('startEvaluator', [speechType, content, category], null, null);
  }
  /**
   * 停止评分录音
   **/
  this.stopEvaluator = function() {
    this.execNativeMethod('stopEvaluator', [], null, null);
  }
}

function UFilePlugin() {
  this.method = BasePlugin;
  this.method('UFile');
  delete this.method;
  /**
   * 预览文件
   * filePath：本地文件地址。通过下载方法获取本地文件地址。
   * fileName：文件名。（标题）
   **/
  this.preFile = function(filePath, fileName) {
    this.execNativeMethod('openFileInOtherApp', [filePath, fileName], null, null);
  }

  /**
   * 下载文件
   * 下载进度回调：function progressFunction(filePath, progress){}  progress=0-100
   * 下载成功回调：function successFunction(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function errorFunction(filePath, error){}  error失败描述
   **/
  this.download = function(filePath, progressFunction, successFunction, errorFunction) {
    // window.progressFunction = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    if (!window.onDownloadAllSuccessed) {
      window.onDownloadAllSuccessed = function() {}
    };

    if (filePath != null && filePath.indexOf("?name") == -1) {
      try {
        var fileFullName = filePath.split("/").pop();
        var fileName = fileFullName.split(".")[0];
        filePath = filePath + "?name=" + fileName;
      } catch (e) {
        window.console.error(e);
      }
    };
    this.execNativeMethod('download', [filePath], null, null);
  }
}

/**
 * 课程插件
 * 提供课程基本信息、课程结构、课程内容
 **/
function CoursePlugin() {
  this.method = BasePlugin;
  this.method('UCourse');
  delete this.method;
  /**
   * 课程基本信息
   * 成功回调：success = function(jsonString){}   jsonString：{"name":"课程", "needapprove":"0/1"}
   **/
  this.info = function(success) {
    this.execNativeMethod('info', [], success, null);
  }

  /**
   * 接口地址：https://www.tongshike.cn/apidoc/index.html?url=../yaml/ua-course.yaml#!/查询一个course的章节页目录结构/get_course_stu_id_directory
   * 接口：/course/stu/{courseid}/directory
   * 获取课程目录(包括课程章节的隐藏情况)
   * 成功回调：success = function(jsonString){}   jsonString参考接口文档数据
   **/
  this.directory = function(success) {
    this.execNativeMethod('directory', [], success, null);
  }

  /**
   * 接口地址：https://apps.tongshike.cn/apidoc/index.html?url=../yaml/coursepage.yaml#!/整个页面/get_wholepage_stu_id
   * 接口：/wholepage/chapter/stu/{chapterid}
   * 获取一章的课程页面详情
   * chapterId：章id
   * 成功回调：success = function(jsonString){}   jsonString 前端提供数据结构
   **/
  this.chapterDetail = function(chapterId, success) {
    this.execNativeMethod('chapterDetail', [chapterId], success, null);
  }

  /**
   * 单词释义接口
   * key为要传入的单词：hello
   * 成功回调接口：success = function(content){}  content 为释义内容，内容可能为空，单词释义表缺省。
   **/
  this.glossary = function(key, success) {
    this.execNativeMethod('glossary', [key], success, null);
  }

  /**
   * 下载文件
   * chapterId： 章id
   * sectionId： 节id
   * pageId：    页面id
   * url：       下载地址
   * 下载进度回调：function progressFunction(filePath, progress){}  progress=0-100
   * 下载成功回调：function successFunction(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function errorFunction(filePath, error){}  error失败描述
   **/
  this.downloadFile = function(chapterId, sectionId, pageId, url, progressFunction, successFunction, errorFunction) {
    // window.progressFunction = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    try {
      delete window.pageResourceMap[pageId];
    } catch (e) {}
    this.execNativeMethod('downloadFile', [chapterId, sectionId, pageId, url], null, null);
  }

  /**
   * 取消下载
   * 一定会成功，所以没有回调
   **/
  this.cancelDownload = function(chapterId, sectionId, pageId, url) {
    this.execNativeMethod('cancelDownloadFile', [chapterId, sectionId, pageId, url], null, null);
  }

  /**
   * 上传文件
   * file：上传文件本地路径
   * fileName：文件名，也是上传七牛的虚拟资源路径。 record/course/userId/xxxxx.mp3|wav
   * 上传进度回调：funciton progressFunction(file, progress){}   progress=0-100
   * 上传成功回调：function successFuntion(file, url){}  url：资源服务器相对路径 record/course/userId/xxxxx.mp3|wav
   * 上传失败回调：function errorFuntion(file, code, desc){}   code：失败代码 desc：失败描述信息
   **/
  this.uploadFile = function(file, fileName, progressFunction, successFunction, errorFunction) {
    window.uploadFileProgressCallback = progressFunction;
    window.uploadFileErrorCallback = errorFunction;
    window.uploadFileSuccessCallback = successFunction;
    this.execNativeMethod('uploadFile', [file, fileName], null, null);
  }

  /**
   * 初始化客户端 页面（下载任务）
   * pageId： 初始页面id
   * success: function(jsonString){} {"tasks":[{"url":"url", "progress":0.12, "status":0/1/2/3/4}], "models":[{"url":"url", "local":"file:///xxx/xx/xx.doc", "size":123123}]}
   * tasks：下载任务。  models：已下载完成的数据模型
   * status=0/1/2/3/4 0:等待、1:下载中、2:下载成功、3:取消下载、4:下载失败。 progress：下载进度。 local：已缓存本地地址。 size：缓存本地文件大小
   * 下载进度回调：function progressFunction(filePath, progress){}  progress=0-100
   * 下载成功回调：function successFunction(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function errorFunction(filePath, error){}  error失败描述
   **/
  this.initPage = function(pageId, success, progressFunction, successFunction, errorFunction) {
    // window.progressFunction = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    this.execNativeMethod('initPage', [pageId], success, null);
  }
}

/**
 * 学习记录插件
 * 增改、查
 **/
function StudyRecordPlugin() {
  this.method = BasePlugin;
  this.method('UStudyRecord');
  delete this.method;

  /**
   * 保存或更新学习记录，离开页面时掉用
   * record：{"chapterId":1, "sectionId":1, "sectionScore":1, "sectionStudyTime":1, "sectionCompletionStatus":1, 
            "pageStudyRecordDTOList":[{"answerTime":1, "complete":1, "pageid":1, "score":1, "studyTime":1, 
                                      "questions":[], "videos":[], "speaks":[]}
                                      ]
            }
   * 成功回调：success = function(recordId){}  recordId记录唯一标示
   * 失败回调：fail = function(failDesc){} failDesc失败描述
   **/
  this.saveOrUpdateRecord = function(record, success, fail) {
    this.execNativeMethod('saveOrUpdateRecord', [record], success, fail);
  }

  /**
   * https://apps.tongshike.cn/apidoc/index.html?url=../yaml/studyrecord.yaml#!/学习记录/get_studyrecord_item_itemid
   * 接口：GET /studyrecord/item/{itemid}
   * 从客户端获取本地保存学习记录
   * sectionId：节Id
   * 成功回调：success = function(jsonString){} jsonString 参考接口文档格式
   **/
  this.sectionStudyRecord = function(sectionId, success) {
    this.execNativeMethod('sectionStudyRecord', [sectionId], success, null);
  }
}

/**
 * 课程播放器插件
 **/
function CoursePlayer() {
  this.method = BasePlugin;
  this.method('UCoursePlayer');
  delete this.method;

  this.course = new CoursePlugin();
  this.studyRecord = new StudyRecordPlugin();

  /**
   * 课程基本信息
   * 成功回调：success = function(jsonString){}   jsonString：{"name":"课程", "needapprove":"0/1"}
   **/
  this.courseInfo = function(success) {
    // $.ajax({
    //   url: CONFIG_API_HOST + "/course/" + 8389 + "/basicinformation",
    //   type: "GET",
    //   contentType: "text/plain",
    //   dataType: "json",
    //   async: false,
    //   data: null,
    //   success: function(result, status, xhr) {
    //     success(result);
    //   },
    //   error: function(xhr, status, error) {
    //     console.log(error);
    //   }
    // });
    // return;

    this.course.info(success);
  }

  /**
   * 获取课程目录(包括课程章节的隐藏情况)
   * 成功回调：success = function(jsonString){}   jsonString参考接口文档数据
   **/
  this.courseDirectory = function(success) {
    // $.ajax({
    //   url: CONFIG_API_HOST + "/course/stu/" + 8389 + "/directory",
    //   type: "GET",
    //   contentType: "text/plain",
    //   dataType: "json",
    //   async: false,
    //   data: null,
    //   success: function(result, status, xhr) {
    //     success(result);
    //   },
    //   error: function(xhr, status, error) {
    //     console.log(error);
    //   }
    // });
    // return;

    this.course.directory(success);
  }

  /**
   * 接口地址：https://apps.tongshike.cn/apidoc/index.html?url=../yaml/coursepage.yaml#!/整个页面/get_wholepage_stu_id
   * 接口：/wholepage/chapter/stu/{chapterid}
   * 获取一章的课程页面详情
   * chapterId：章id
   * 成功回调：success = function(jsonString){}   jsonString 前端提供数据结构
   **/
  this.courseChapterDetail = function(chapterId, success) {
    this.course.chapterDetail(chapterId, success);
  }

  /**
   * 单词释义接口
   * key为要传入的单词：hello
   * 成功回调接口：success = function(content){}  content 为释义内容，内容可能为空
   **/
  this.glossary = function(key, success) {
    this.course.glossary(key, success);
  }

  /**
   * 保存或更新学习记录，离开页面时掉用
   * record：{"chapterId":1, "sectionId":1, "sectionScore":1, "sectionStudyTime":1, "sectionCompletionStatus":1, 
            "pageStudyRecordDTOList":[{"answerTime":1, "complete":1, "pageid":1, "score":1, "studyTime":1, 
                                      "questions":[], "videos":[], "speaks":[]}
                                      ]
            }
   * 成功回调：success = function(recordId){}  recordId记录唯一标示
   * 失败回调：fail = function(failDesc){} failDesc失败描述
   **/
  this.saveOrUpdateRecord = function(record, success, fail) {
    this.studyRecord.saveOrUpdateRecord(record, success, fail);
  }

  /**
   * https://apps.tongshike.cn/apidoc/index.html?url=../yaml/studyrecord.yaml#!/学习记录/get_studyrecord_item_itemid
   * 接口：GET /studyrecord/item/{itemid}
   * 从客户端获取本地保存学习记录
   * sectionId：节Id
   * 成功回调：success = function(jsonString){} jsonString 参考接口文档格式
   **/
  this.sectionStudyRecord = function(sectionId, success) {
    this.studyRecord.sectionStudyRecord(sectionId, success);
  }
}

if (!window.ULplugin) {
  window.ULplugin = {};
}
window.ULplugin.share = new Share();
window.ULplugin.BaseTool = new BaseTool();
window.ULplugin.UApp = new UApp();
window.ULplugin.Umessage = new Umessage();
window.ULplugin.User = new User();
window.ULplugin.recorder = new Recorder();
window.CoursePlayer = new CoursePlayer();
window.FilePugin = new UFilePlugin();

if (window.pluginReady) {
  window.pluginReady();
  if (window.backButtonListener) {
    document.addEventListener("backbutton", window.backButtonListener, false);
  };
}