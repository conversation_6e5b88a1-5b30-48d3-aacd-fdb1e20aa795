window.cordova.define('Cordova/BduLocation', function (cordovaRequire, cordovaExport, cordovaModule) {

  var exec = cordovaRequire('cordova/exec')
  var baidulocation = {
    getCurrentPosition: function (successFn, failureFn) {
      window.getLocationErr = failureFn
      exec(successFn, failureFn, 'BaiduLocation', 'getCurrentPosition', [])
    },
    // 打开定位设置 
    openLoactionInfo:function () {
      exec(null,null,'BaiduLocation','openLoactionInfo',[])
    }
  }
  cordovaModule.exports = baidulocation
})
