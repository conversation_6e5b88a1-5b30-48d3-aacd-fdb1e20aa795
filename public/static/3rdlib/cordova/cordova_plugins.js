window.cordova.define('cordova/plugin_list', function (require, exports, module) {
  module.exports = [
    // {
    //   "id": "cordova-plugin-common.device",
    //   "file": "static/3rdlib/cordova/CordovaPlugin.js",
    //   "clobbers": [
    //     "share"
    //   ]
    // },
    {
      'id': 'cordova-plugin-inappbrowser.inappbrowser',
      'file': 'static/3rdlib/cordova/plugins/cordova-plugin-inappbrowser/www/inappbrowser.js',
      'pluginId': 'cordova-plugin-inappbrowser',
      'clobbers': [
        'cordova.InAppBrowser.open',
        'window.open'
      ]
    },
    {
      'id': 'cordova-plugin-detect-webview-engine.DetectWebViewEngine',
      'file': 'static/3rdlib/cordova/plugins/cordova-plugin-detect-webview-engine/dist/www/detect-webview-engine.js',
      'pluginId': 'cordova-plugin-detect-webview-engine',
      'clobbers': [
        'cordova.plugins.webviewEngine'
      ]
    },
    {
      'id': 'cordova-plugin-wkwebview-engine.ios-wkwebview-exec',
      'file': 'static/3rdlib/cordova/plugins/cordova-plugin-wkwebview-engine/src/www/ios/ios-wkwebview-exec.js',
      'pluginId': 'cordova-plugin-wkwebview-engine',
      'clobbers': [
        'cordova.exec'
      ]
    },
    {
      'id': 'cordova-plugin-wkwebview-engine.ios-wkwebview',
      'file': 'static/3rdlib/cordova/plugins/cordova-plugin-wkwebview-engine/src/www/ios/ios-wkwebview.js',
      'pluginId': 'cordova-plugin-wkwebview-engine',
      'clobbers': [
        'window.WkWebView'
      ]
    },
    {
      'id': 'cordova-plugin-broadcaster',
      'file': 'static/3rdlib/cordova/broadcaster.js',
      'clobbers': [
        'broadcaster'
      ]
    }
  ]
  module.exports.metadata =
  // TOP OF METADATA
    {
      'cordova-plugin-whitelist': '1.3.3',
      'cordova-plugin-inappbrowser': '2.0.2',
      'cordova-plugin-add-swift-support': '1.7.2',
      'cordova-plugin-detect-webview-engine': '1.0.1',
      'cordova-plugin-wkwebview-engine': '1.1.4',
      'cordova-plugin-crosswalk-webview': '2.4.0'
    }
// BOTTOM OF METADATA
})
