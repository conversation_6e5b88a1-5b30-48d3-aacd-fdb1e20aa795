var cordova_timestamp = new Date().getTime() + '';
cordova.define('cordova/plugin_list', function(require, exports, module) {
module.exports = [
{
    "file": "plugins/sharePlugin.js?v=" + cordova_timestamp
  },
  {
    "id": "cordova-plugin-inappbrowser.inappbrowser",
    "file": "plugins/cordova-plugin-inappbrowser/www/inappbrowser.js",
    "pluginId": "cordova-plugin-inappbrowser",
    "clobbers": [
      "cordova.InAppBrowser.open",
      "window.open"
    ]
  },
  {
    "id": "cordova-plugin-detect-webview-engine.DetectWebViewEngine",
    "file": "plugins/cordova-plugin-detect-webview-engine/dist/www/detect-webview-engine.js",
    "pluginId": "cordova-plugin-detect-webview-engine",
    "clobbers": [
      "cordova.plugins.webviewEngine"
    ]
  },
  {
    "id": "cordova-plugin-wkwebview-engine.ios-wkwebview-exec",
    "file": "plugins/cordova-plugin-wkwebview-engine/src/www/ios/ios-wkwebview-exec.js",
    "pluginId": "cordova-plugin-wkwebview-engine",
    "clobbers": [
      "cordova.exec"
    ]
  },
  {
    "id": "cordova-plugin-wkwebview-engine.ios-wkwebview",
    "file": "plugins/cordova-plugin-wkwebview-engine/src/www/ios/ios-wkwebview.js",
    "pluginId": "cordova-plugin-wkwebview-engine",
    "clobbers": [
      "window.WkWebView"
    ]
  }
];
module.exports.metadata = 
// TOP OF METADATA
{
  "cordova-plugin-whitelist": "1.3.3",
  "cordova-plugin-inappbrowser": "2.0.2",
  "cordova-plugin-add-swift-support": "1.7.2",
  "cordova-plugin-detect-webview-engine": "1.0.1",
  "cordova-plugin-wkwebview-engine": "1.1.4",
  "cordova-plugin-crosswalk-webview": "2.4.0"
};
// BOTTOM OF METADATA
});