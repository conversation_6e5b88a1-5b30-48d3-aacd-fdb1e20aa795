window.onload = function () {
  if (navigator.userAgent.indexOf("umoocApp") == -1 && navigator.userAgent.indexOf("harmonyNextAPP") == -1) {
    return;
  }

  // 用来分发多个文件同时下载时的回调
  window.downloadCallbackObj = {
    "filePath": {
      "progressFunction": function () {},
      "successFunction": function () {},
      "errorFunction": function () {}
    }
  };
  window.download = function progressFunction(filePath, progress) {
    // console.log(progress);
    for (var i in window.downloadCallbackObj) {
      if (i == filePath) {
        window.downloadCallbackObj[i].progressFunction(filePath, progress);
        return;
      }
    }
  };
  window.successFunction = function successFunction(filePath, localFilePath) {
    console.log(localFilePath);
    for (var i in window.downloadCallbackObj) {
      if (i == filePath) {
        window.downloadCallbackObj[i].successFunction(filePath, localFilePath);
        delete window.downloadCallbackObj[i];
        return;
      }
    }
  };
  window.errorFunction = function errorFunction(filePath, error) {
    console.log(error);
    for (var i in window.downloadCallbackObj) {
      if (i == filePath) {
        window.downloadCallbackObj[i].errorFunction(filePath, error);
        delete window.downloadCallbackObj[i];
        return;
      }
    }
  };

  // 判断课件一个页面中的一个资源是否已经下载
  window.pageResourceMap = {};
  window.isResourceDownloaded = function (pageId, filePath) {
    if (!pageId || !filePath) {
      return;
    }

    function judgeResourceStatus(downloadObj, filePath) {
      for (var i = 0; i < downloadObj.models.length; i++) {
        var model = downloadObj.models[i];
        if (model.url == filePath) {
          return model.local;
        }
      }
      for (var i = 0; i < downloadObj.tasks.length; i++) {
        var task = downloadObj.tasks[i];
        if (task.url == filePath) {
          return task;
        }
      }

      return false;
    }

    if (window.pageResourceMap[pageId]) {
      return judgeResourceStatus(window.pageResourceMap[pageId], filePath);
    } else {
      window.CoursePlayer.course.initPage(pageId, function (jsonString) {
        var downloadObj = JSON.parse(jsonString);
        window.pageResourceMap[pageId] = downloadObj;

        return judgeResourceStatus(downloadObj, filePath);
      });
    }
  };

  ;
  (function initCordova() {
    var platform = navigator.userAgent.toLowerCase();
    var js = document.scripts;
    var src;
    for (var i = 0; i < js.length; i++) {
      var tJs = js[i];
      if (tJs.src && tJs.src.indexOf("cordova_init.js") != -1) {
        src = tJs.src.substring(0, tJs.src.indexOf("cordova_init.js") - 1);
        break;
      }
    }

    var new_element = document.createElement("script");
    new_element.type = "text/javascript"
    new_element.src = src + '/cordova.js'
    //用来找到plugins.js文件的前置路径
    document.body.appendChild(new_element);

    new_element = document.createElement("script");
    new_element.type = "text/javascript"
    if (/harmonynextapp/.test(platform)) {
      //鸿蒙直接引入sharePlugin.js 
      new_element.src = src + '/plugins/sharePlugin.js'
    } else if (/iphone|ipad|ipod/.test(platform)) {
      new_element.src = src + '/common/cordova-ios.js'
    } else if (/android/.test(platform)) {
      new_element.src = src + '/common/cordova-android.js'
    } 

    document.body.appendChild(new_element);
  }());
}