/*
 * @Descripttion: 
 * @version: 
 * @Author: congsir
 * @Date: 2020-12-18 09:37:15
 * @LastEditors: 丁永康
 * @LastEditTime: 2020-12-30 18:06:22
 */
var exec = null;
var platform = navigator.userAgent.toLowerCase();
if ((/iphone|ipad|ipod/.test(platform))||(/android/.test(platform))){
  exec = window.cordova.require("cordova/exec");
}
function BasePlugin(pluginName) {
  //插件名称对应原生配置插件名称  config plugin=name
  this.pluginName = pluginName;
  //methodName原生方法名
  this.execNativeMethod = function (methodName, args, success, fail) {
    if (navigator.userAgent.indexOf("harmonyNextAPP") > -1) {
      window.HarmonyNext(pluginName, methodName, args, (data) => {
        let res = data
        if(typeof(data)=="string"){
          res = JSON.parse(data)
        }
        if (res.code == 1) {
          // 成功
          success && success(res.result)
        } else if (res.code == 9) {
          // 失败
          fail && fail(res.result)
        }
      })
    } else {
      exec(success, fail, this.pluginName, methodName, args);
    }
  }
}

/**
 *分享
 */
function Share() {
  this.method = BasePlugin;
  this.method("UShare");
  delete this.method;

  /**
   * 分享/复制
   * shareContent jsonString：{"title":"标题", "summary":"内容", "image_url":"图片远程地址", "target_url":"点开分享跳转地址", "type":"1/2/3/4/5"}
   * type：1（复制target_url）、2（分享QQ）、3（分享QQ空间）、4（分享给微信好友）、5（分享给微信朋友圈）
   **/
  this.share = function (shareContent, success, fail) {
    this.execNativeMethod("share", [shareContent], success, fail);
  }
}

// 基础的和原生交互的工具
function BaseTool() {
  this.method = BasePlugin;
  this.method("BaseTool");
  // 方法和UApp中相同
  delete this.method;
  // 返回原生方法（退出当前H5页面）
  this.goToNative = function () {
    var platform = navigator.userAgent.toLowerCase();
    if (/android/.test(platform) || /harmonynextapp/.test(platform)) {
      window.ULplugin.UApp.finish();
    } else {
      this.execNativeMethod("goToNative", [], null, null);
    }
  }
}

function UApp() {
  this.method = BasePlugin;
  this.method('UApp');
  delete this.method;
  // 返回原生方法（退出当前H5页面），同时触发上一个页面内的 window.onResume() 方法
  this.finish = function () {
    var platform = navigator.userAgent.toLowerCase();
    if (/iphone|ipad|ipod/.test(platform)) {
      this.execNativeMethod("goToNative", [], null, null);
    } else if (/android/.test(platform) || /harmonynextapp/.test(platform)) {
      this.execNativeMethod('finish', [], null, null);
    }
  }
  // 同finish，不触发onResume
  this.back = function () {
    this.execNativeMethod('back', [], null, null);
  }
  // 刷新页面
  this.refresh = function () {
    this.execNativeMethod('refresh', [], null, null);
  }
  // 旋转屏幕，并关闭H5页面
  this.goToNative = function () {
    this.execNativeMethod('goToNative', [], null, null);
  }
  // 打开小能
  this.xnService = function () {
    this.execNativeMethod('xnService', [], null, null);
  }
  /**
   * 触发扫码
   * success = function(str){}
   * str: 二维码内容
   **/
  // 扫一下二维码  返回在成功回调中  
  this.scanQRCode = function (isForbid, success, failure) {
    this.execNativeMethod('scanQRCode', [isForbid], null, null)
    window.onQRSuccess = function () {
      typeof success === 'function' && success(...arguments)
    }
    window.onQRFailure = function () {
      typeof failure === 'function' && failure(...arguments)
    }
  }
  // 在新页面打开指定地址
  // external: true/false 是否通过系统浏览器打开
  this.openUrl = function (url, external) {
    this.execNativeMethod('openUrl', [url, external], null, null);
  }
  /**
   * 打开新页面
   * url：地址
   * hideClientNavigation：是否隐藏客户端导航栏。 true:隐藏、false：不隐藏
   **/
  this.openNewWindow = function (url, hideClientNavigation) {
    this.execNativeMethod('openNewWindow', [{
      'url': url
    }, hideClientNavigation], null, null);
  }
  /**
   * 网络状态
   * 成功回调：success = function(state){}  state:-1（未知网络）、0（无网络）、1（Wi-Fi）、2（移动网络）
   **/
  this.networkState = function (success) {
    this.execNativeMethod('networkState', [], success, null);
  }
  /**
   * 打开客户端活动列表
   * args：[班课id, 是否是班课管理员]
   **/
  this.openActivityList = function (args) {
    this.execNativeMethod('openActivityList', args, null, null);
  }
  /**
   * 获取当前环境信息
   * success = function(jsonString){}
   * jsonString：{"platform":"android","language":"zh","appVersion":"1.8.5-DEBUG","appVersionCode":47,"domain":"tongshike.cn","deviceID":"635bb204e1e86699"}
   **/
  this.getConfigInfo = function (success, fail) {
    this.execNativeMethod('getConfigInfo', [], success, fail);
  }
  // 禁止屏幕录制
  this.forbidScreenRecord = function (isForbid) {
    this.execNativeMethod('forbidScreenRecord', [isForbid], null, null)
  }
  // 主动获取屏幕录制状态
  this.getScreenRecordState = function (success, fail) {
    this.execNativeMethod('getScreenRecordState', [], success, fail)
  }
  // ios禁止侧滑返回
  this.forbidBackWithGesture = function (isForbid) {
    this.execNativeMethod('forbidBackWithGesture', [isForbid], null, null)
  }
  // 注入词库
  this.injectThesaurus = function (success, fail) {
    this.execNativeMethod('injectThesaurus', [], success, fail)
  }
  // 写入日志
  this.saveLog = function (content, success, fail) {
    this.execNativeMethod('saveLog', [content], success, fail)
  }
  // 检测多窗口离开
  this.multiWindowMode = function (success, fail) {
    this.execNativeMethod('multiWindowMode', [], success, fail)
  }
  // 人脸识别
  this.faceRecognition = function (isFromExamDetail, success, fail) {
    this.execNativeMethod('faceRecognition', [isFromExamDetail], success, fail)
  }
  /**
   * 高亮屏幕
   * args：[-1是默认亮度，1是高亮]
   **/
  this.setDisplayBrightness = function (args) {
    this.execNativeMethod('setDisplayBrightness', [args], null, null)
  }
}

function Umessage() {
  this.method = BasePlugin;
  this.method('UMessage');
  delete this.method;
  /**
   * 打开聊天页面
   * userName：当前用户用户名（账号）
   * name：聊天目标用户用户名（账号）
   **/
  this.goToChat = function (userName, name) {
    this.execNativeMethod('goToChat', [{
      "userName": userName,
      'name': name
    }], null, null);
  }
}

function User() {
  this.method = BasePlugin;
  this.method('User');
  delete this.method;
  /**
   * 获取当前登陆用户的信息
   * success = function(jsonString){}
   * jsonString：{"userID":1, "loginName":"登陆账号", "name":"姓名", "telphone":"电话", "email":"邮箱", "token":"token", "role":8/9, "sex":true/false, "avatar":"头像url"}
   * role： 8（教师） 9（学生）
   * sex： true（男） false(女)
   **/
  this.getUser = function (success) {
    this.execNativeMethod('getUser', [], (user) => {
      return success(this.uniteUser(user))
    }, null)
  }

  /**
   * 登录接口，明文密码
   **/
  this.login = function (username, password) {
    this.execNativeMethod('login', [username, password], null, null);
  }

  /**
   * 退出登录
   **/
  this.logout = function () {
    this.execNativeMethod('logout', [], null, null);
  }
  /*
  * 异地登录下线
  * */
  this.loggedOnOtherDevices = function (success, fail) {
    this.execNativeMethod('loggedOnOtherDevices', [], success, fail);
  }
  /*
  * 重新登录
  * */
  this.reLogin = function (success, fail) {
    this.execNativeMethod('reLogin', [], success, fail)
  }
  this.uniteUser = function (user) {
    var sex = 0
    switch (user.sex) {
      case '1':
      case 1:
      case true:
        sex = 1 // 男
        break
      case '2':
      case 2:
      case false:
        sex = 0 // 女
        break
    }
    user.userID =  parseInt(user.userID)
    user.role =  parseInt(user.role)
    user.sex =  sex
    return user
  }
}

function ULive() {
  this.method = BasePlugin;
  this.method('ULive');
  delete this.method;

  /**
   * 进入原生直播页面
   * args: [sdkType, liveType,fromClass ]
   * {
      "liveType": 0, // 0：直播  1：回看
      "host": domainUrl,
      "port": 443,
      "server": "global",
      "serial": infos.roomid, // 房间号
      "sdkType": sdkType, // 1：拓课云
      "password": typePassword, // 邀请码
      "userrole": 0,  // 用户角色 非必填 0:教师 2：学生
      "nickname": "" //用户昵称 非必填
    }
   **/
  this.startPlay = function (args, success, fail) {
    this.execNativeMethod('startPlay', args, success, fail);
  }
}

function URecorder() {
  this.method = BasePlugin;
  this.method('URecorder');
  delete this.method;
  
  this.startRecord = function (onComplete, onBeforeStartRecord) {
    // 回调 录音开始
    window.startRecord = () => {
      typeof this.onBeforeStartRecord === 'function' && this.onBeforeStartRecord()
    }
    // 回调 录音结束
    window.stopRecord = () => {
      typeof this.onBeforeStopRecord === 'function' && this.onBeforeStopRecord()
    }
    // 回调 声音
    window.volume = () => {}
    // 回调 录音完成
    window.recordFile = (filePath) => {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      typeof this.onComplete === 'function' && this.onComplete(filePath)
    }
    // 录音前请求权限
    this.onBeforeStartRecord = onBeforeStartRecord
    this.onComplete = onComplete
    return this.execNativeMethod('startRecord', [], null, null)
  }
  /**
   * 停止录音
   **/
  this.stopRecord = function (onBeforeStopRecord) {
    this.onBeforeStopRecord = onBeforeStopRecord
    return this.execNativeMethod('stopRecord', [], null, null)
  }
  /**
   * 检查录音权限
   * success 检查录音权限可以正常使用
   * fail 检查录音权限不可以正常使用。权限被禁止或者录音资源被占用
   **/
  this.checkPermission = function (success, fail) {
    this.execNativeMethod('checkPermission', [], success, fail);
  }
  /**
   * 录音插件版本
   * success = function(versionJsonObj){}    {'version':2}
   **/
  this.version = function (success) {
    this.execNativeMethod('version', [], success, null);
  }
  /**
   * 启动评分录音
   * speechType: read_word（单词）、read_sentence（句子）、read_pred(段落)、exam（开放式练习，多个参考答案）
   * content： 评分内容
   * category: 模块。 1（课程）、2（单词听写）、3（口语测评）
   * 测评录音开始回调：   onBeginOfSpeech = function(dataString){}  dataString 空内容
   * 测评录音音量大小回调： onVolumeChanged = function(volumeString){} volumeString 声音音量。 
   * 测评录音结束回调：   onEndOfSpeech = function(recordFilePath){} recordFilePath 本地录音文件地址。 录音结束。
   * 测评录音结果回调：   onResult = function(jsonArrayString){} jsonArrayString 录音评分结果。 开放式 练习返回结果：[{"score":100}]，其他返回结果：[{"hello": 50, "word": 50}]
   * 测评录音错误回调：   onError = function(errorJsonObject){} errorJsonObject 录音错误反馈信息。{"ErrorCode", intValue, "ErrorDescription":"错误信息"}
   **/
  this.startEvaluator = function (speechType, content, category, speechTime, onBeginOfSpeech, onVolumeChanged, onEndOfSpeech, onResult, onError) {
    window.onBeginOfSpeech = onBeginOfSpeech;
    window.onVolumeChanged = onVolumeChanged;
    window.onEndOfSpeech = onEndOfSpeech;
    window.onResult = onResult;
    window.onError = onError;
    content = content.replace(/ /g, ' ');
    this.execNativeMethod('startEvaluator', [speechType, content, category, speechTime], null, null);
  }
  /**
   * 停止评分录音
   **/
  this.stopEvaluator = function () {
    this.execNativeMethod('stopEvaluator', [], null, null);
  }
}

function U1Recorder() {
  this.method = BasePlugin;
  this.method('U1Recorder');
  delete this.method;
  this.isRecord = function () {
    return this.recording;
  }
  /**
   * 开始录音
   * 开始录音回调：startRecordFunc = function(stringData){}    stringData空内容
   * 停止录音回调：stopRecordFunc = function(stringData){}     stringData空内容
   * 录音文件回调：recordFileFunc = function(filePath){}       filePath录音文件本地地址
   * 录音音量大小回调：volumeFunc = function(volume){}             声音大小
   **/
  this.startRecord = function (startRecordFunc, stopRecordFunc, recordFileFunc, volumeFunc) {
    /**
     * @name: success
     * @msg: 录音评分回调
     * @param {
      *    code:-1 录音出错 0 开始录音 1 正在录音 2 停止录音
      *    result: 返回数据，不同的code值，返回的result不同
      *    message:带国际化出错文本
      * }
      * 实例： 
      *     {code:0, result:"", message:"" }
      *     {code:-1, result:"", message:"麦克风权限未被允许" }
      *     {code:2,//停止录音 result:"文件url", message:"" }
      *     {code:1,//正在录音 result:1,//音量大小 message:"" }
      * @return {*}
      */
    function success(data) {
      data = typeof data === 'object' ? data : JSON.parse(data)
      if(data.code === 0) { // 开始录音回调
        startRecordFunc()
      }
      if (data.code === 1) { // 正在录音回调
        volumeFunc(data.result)
      }
      if (data.code === 2) { // 停止录音回调
        recordFileFunc(data.result)
      }
      if (data.code === -1) { // 录音出错
      }
    }
    this.execNativeMethod('startRecord', [], success, null);
  }
  /**
   * 停止录音
   **/
  this.stopRecord = function () {
    this.execNativeMethod('stopRecord', [], null, null);
  }
  /**
   * 检查录音权限
   * success 检查录音权限可以正常使用
   * fail 检查录音权限不可以正常使用。权限被禁止或者录音资源被占用
   **/
  this.checkPermission = function (success, fail) {
    this.execNativeMethod('checkPermission', [], success, fail);
  }
  /**
   * 录音插件版本
   * success = function(versionJsonObj){}    {'version':2}
   **/
  this.version = function (success) {
    this.execNativeMethod('version', [], success, null);
  }
  /**
   * 启动评分录音
   * speechType: read_word（单词）、read_sentence（句子）、read_pred(段落)、exam（开放式练习，多个参考答案）
   * content： 评分内容
   * category: 模块。 1（课程）、2（单词听写）、3（口语测评）
   * 测评录音开始回调：   onBeginOfSpeech = function(dataString){}  dataString 空内容
   * 测评录音音量大小回调： onVolumeChanged = function(volumeString){} volumeString 声音音量。 
   * 测评录音结果回调：   onResult = function(jsonArrayString){} jsonArrayString 录音评分结果。 开放式 练习返回结果：[{"score":100}]，其他返回结果：[{"hello": 50, "word": 50}]
   * 测评录音错误回调：   onError = function(errorJsonObject){} errorJsonObject 录音错误反馈信息。{"ErrorCode", intValue, "ErrorDescription":"错误信息"}
   * 
   **/
  this.startEvaluator = function (speechType, content, category, speechTime, onBeginOfSpeech, onVolumeChanged, onResult, onError) {
    /**
     * @name: success
     * @msg: 录音评分回调
     * @param {
     *    code:-1 录音出错 0 开始录音 1 正在录音 2 停止录音
     *    result: 返回数据，不同的code值，返回的result不同
     *    message:带国际化出错文本
     * }
     * 实例： 
     *     {code:0, result:"", message:"" }
     *     {code:-1, result:"", message:"麦克风权限未被允许" }
     *     {code:2,//停止录音 result: jsonstr, message:"" }  外层result: {path:"文件url", result:"之前回调的那一坨，onResult里面的"} 内层result: 开放式 练习返回结果：[{"score":100}]，其他返回结果：[{"hello": 50, "word": 50}]
     *     {code:1,//正在录音 result:1,//音量大小 message:"" }
     * 
     *  注意： code为2时： result为jsonstr类型的字符串，内层结构示例：
     *         {  code:2
                  result:{
                    path:"文件url",
                    result: [{"hello": 50, "word": 50}]
                  }
                  message:""
                }
     * 
     * @return {*}
     */
    function success(data) {
      data = typeof data === 'object' ? data : JSON.parse(data)
      if(data.code === 0) { // 开始录音回调
        onBeginOfSpeech()
      }
      if (data.code === 1) { // 正在录音回调
        onVolumeChanged(data.result)
      }
      if (data.code === 2) { // 停止录音回调
        onResult(data.result)
      }
      if (data.code === -1) { // 录音出错
        onError()
        console.log('录音出错')
      }
    }
    content = content.replace(/ /g, ' ');
    this.execNativeMethod('startEvaluator', [speechType, content, category, speechTime], success, null);
  }
  /**
   * 停止评分录音
   **/
  this.stopEvaluator = function () {
    this.execNativeMethod('stopEvaluator', [], null, null);
  }
}

function UFile() {
  this.method = BasePlugin;
  this.method('UFile');
  delete this.method;

  /**
   * @name: delFilePath
   * @msg: 处理文件路径，用于下载、判断文件是否存在、删除已下载文件
   * @param {
   *    filePath: 文件本身下载路径
   * } 
   * @return {*}
   */
  function delFilePath(filePath) {
    if (filePath != null && filePath.indexOf("?name") == -1) {
      try {
        var fileFullName = filePath.split("/").pop();
        var fileName = fileFullName.split(".")[0];
        filePath = filePath + "?name=" + fileName;
      } catch (e) {
        window.console.error(e);
      }
    };
    return filePath
  }
  /**
   * 预览文件
   * filePath：本地文件地址。通过下载方法获取本地文件地址。
   * fileName：文件名。（标题）
   **/
  this.preFile = function (filePath, fileName) {
    this.execNativeMethod('openFileInOtherApp', [filePath, fileName], null, null);
  }

  /**
   * 下载文件
   * 下载进度回调：function download(filePath, progress){}  progress=0-100
   * 下载成功回调：function onDownloadSuccessed(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function onDownloadError(filePath, error){}  error失败描述
   * 下载全部成功回调：function onDownloadAllSuccessed(){}
   **/
  this.download = function (filePath, progressFunction, successFunction, errorFunction) {
    // window.download = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    if (!window.onDownloadSuccessed) {
      window.onDownloadSuccessed = function () {}
    };
    if (!window.onDownloadAllSuccessed) {
      window.onDownloadAllSuccessed = function () {}
    };
    // 多文件同时下载，内容分发
    if (!window.downloadCallbackObj[filePath]) {
      window.downloadCallbackObj[filePath] = {
        "progressFunction": progressFunction,
        "successFunction": successFunction,
        "errorFunction": errorFunction
      }
    }

    if (filePath != null && filePath.indexOf("?name") == -1) {
      try {
        var fileFullName = filePath.split("/").pop();
        var fileName = fileFullName.split(".")[0];
        filePath = filePath + "?name=" + fileName;
      } catch (e) {
        window.console.error(e);
      }
    };
    this.execNativeMethod('download', [filePath], null, null);
  }

  /**
   * @name: downloadV1
   * @msg: 新改版的下载方法:
   * @param {
   *     filePath: 文件本身下载路径
   *     progressFunction： function (filePath, progress) {} filePath：下载文件地址  progress：下载进度。
   *     successFunction: function (filePath, progress, localFilePath) {} filePath：下载文件地址  progress：下载进度 localFilePath： 下载后的本地文件地址
   * }
   * @return {*}
   */ 
  this.downloadV1 = function (filePath, progressFunction, successFunction, errorFunction) {
    filePath = delFilePath(filePath)
    /**
     * @name: success
     * @msg: 客户端success回调
     * @param {
     *    data: {
     *      status:-1 下载失败 0 取消下载 1 下载中  2 下载完成 
     *      progress:进度 0-100
     *      url:前端传过来的下载地址
     *      localFilePath: 下载的文件本地地址
     *      message: 失败信息
     *    }
     * } 
     * @return {*}
     */
    function success(data) {
      console.log('downloadV1 success 回调', data)
      if (data.status === -1) { // 下载失败
        console.log('下载失败')
        errorFunction(data)
      }
      if (data.status === 0) { // 取消下载
        console.log('取消下载')
      }
      if (data.status === 1) { // 下载中
        console.log('下载中')
        progressFunction(data.url, data.progress)
      }
      if (data.status === 2) { // 下载完成
        console.log('下载完成')
        successFunction(data.url, data.progress, data.localFilePath)
      }
    }
    this.execNativeMethod('downloadV1', [filePath], success, null);
  }

  // selectFile 只在android上使用
  /**
   * (文件类型,本次文件选择总大小,本次文件选择总数量,单个文件大小,是否显示隐藏本地上传按钮 0 显示 1隐藏)
   * eg: (“.doc;.docx;.xls;.xlsx;.pptx;.ppt;.pdf;.txt;”,500,1,50,1)
   */
  this.selectFile = function (fileType, allFileSize, fileMaxCount, fizeSize) {
    var arg = [].slice.call(arguments, 0)
    this.execNativeMethod('selectFile', arg)
  },
  // isExist
  // cancelDownload 取消下载 
  /**
   * @name: cancelDownload
   * @msg: 取消下载 
   * @param {
   *  filePath：  标准下载地址url
   * }
   * @return {*}
   */ 
  this.cancelDownload = function(filePath) {
    this.execNativeMethod('cancelDownload', [filePath], null, null);
  }
  /**
   * @name: isExist
   * @msg: 文件是否存在
   * @param {
   *    filePath： 需要文件名称参数，与download保持一致
   * }
   * @return {*}
   */
  this.isExist = function (filePath) {
    filePath = delFilePath(filePath)
    function success(data) {
      alert(typeof data === 'object' ? JSON.stringify(data) : data)
    }
    this.execNativeMethod('cancelDownload', [filePath], success, null);
  }
  // 获取文件大小
  this.fileSize = function (filePath, success) {
    this.execNativeMethod('fileSize', [filePath], success, null)
  }
  
}
function UUploader() {
  this.method = BasePlugin;
  this.method("UUploader");
  delete this.method;
   // 上传进度
  window.onUploadProgress = function (filePath, progress) {
    if (!/^file/.test(filePath)) {
      filePath = 'file://' + filePath
    }
    for (var i in window.uploadCbs) {
      if (i == filePath) {
        var progressCb = window.uploadCbs[i].progressCb
        progressCb && progressCb(filePath, progress);
        return;
      }
    }
  }
  // 取消成功回调
  window.onUploadCancel = function (filePath) {
    if (!/^file/.test(filePath)) {
      filePath = 'file://' + filePath
    }
    for (var i in window.uploadCbs) {
      if (i == filePath) {
        var cancelCb = window.uploadCbs[i].cancelCb
        cancelCb && cancelCb(filePath);
        return;
      }
    }
  }
  // 上传成功回调
  window.onUploadSuccess = function (filePath, remotePath) {
    if (!/^file/.test(filePath)) {
      filePath = 'file://' + filePath
    }
    for (var i in window.uploadCbs) {
      if (i == filePath) {
        var successCb = window.uploadCbs[i].successCb
        successCb(filePath, remotePath);
        return;
      }
    }
  }
  // 上传失败的回调
  window.onUploadFail = function (filePath) {
    if (!/^file/.test(filePath)) {
      filePath = 'file://' + filePath
    }
    for (var i in window.uploadCbs) {
      if (i == filePath) {
        var failCb = window.uploadCbs[i].failCb
        failCb(filePath);
        return;
      }
    }
  }
  // 上传
  this.upload = function (filePath, success, fail, progress) {
    // 上传进度
    window.onUploadProgress = (file, progress) => {
      typeof this.progress === 'function' && this.progress(file, progress)
    }
    // 取消成功回调
    window.onUploadCancel = (file) => {
      typeof this.cancel === 'function' && this.cancel(file)
    }
    // 上传成功回调
    window.onUploadSuccess = (file, remotePath) => {
      typeof this.success === 'function' && this.success(file, remotePath)
    }
    // 上传失败的回调
    window.onUploadFail = (file) => {
      typeof this.fail === 'function' && this.fail(file)
    }

    const str = filePath.replace('file://', '')
    const timestamp = new Date().getTime()
    const fileName = filePath.split('.').pop()
    const random = Math.random(1000).toFixed(3) * 1000
    const random2 = Math.random(1000).toFixed(3) * 1000
    const remoteFile = 'homework' + '/web/' + random + '/' + random2 + '/' + timestamp + '.' + fileName
    this.success = success
    this.progress = progress
    this.fail = fail
    return this.execNativeMethod('upload', [str, remoteFile], success, fail)
  }
  /* 上传文件 option{module:'string',userID:''}*/
  this.uploadFile = function (filePath, progressFn, success, failure, options) {
    var str = filePath.replace("file://", "");
    var timestamp = new Date().getTime();
    var random = parseInt(Math.random() * 1000)
    var module = ''
    if (!options) {
      options = {
        module: 'homework',
        userID: 1111
      }
    }
    module = options.module + '/web/' + options.userID + '/'
    var type = filePath.split('.').pop()
    var fileName = module + timestamp + '/' + random + "." + type;

    window.onUploadProgress = function (file, progress) {
      // alert(progress+'sharePlugin')
      progressFn(file, progress);
    };
    // 取消成功回调
    window.onUploadCancel = function (file) {};
    // 上传成功回调
    window.onUploadSuccess = function (file, qinniu) {
      // qinniu = qinniu.replace('https://leicloud.ulearning.cn/', '');
      // qinniu = qinniu.replace(window.apiConfig.qiniu, '');
      success && success(file, qinniu);
    };
    // 上传失败的回调
    window.onUploadFail = function (file) {
      failure && failure(file);
    };
    this.execNativeMethod("upload", [str, fileName], success, failure);
  }

  // 取消上传
  this.cancel = function (filePath, cancel) {
    const str = filePath.replace('file://', '')
    this.cancel = cancel
    this.execNativeMethod('cancel', [str], null, null)
  }

   /** uploadV1 新版上传接口
   * params: {
   *    filePath: 文件路径
   *    progressFunction：  上传中进度回调函数
   *    successFunction： 上传成功回调函数
   *    errorFunction： 上传失败回调函数
   *    cancelFunction: 上传取消回调函数
   *    options: 可配置参数： module: 模块名称  userID: 用户id
   *    options参数主要用于保存时的文件名称
   * }
   * 
   */
  this.uploadV1 = function (filePath, progressFunction, successFunction, errorFunction, cancelFunction, options) {
    var str = filePath.replace("file://", "");
    var timestamp = new Date().getTime();
    var random = parseInt(Math.random() * 1000)
    var module = ''
    if (!options) {
      options = {
        module: 'homework',
        userID: 1111
      }
    }
    module = options.module + '/web/' + options.userID + '/'
    var type = filePath.split('.').pop()
    var fileName = module + timestamp + '/' + random + "." + type;
    function success(data) {
      console.log('uploadV1 success', data)
      if (data.code === 1) { // 上传中： 进度展示回调
        data.result ? '' : data.result = {}
        progressFunction(data.result.file, data.result.percent)
      }
      if (data.code === 2) { // 上传成功
        data.result ? '' : data.result = {}
        successFunction(data.result.file, data.result.qnFileUrl)
      }
      if(data.code === 0) { // 上传取消
        data.result ? '' : data.result = {}
        cancelFunction(data.result.file, data.result.percent)
      }
      if (data.code === -1) { // 上传失败
        data.result ? '' : data.result = {}
        errorFunction(data.result.file, data.result.status)
      }
    }
    function fail() {
      errorFunction()
    }
    this.execNativeMethod('uploadV1', [str, fileName], success, fail);
  }
  // 取消上传
  this.cancelV1 = function (filePath) {
    var str = ''
    if (filePath) {
      str = filePath.replace("file://", "");
    }
    this.execNativeMethod("cancelV1", [str], null, null);
  }

}


function UMedia() {
  // 已弃用
  this.method = BasePlugin;
  this.method("UMedia");
  delete this.method;

  // 原生播放视频
  this.playVideo = function (url) {
    this.execNativeMethod("playVideo", [url], null, null);
  }
  // 原生播放音频
  this.playAudio = function (audioFile, complete, duration) {
    window.audioCompletion = typeof complete === 'function' ? complete : function () { }
    window.yxyAudioPlaying = typeof duration === 'function' ? duration : function () { }
    // audioFile = audioFile.replace('file://','');
    audioFile = audioFile.replace('cdvfile://umooc_cordova', '')
    // audioFile = audioFile.replace('cdvfile://','')
    // audioFile= '/'+audioFile
    this.execNativeMethod('playAudio', [audioFile], null, null)
  }
  this.formatFilePath = function (filePath) {
    return filePath.replace(/^file:\/\//, '')
  }
  this.play = function (filePath, onEnded) {
    this.onEnded = onEnded
    return this.execNativeMethod('playAudio', [this.formatFilePath(filePath)], null, null)
  }
  this.pause = function (filePath) {
    return this.execNativeMethod('stopAudio', [this.formatFilePath(filePath)], null, null)
  }
  this.stopAudio = function () {
    this.execNativeMethod('stopAudio', [], null, null)
  }
}

function Camera() {
  // 已弃用
  this.method = BasePlugin;
  this.method("Camera");
  delete this.method;

  /**
   * 检查拍照权限
   * success 检查拍照权限可以正常使用
   * fail 检查拍照权限不可以正常使用。权限被禁止或者拍照资源被占用
   **/
  this.checkPermission = function (success, fail) {
    this.execNativeMethod('checkPermission', [], success, fail);
  }
  // takeACandidPhotograph
  // takePicture
  // selectFileFromiOS 从“文件app”里选择文件，目前只支持文档
  // selectMediaFromPhotoAlbum
  // cleanup
  this.getPicture = function (successCallback, errorCallback, options) {
    options = options || {}
    var getValue = function(a, b) {
      return a ? a : b
    }

    var quality = getValue(options.quality, 50)
    // var destinationType = getValue(options.destinationType, Camera.DestinationType.FILE_URI);
    var destinationType = getValue(options.destinationType, 1)
    // var sourceType = getValue(options.sourceType, Camera.PictureSourceType.CAMERA);
    var sourceType = getValue(options.sourceType, 0)
    var targetWidth = getValue(options.targetWidth, -1)
    var targetHeight = getValue(options.targetHeight, -1)
    // var encodingType = getValue(options.encodingType, Camera.EncodingType.JPEG);
    var encodingType = getValue(options.encodingType, 0)
    // var mediaType = getValue(options.mediaType, Camera.MediaType.PICTURE);
    var mediaType = getValue(options.mediaType, 0)
    var allowEdit = !!options.allowEdit
    var correctOrientation = !!options.correctOrientation
    var saveToPhotoAlbum = !!options.saveToPhotoAlbum
    var popoverOptions = getValue(options.popoverOptions, null)
    // var cameraDirection = getValue(options.cameraDirection, Camera.Direction.BACK);
    var cameraDirection = getValue(options.cameraDirection, 0)

    // 一次选择的最大数量
    var macAccount = Math.min(getValue(options.macAccount, 3), 9)
    // 是否回调取消选择文件
    var cancelCallback = !!options.cancelCallback

    var args = [quality, destinationType, sourceType, targetWidth, targetHeight, encodingType,
      mediaType, allowEdit, correctOrientation, saveToPhotoAlbum, popoverOptions, cameraDirection, macAccount, cancelCallback]

      this.execNativeMethod('takePicture', args, successCallback, errorCallback)
  }
  // 打开相机
  this.takeACandidPhotograph = function(success, fail) {
    this.execNativeMethod('takeACandidPhotograph', [], success, fail)
  }
  // 相册选择
  this.selectMediaFromPhotoAlbum = function(success, fail, options) {
    // mQuality = args.optInt(0, 50); //图片质量
    // destType = args.optInt(1, 1);
    // srcType = args.optInt(2, 1); 1拍照，2图库
    // targetWidth = args.optInt(3, -1);照片尺寸
    // targetHeight = args.optInt(4, -1);照片尺寸
    // encodingType = args.optInt(5, 0); 使用的编码类型（未使用） 
    // mediaType = args.optInt(6, 0);  资源类型：0 照片 1视频 2照片+视频
    // allowEdit = args.optBoolean(7, false);图片是否可裁切
    // correctOrientation = args.optBoolean(8, false);图片是否可压缩
    // saveToPhotoAlbum = args.optBoolean(9, false); 拍照图片是否保存到相册
    // popoverOptions	//预览参数 默认空
    // {
    //   x: '',
    //   y: '',
    //   width: '',
    //   height: '',
    //   arrowDir: ''
    // }
    // cameraDirection	// 0 后置摄像头 1前置摄像头，默认1
    // maxCount			// 最大上传数量
    options = options || {}
    var getValue = function(a, b) {
      return a ? a : b
    }
    var mQuality = getValue(options.quality, 50)
    var destType = getValue(options.destType, 1)
    var srcType =  getValue(options.srcType, 2)
    var targetWidth = getValue(options.targetWidth, -1)
    var targetHeight = getValue(options.targetHeight, -1)
    var encodingType = getValue(options.encodingType, 0)
    var mediaType = getValue(options.mediaType, 2)
    var allowEdit = !!options.allowEdit
    var correctOrientation = !!options.correctOrientation
    var saveToPhotoAlbum = !!options.saveToPhotoAlbum
    var popoverOptions = ''
    var cameraDirection = getValue(options.cameraDirection, 1)
    var maxCount = getValue(options.maxCount, 1)
    var args = [mQuality, destType, srcType, targetWidth, targetHeight, encodingType, mediaType, allowEdit, correctOrientation, saveToPhotoAlbum, popoverOptions, cameraDirection, maxCount]
    this.execNativeMethod('selectMediaFromPhotoAlbum', args, success, fail)
  }
  // ios 选择文件selectFileFromiOS
  this.selectFileFromiOS = function(success,fail, options) {
    options = options || {}
    var cancelCallback = !!options.cancelCallback
    this.execNativeMethod('selectFileFromiOS', [cancelCallback], success, fail)
  }
  // 开启视频流摄像头0:关闭，1:开启调试 2 开启正式 传1和2还要传推帧间隔（ms）
  this.cameraStream = function (success, fail, options) {
    var isOpenCamera = options.isOpenCamera || 0
    var intervalTime = options.intervalTime || 0
    this.execNativeMethod('cameraStream', [isOpenCamera, intervalTime], success, fail)
  }  
  // 姿态检测结果 string
  this.posenetResult = function (successCallback, errorCallback, options) {
    var result = options.result || ''
    var status = options.status
    this.execNativeMethod('posenetResult', [result, status], successCallback, errorCallback)
  }
  // 显示姿态检测预览 0:销毁, 1:展示 2隐藏
  this.showCameraPreview = function (success, fail, value) {
    value = value || 0
    this.execNativeMethod('showCameraPreview', [value], success, fail)
  }
  //开始视频流录制 0:结束，1:开启；传0是否上传视频
  this.videoRecord = function (successCallback, errorCallback, args) {
    var examId = args.examId || 0
    var isStart = args.isStart || 0
    var videoName = args.videoName || ''
    var isUpload = args.isUpload || 0
    this.execNativeMethod('videoRecord', [isStart, videoName, isUpload, examId], successCallback, errorCallback)
  }
  //上传异常照片
  this.uploadPosenet = function (successCallback, errorCallback, args) {
    var fileName = args.fileName || ''
    var filePath = args.filePath || ''
    this.execNativeMethod('uploadPosenet', [fileName, filePath], successCallback, errorCallback)
  }
  
  //拿到抓拍本地路径并上传
  this.takeACandidPhotograph = function (successCallback, errorCallback) {
    this.execNativeMethod('takeACandidPhotograph', [], successCallback, errorCallback)
  }

  //检查相机权限
  this.checkPermission = function (successCallback, errorCallback) {
    this.execNativeMethod('checkPermission', [], successCallback, errorCallback)
  }
  // 人脸识别处getClientFile照片
  this.takePicture = function (args, successCallback, errorCallback) {
    this.execNativeMethod('takePicture', [...args], successCallback, errorCallback)
  }
}

function BaiduLocation() {
  // 已弃用
  this.method = BasePlugin;
  this.method("BaiduLocation");
  delete this.method;

  // 获取定位
  this.getCurrentPosition = function (successCallback, errorCallback, url) {
    this.execNativeMethod("getCurrentPosition", [url], successCallback, errorCallback);
  }
  // 打开定位设置
  this.openLoactionInfo = function () {
    this.execNativeMethod("openLoactionInfo", [], null, null);
  }
}

/**
 * 课程插件
 * 提供课程基本信息、课程结构、课程内容
 **/
function CoursePlugin() {
  this.method = BasePlugin;
  this.method('UCourse');
  delete this.method;
  /**
   * 课程基本信息
   * 成功回调：success = function(jsonString){}   jsonString：{"name":"课程", "needapprove":"0/1"}
   **/
  this.info = function (success) {
    this.execNativeMethod('info', [], success, null);
  }

  /**
   * 接口地址：https://www.tongshike.cn/apidoc/index.html?url=../yaml/ua-course.yaml#!/查询一个course的章节页目录结构/get_course_stu_id_directory
   * 接口：/course/stu/{courseid}/directory
   * 获取课程目录(包括课程章节的隐藏情况)
   * 成功回调：success = function(jsonString){}   jsonString参考接口文档数据
   **/
  this.directory = function (success) {
    this.execNativeMethod('directory', [], success, null);
  }

  /**
   * 接口地址：https://apps.tongshike.cn/apidoc/index.html?url=../yaml/coursepage.yaml#!/整个页面/get_wholepage_stu_id
   * 接口：/wholepage/chapter/stu/{chapterid}
   * 获取一章的课程页面详情
   * chapterId：章id
   * 成功回调：success = function(jsonString){}   jsonString 前端提供数据结构
   **/
  this.chapterDetail = function (chapterId, success) {
    this.execNativeMethod('chapterDetail', [chapterId], success, null);
  }

  /**
   * 单词释义接口
   * key为要传入的单词：hello
   * 成功回调接口：success = function(content){}  content 为释义内容，内容可能为空，单词释义表缺省。
   **/
  this.glossary = function (key, success) {
    this.execNativeMethod('glossary', [key], success, null);
  }

  /**
   * 下载文件
   * chapterId： 章id
   * sectionId： 节id
   * pageId：    页面id
   * url：       下载地址
   * 下载进度回调：function progressFunction(filePath, progress){}  progress=0-100
   * 下载成功回调：function successFunction(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function errorFunction(filePath, error){}  error失败描述
   **/
  this.downloadFile = function (chapterId, sectionId, pageId, url, progressFunction, successFunction, errorFunction) {
    // window.progressFunction = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    try {
      delete window.pageResourceMap[pageId];
    } catch (e) {}
    this.execNativeMethod('downloadFile', [chapterId, sectionId, pageId, url], null, null);
  }

  /**
   * 取消下载
   * 一定会成功，所以没有回调
   **/
  this.cancelDownload = function (chapterId, sectionId, pageId, url) {
    this.execNativeMethod('cancelDownloadFile', [chapterId, sectionId, pageId, url], null, null);
  }

  /**
   * 上传文件 待合并
   * file：上传文件本地路径
   * fileName：文件名，也是上传七牛的虚拟资源路径。 record/course/userId/xxxxx.mp3|wav
   * 上传进度回调：funciton progressFunction(file, progress){}   progress=0-100
   * 上传成功回调：function successFuntion(file, url){}  url：资源服务器相对路径 record/course/userId/xxxxx.mp3|wav
   * 上传失败回调：function errorFuntion(file, code, desc){}   code：失败代码 desc：失败描述信息
   **/
  this.uploadFile = function (file, fileName, progressFunction, successFunction, errorFunction) {
    window.uploadFileProgressCallback = progressFunction;
    window.uploadFileErrorCallback = errorFunction;
    window.uploadFileSuccessCallback = successFunction;
    this.execNativeMethod('uploadFile', [file, fileName], null, null);
  }

  /**
   * 初始化客户端 页面（下载任务）
   * pageId： 初始页面id
   * success: function(jsonString){} {"tasks":[{"url":"url", "progress":0.12, "status":0/1/2/3/4}], "models":[{"url":"url", "local":"file:///xxx/xx/xx.doc", "size":123123}]}
   * tasks：下载任务。  models：已下载完成的数据模型
   * status=0/1/2/3/4 0:等待、1:下载中、2:下载成功、3:取消下载、4:下载失败。 progress：下载进度。 local：已缓存本地地址。 size：缓存本地文件大小
   * 下载进度回调：function progressFunction(filePath, progress){}  progress=0-100
   * 下载成功回调：function successFunction(filePath, localFilePath){} localFilePath本地文件地址
   * 下载失败回调：function errorFunction(filePath, error){}  error失败描述
   **/
  this.initPage = function (pageId, success, progressFunction, successFunction, errorFunction) {
    // window.progressFunction = progressFunction;
    // window.onDownloadError = errorFunction;
    // window.onDownloadSuccessed = successFunction;
    this.execNativeMethod('initPage', [pageId], success, null);
  }
}

/**
 * 学习记录插件
 * 增改、查
 **/
function StudyRecordPlugin() {
  this.method = BasePlugin;
  this.method('UStudyRecord');
  delete this.method;

  /**
   * 保存或更新学习记录，离开页面时掉用
   * record：{"chapterId":1, "sectionId":1, "sectionScore":1, "sectionStudyTime":1, "sectionCompletionStatus":1, 
            "pageStudyRecordDTOList":[{"answerTime":1, "complete":1, "pageid":1, "score":1, "studyTime":1, 
                                      "questions":[], "videos":[], "speaks":[]}
                                      ]
            }
   * 成功回调：success = function(recordId){}  recordId记录唯一标示
   * 失败回调：fail = function(failDesc){} failDesc失败描述
   **/
  this.saveOrUpdateRecord = function (record, success, fail) {
    this.execNativeMethod('saveOrUpdateRecord', [record], success, fail);
  }

  /**
   * https://apps.tongshike.cn/apidoc/index.html?url=../yaml/studyrecord.yaml#!/学习记录/get_studyrecord_item_itemid
   * 接口：GET /studyrecord/item/{itemid}
   * 从客户端获取本地保存学习记录
   * sectionId：节Id
   * 成功回调：success = function(jsonString){} jsonString 参考接口文档格式
   **/
  this.sectionStudyRecord = function (sectionId, success) {
    this.execNativeMethod('sectionStudyRecord', [sectionId], success, null);
  }
}

/**
 * 课程播放器插件
 **/
function CoursePlayer() {
  this.method = BasePlugin;
  this.method('UCoursePlayer');
  delete this.method;

  this.course = new CoursePlugin();
  this.studyRecord = new StudyRecordPlugin();

  /**
   * 课程基本信息
   * 成功回调：success = function(jsonString){}   jsonString：{"name":"课程", "needapprove":"0/1"}
   **/
  this.courseInfo = function (success) {
    // $.ajax({
    //   url: CONFIG_API_HOST + "/course/" + 8389 + "/basicinformation",
    //   type: "GET",
    //   contentType: "text/plain",
    //   dataType: "json",
    //   async: false,
    //   data: null,
    //   success: function(result, status, xhr) {
    //     success(result);
    //   },
    //   error: function(xhr, status, error) {
    //     console.log(error);
    //   }
    // });
    // return;

    this.course.info(success);
  }

  /**
   * 获取课程目录(包括课程章节的隐藏情况)
   * 成功回调：success = function(jsonString){}   jsonString参考接口文档数据
   **/
  this.courseDirectory = function (success) {
    // $.ajax({
    //   url: CONFIG_API_HOST + "/course/stu/" + 8389 + "/directory",
    //   type: "GET",
    //   contentType: "text/plain",
    //   dataType: "json",
    //   async: false,
    //   data: null,
    //   success: function(result, status, xhr) {
    //     success(result);
    //   },
    //   error: function(xhr, status, error) {
    //     console.log(error);
    //   }
    // });
    // return;

    this.course.directory(success);
  }

  /**
   * 接口地址：https://apps.tongshike.cn/apidoc/index.html?url=../yaml/coursepage.yaml#!/整个页面/get_wholepage_stu_id
   * 接口：/wholepage/chapter/stu/{chapterid}
   * 获取一章的课程页面详情
   * chapterId：章id
   * 成功回调：success = function(jsonString){}   jsonString 前端提供数据结构
   **/
  this.courseChapterDetail = function (chapterId, success) {
    this.course.chapterDetail(chapterId, success);
  }

  /**
   * 单词释义接口
   * key为要传入的单词：hello
   * 成功回调接口：success = function(content){}  content 为释义内容，内容可能为空
   **/
  this.glossary = function (key, success) {
    this.course.glossary(key, success);
  }

  /**
   * 保存或更新学习记录，离开页面时掉用
   * record：{"chapterId":1, "sectionId":1, "sectionScore":1, "sectionStudyTime":1, "sectionCompletionStatus":1, 
            "pageStudyRecordDTOList":[{"answerTime":1, "complete":1, "pageid":1, "score":1, "studyTime":1, 
                                      "questions":[], "videos":[], "speaks":[]}
                                      ]
            }
   * 成功回调：success = function(recordId){}  recordId记录唯一标示
   * 失败回调：fail = function(failDesc){} failDesc失败描述
   **/
  this.saveOrUpdateRecord = function (record, success, fail) {
    this.studyRecord.saveOrUpdateRecord(record, success, fail);
  }

  /**
   * https://apps.tongshike.cn/apidoc/index.html?url=../yaml/studyrecord.yaml#!/学习记录/get_studyrecord_item_itemid
   * 接口：GET /studyrecord/item/{itemid}
   * 从客户端获取本地保存学习记录
   * sectionId：节Id
   * 成功回调：success = function(jsonString){} jsonString 参考接口文档格式
   **/
  this.sectionStudyRecord = function (sectionId, success) {
    this.studyRecord.sectionStudyRecord(sectionId, success);
  }
}

/**
 *通知
 */
 function FireNativeEvent() {
  this.method = BasePlugin;
  this.method("fireNativeEvent");
  delete this.method;
  this.fireNativeEvent = function (fireContent, success, fail) {
    this.execNativeMethod("fireNativeEvent", [fireContent], success, fail);
  }
}
// 检测是否多窗口
function NativeLifeCycle() {
  this.method = BasePlugin;
  this.method("NativeLifeCycle");
  delete this.method;
  // handel 的含义见文档： https://github.com/pliablepixels/cordova-plugin-multi-window
  this.registerOnStop = function (handle, onStopCallback) {
    this.execNativeMethod('registerOnStop', [handle], onStopCallback, null);
  }
  this.deregisterOnStop = function (handle) {
    this.execNativeMethod('deregisterOnStop', [handle], null, null);

  }
  this.registerOnStart = function (handle, onStartCallback) {
    this.execNativeMethod('registerOnStart', [handle], onStartCallback, null);

  }
  this.deregisterOnStart = function (handle) {
    this.execNativeMethod('deregisterOnStart', [handle], null, null);
  }
  this.deregisterAll = function () {
    this.execNativeMethod('deregisterAll', [], null, null);
  }
}

function Broadcaster() {
  this.method = BasePlugin;
  this.method('broadcaster');
  delete this.method;
  
//   addEventListener监听

// fireNativeEvent发送

// removeEventListener删除

  this.addEventListener = function (eventName, success) {
    this.execNativeMethod('addEventListener', [eventName], success, null);
  }
  this.fireNativeEvent = function (eventName, data) {
    this.execNativeMethod('fireNativeEvent', [eventName, data], null, null);
  }
  this.removeEventListener = function (eventName) {
    this.execNativeMethod('removeEventListener', [eventName], null, null);
  }
}

function UScreenAnalysis () {
  this.method = BasePlugin
  this.method('UScreenAnalysis')
  // this.method('Camera')
  delete this.method
  
  // 申请权限并启动录屏服务
  this.startScreenCaptureService = function (callBack) {
    this.execNativeMethod('startScreenCaptureService', [], null, null)
  },
  // 启动屏幕分析
  // ● remainingTime：考试时长（剩余时长）
  // ● examUserId：答题记录 ID（区分每次答题最多上传多少张图片）
  this.startScreenAnalysis = function (options) {
    var remainingTime = options.remainingTime
    var examUserId = options.examUserId
    this.execNativeMethod('startScreenAnalysis', [remainingTime, examUserId, null, null, 0.9, null, 5], null, null)
    // this.execNativeMethod('startScreenAnalysis', [remainingTime, examUserId, null,null,null,null,null,null,5], null, null)
  },
  // 结束录屏服务并停止屏幕分析
  this.stopScreenCaptureService = function () {
    this.execNativeMethod('stopScreenCaptureService', [], null, null)
  }
  // 分析一次屏幕
  this.analyze = function (options) {
    var id = options.id
    var timestamp = options.timestamp
    var isDelay = options.isDelay ? true: false
    var ignoreCD = options.ignoreCD ? true: false
    this.execNativeMethod('analyze', [id, timestamp, isDelay, ignoreCD], null, null)
  }
  // 设置是否可以分析
  this.setCanAnalyze = function (canAnalyze) {
    this.execNativeMethod('setCanAnalyze', [canAnalyze], null, null)
  }
}


function UKeyboard() {
  this.method = BasePlugin;
  this.method("UKeyboard");
  delete this.method;
  const self = this;
  this.KeyboardInit = function (success, error) {
    this.execNativeMethod("init", [], success, error);
  };
  this.keyboardReset = function (success, error) {
    this.execNativeMethod("reset", [], success, error);
  };
  this.getKeyboardResult = function (data, success, error) {
    this.execNativeMethod("inputKey", data, success, error);
  };
  this.switchInputMode = function (data, success, error) {
    this.execNativeMethod("switchInputMode", data, success, error);
  };
}

if (!window.ULplugin) {
  window.ULplugin = {};
}
if (!window.uploadCbs) {
  window.uploadCbs = {};
}
window.ULplugin.share = new Share();
window.ULplugin.BaseTool = new BaseTool();
window.ULplugin.UApp = new UApp();
window.ULplugin.Umessage = new Umessage();
window.ULplugin.User = new User();
window.ULplugin.URecorder = new URecorder();
window.ULplugin.U1Recorder = new U1Recorder();
window.ULplugin.UUploader = new UUploader();
window.ULplugin.Camera = new Camera();
window.CoursePlayer = new CoursePlayer();
window.ULplugin.UFile = new UFile();
window.ULplugin.ULive = new ULive();
window.ULplugin.fireNativeEvent = new FireNativeEvent();
window.ULplugin.BaiduLocation = new BaiduLocation();
window.ULplugin.NativeLifeCycle = new NativeLifeCycle();
window.ULplugin.UScreenAnalysis = new UScreenAnalysis();
window.broadcaster = new Broadcaster()
window.ULplugin.UKeyboard = new UKeyboard()

window.pluginReady && window.pluginReady()
window.ULplugin.UMedia = new UMedia()