<!DOCTYPE html>
<html lang="zh-CN" dir="ltr">

<head>
  <meta charset="utf-8" />
  <!-- <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/> -->
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
  <title>手机考试</title>
  <link rel="icon" href="data:image/ico;base64,aWNv" />
  <link rel="stylesheet" type="text/css" href="static/css/reset.css" />
  <link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/c/font_288618_l90803ti6d.css" />
  <link rel="stylesheet" type="text/css" href="static/fonts/iconfont.css" />
  <style media="screen" type="text/css">
    /* html, body, #app { height: 100%; margin: 0px; padding: 0px; width: 100%; }
    .d2-home { background-color: #303133; height: 100%; display: flex; flex-direction: column; }
    .d2-home__main { user-select: none; width: 100%; flex-grow: 1; display: flex; justify-content: center; align-items: center; flex-direction: column; }
    .d2-home__loading { height: 32px; width: 32px; margin-bottom: 20px; }
    .d2-home__title { color: #FFF; font-size: 14px; margin-bottom: 10px; }
    .d2-home__sub-title { color: #ABABAB; font-size: 12px; } */

    #appLoading {
      width: 100%;
      height: calc(100vh);
      background: url("static/img/list_bj.png");
    }

    #appLoading img {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 120px;
      height: 120px;
      -webkit-transform: translateY(-50%) translateX(-50%);
      transform: translateY(-50%) translateX(-50%);
    }
  </style>
  <script>
    function getUrlParam(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return decodeURI(r[2]);
      return null;
    }
    if (!getUrlParam('v')) {
      location.search = location.search + "&v=" + new Date().getTime()
      location.replace(location.href)
    }
    (function () {
      var viewportTag = null;
      var metaTags = document.getElementsByTagName("meta");
      for (var i = 0; i < metaTags.length; i++) {
        if (metaTags[i].getAttribute("name") === "viewport") {
          viewportTag = metaTags[i];
          break;
        }
      }
      if (!viewportTag) {
        viewportTag = document.createElement("meta");
        viewportTag.setAttribute("name", "viewport");
      }

      var viewportTagContent =
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no";

      // Detect if iOS device
      if (/(iPhone|iPod|iPad)/i.test(window.navigator.userAgent)) {
        // Get iOS major version
        var iosVersion = parseInt(
          window.navigator.userAgent.match(
            /OS (\d+)_(\d+)_?(\d+)? like Mac OS X/i
          )[1]
        );
        // Detect if device is running >iOS 11
        // iOS 11's UIWebView and WKWebView changes the viewport behaviour to render viewport without the status bar. Need to override with "viewport-fit: cover" to include the status bar.
        if (iosVersion >= 11) {
          viewportTagContent += ", viewport-fit=cover";
        }
      }

      // Update viewport tag attribute
      viewportTag.setAttribute("content", viewportTagContent);
    })();
  </script>
</head>

<body>
  <div id="app">
    <div id="appLoading">
      <img src="static/img/loading.gif" alt="loading" />
    </div>
  </div>
  <script src="./static/cordova/cordova_init.js"></script>
  <script type="text/x-mathjax-config">
    try {
      MathJax.Hub.Config({
        tex2jax: {
          inlineMath: [["$", "$"], ["\\(", "\\)"]],
          displayMath: [["$$", "$$"], ["\\[", "\\]"]],
          processEscapes: true,
          skipTags: ['script', 'noscript', 'style', 'textarea', 'code', 'a']
        },
        TeX: {
          extensions: ["mhchem.js"]
        }
      });
    } catch (e) {}
  </script>
  <!-- <script src="./static/face/pico.min.js"></script> -->
  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <script src="static/monitor/tfjs.js"></script>
  <script src="static/monitor/posenet.js"></script>
  <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
</body>
</html>