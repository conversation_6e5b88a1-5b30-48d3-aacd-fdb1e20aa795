# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 重要规则

- **使用中文回答用户的所有问题和对话**
- 在编写代码注释时使用中文
- 在解释技术概念时使用中文术语
- 遵循项目现有的代码风格和约定

## 项目概述

这是一个 Vue.js 移动端考试应用，支持多种题型包括键盘输入、人脸识别、姿态检测和文件附件。该应用设计用于移动浏览器和 Cordova 集成的原生应用环境。

## 开发命令

```bash
# 开发服务器
npm run serve

# 生产构建
npm run build

# 代码检查
npm run lint

# 打包分析
npm run analyzer
```

## 架构说明

### 核心框架
- **Vue.js 2.5.17** 配合 Vue Router 和 Vuex
- **Cube UI** 移动端组件库
- **Vue i18n** 国际化支持 (支持 zh, en, es, ar, th, id, tw, am, fr)

### 主要依赖
- **Better Scroll** 移动端滚动
- **Vue Touch** 触摸手势
- **Axios** HTTP 请求
- **Crypto-js** 加密解密
- **Swiper** 轮播组件

### 目录结构
- `src/views/` - 主要应用页面 (考试列表、考试详情、考试视图等)
- `src/components/` - 可复用组件 (键盘、验证码、工具箱等)
- `src/common/` - 共享工具、样式和组件
- `src/api/` - API 服务模块
- `src/utils/` - 工具函数
- `src/locales/` - 国际化文件

### 特殊组件
- **键盘组件** (`src/components/Keyboard/`) - 自定义虚拟键盘，支持多种布局
- **人脸识别** (`src/components/face/`) - 使用 canvas 进行面部验证
- **姿态检测** (`src/components/posenet/`) - 使用 TensorFlow.js 进行身体姿态检测
- **文件附件** (`src/components/attach-file/`) - 处理图片、音频和视频上传

### 原生集成
- **Cordova 插件** 位于 `src/lib/cordovaPlugin/`，用于摄像头、定位和文件上传
- **平台检测** 工具位于 `src/common/utils/platform.js`
- **原生生命周期** 管理位于 `src/common/plugins/`

### 状态管理
- **Vuex Store** (`src/store/`) 管理用户状态和考试数据
- **事件总线** (`src/utils/bus.js`) 用于组件间通信

### 样式处理
- **Stylus** 预处理器，共享变量位于 `src/common/styles/`
- **PostCSS RTL** 支持从右到左语言
- **响应式设计** 针对移动设备优化

## 已知问题 (来自 README.md)
1. 图标使用 CSS 类而非 SVG (应迁移到 SVG)
2. 完型填空题与综合题共享类型，通过标题字段区分
3. 一些 DOM 修改在组件挂载后使用全局 CSS 进行
4. 路由配置可通过 `from=` 查询参数改进
5. Paper 数据包含 `checkResult` 字段，可用 `stat` 替代

## 构建配置
- **Webpack 代码分割** 在 vue.config.js 中配置，优化加载
- **Source Maps** 开发环境启用
- **Terser** 生产环境优化
- **打包分析器** 通过 npm 脚本可用