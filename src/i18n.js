import Vue from "vue";
import VueI18n from "vue-i18n";
import { Locale } from "cube-ui";

Vue.use(VueI18n);

function loadLocaleMessages() {
  const locales = require.context(
    "./locales",
    true,
    /[A-Za-z0-9-_,\s]+\.json$/i
  );
  const messages = {};
  locales.keys().forEach(key => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

const messages = loadLocaleMessages();

Vue.prototype.$languages = Object.keys(messages).map(langlage => ({
  label: messages[langlage]._name,
  value: langlage
}));

let langlage = navigator.language || navigator.browserLanguage;
if (navigator.userAgent.indexOf("umoocApp") !== -1) {
  var ua =
    window.navigator.userAgent.indexOf("-language-") != -1 ?
    window.navigator.userAgent :
    "";
  var start = ua.indexOf("-language-") + "-language-".length;
  var end = ua.indexOf(" ", start);
  end = end == -1 ? ua.length : end;

  try {
    langlage =
      ua.substring(start, end) ||
      navigator.language ||
      navigator.browserLanguage;
  } catch (e) {
    lang = navigator.language || navigator.browserLanguage;
  }
}
langlage = langlage.toLowerCase();

let newLocale = "zh";
if (langlage.indexOf("en") > -1) {
  newLocale = "en";
} else if (langlage.indexOf("id") > -1) {
  newLocale = "id";
} else if (
  langlage === "zh_tw" ||
  langlage === "zh-tw" ||
  langlage === "zh-hk" ||
  langlage === "tw"
) {
  newLocale = "tw";
} else if (langlage.indexOf("th") > -1) {
  newLocale = "th";
} else if (langlage.indexOf("es") > -1) {
  newLocale = "es";
} else if (langlage.indexOf("ar") > -1) {
  newLocale = "ar";
} else {
  newLocale = "zh";
}

// 调试用，不要忘记去掉
// newLocale = 'ar'

if (newLocale === 'ar') {
  document.documentElement.setAttribute('dir', 'rtl')
}

const message = messages[newLocale]
Vue.use(Locale)
Locale.use(newLocale, {
  ok: message.confirm,
  cancel: message.cancel
})


export default new VueI18n({
  locale: newLocale,
  messages
});