<template>
  <div id="app" :class="[platform]">
    <router-view/>
    <!-- <keep-alive :include="['exam-view']">
      <router-view />
    </keep-alive> -->
    <!-- <portal-target name="modal"></portal-target> -->
  </div>
</template>
<script>
import { getPlatform } from '@/common/utils'
import { saveError } from '@/api'

export default {
  watch: {
    "$i18n.locale": "i18nHandle"
  },
  data () {
    return {
      platform: ''
    }
  },
  computed: {},
  created () {
    this.platform = 'wrap' + getPlatform().platform
    const element = document.querySelector('#appLoading')
    element && element.remove()
    setTimeout(function () {
      document.getElementById('app').style.display = 'block'
    }, 500)

    this.i18nHandle(this.$i18n.locale);
  },
  methods: {
    saveError (event) {
      const { error } = event
      const data = { ...error, location: window.location.href }
      return saveError(data)
    },
    i18nHandle(val, oldVal) {
      document.querySelector("html").setAttribute("lang", val);
    }
  },
  mounted () {
    window.addEventListener('error', this.saveError)
    // window.addEventListener('unhandledrejection')
  },
  destroyed () {
    window.removeEventListener('error', this.saveError)
  }
}
</script>

<style lang="stylus">
html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video, input
  box-sizing border-box

body, html
  line-height 1.5

.cube-popup
  line-height 1

h1, h2, h3, h4, h5, h6
  font-weight normal

ul, ol, dl
  list-style-type none
  padding 0

a
  color #42b983

img
  max-width 100%
  height auto

// 覆盖cube-ui的Dialog样式
.cube-dialog-btn_highlight
  color $text-color-default !important

.cube-dialog-main
  border-radius 5px !important

.exam-pc-tip
  padding 30px 10px

.noSkipping   
  .cube-popup-content
    .cube-dialog-alert
      .cube-dialog-btns
        margin-top: 24px
        .cube-dialog-btn_highlight
          color: #EE6723 !important
</style>
