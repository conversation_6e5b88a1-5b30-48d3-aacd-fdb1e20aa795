import Vue from 'vue'
import Router from 'vue-router'
// import List from './views/exam-list'
// import Details from './views/exam-details'
// import Face from './views/exam-details/face'
// import Exam from './views/exam-view'
// import Loading from './views/exam-view/loading'
// import ErrorComponent from './views/exam-view/error'

// const lazyLoadView = (AsyncView) => {
//   const AsyncHandler = () => ({
//     component: AsyncView,
//     loading: Loading,
//     error: ErrorComponent,
//     delay: 100
//   })
//   return Promise.resolve({
//     functional: true,
//     render (h) {
//       return h(AsyncHandler)
//     }
//   })
// }

Vue.use(Router)

export default new Router({
  routes: [
    {
      name: 'examList',
      path: '/index',
      component: () => import(/* webpackChunkName: "examList" */ './views/exam-list')
    },
    // 注意: details 是APP 考试活动的入口, 只有APP考试活动才可以使用这个入口
    // 如此可以区分是从哪个入口进的.
    {
      name: 'details',
      path: '/details/:userID/:examID',
      component: () => import(/* webpackChunkName: "details" */ './views/exam-details'),
      meta: {
        title: '详情'
      }
    },
    {
      name: 'report',
      path: '/report/:userID/:examID',
      component: () => import(/* webpackChunkName: "details" */ './views/exam-details'),
      meta: {
        title: '详情'
      }
    },
    {
      name: 'info',
      path: '/info/:userID/:examID',
      component: () => import(/* webpackChunkName: "details" */ './views/exam-details'),
      meta: {
        title: '详情'
      }
    },
    {
      name: 'signin',
      path: '/signin',
      component: () => import(/* webpackChunkName: "signin" */ './views/exam-details/signin'),
      meta: {
        title: '考试签到'
      }
    },
    {
      name: 'scanSignin',
      path: '/scanSignin',
      component: () => import(/* webpackChunkName: "signin" */ './views/exam-details/signin'),
      meta: {
        title: '考试签到'
      }
    },
    {
      name: 'signinByCode',
      path: '/signinByCode',
      component: () => import(/* webpackChunkName: "code" */ './views/exam-details/code'),
      meta: {
        title: '考试验证码'
      }
    },
    {
      name: 'position',
      path: '/position',
      component: () => import(/* webpackChunkName: "position" */ './views/exam-details/position'),
      meta: {
        title: '位置'
      }
    },
    {
      name: 'cameraPose',
      path: '/cameraPose',
      component: () => import(/* webpackChunkName: "camera-pose" */ './views/exam-details/camera-pose'),
      meta: {
        title: '调试摄像头'
      }
    },
    {
      name: 'face',
      path: '/face/:userID/:examID/:type',
      component: () => import(/* webpackChunkName: "face" */ './views/exam-details/face'),
      meta: {
        title: '人脸验证'
      }
    },
    {
      name: 'exam',
      path: '/exam/:userID/:examID',
      // component: () => lazyLoadView(import(/* webpackChunkName: "exam-view" */ './views/exam-view')),
      component: () => import(/* webpackChunkName: "exam" */ './views/exam-view'),
      meta: {
        title: '开始考试'
      }
    },
    {
      name: 'examReport',
      path: '/exam-report/:examUserID/:type:/:userID',
      // component: () => lazyLoadView(import(/* webpackChunkName: "exam-view" */ './views/exam-view')),
      component: () => import(/* webpackChunkName: "exam" */ './views/exam-view'),
      meta: {
        title: '答题记录'
      }
    },
    {
      name: 'file-preview',
      path: '/file-preview',
      // component: () => lazyLoadView(import(/* webpackChunkName: "exam-view" */ './views/exam-view')),
      component: () => import(/* webpackChunkName: "exam" */ './views/exam-view/file-preview'),
      meta: {
        title: '文件预览'
      }
    },
    {
      name: 'login',
      path: '/login',
      component: () => import(/* webpackChunkName: "exam-login" */ './views/exam-login'),
      meta: {
        title: '登录',
        changeTitle: true
      }
    },
    {
      name: 'checkScore',
      path: '/checkScore',
      component: () => import(/* webpackChunkName: "exam-login" */ './views/exam-login'),
      meta: {
        title: '成绩查询',
        changeTitle: true
      }
    },
    {
      name: 'result',
      path: '/result',
      component: () => import(/* webpackChunkName: "exam-login" */ './views/exam-result'),
      meta: {
        title: '成绩查询',
        changeTitle: true
      }
    },
    {
      path: '/',
      redirect: '/index'
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 }
  }
})
