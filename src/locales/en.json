{"_name": "English", "mobileExam": "Mobile Exam", "expand": "Expand", "collapse": "Collapse", "answerRecord": "Answer Record", "highestScore": "Highest Score", "points": "{n} points", "examTime": "<PERSON>am <PERSON>", "examLimitTime": "Time limit for exam", "minutes": "{n} minutes", "answerTime": "Answering Times", "unlimited": "Unlimited times", "unlimited2": "Unlimited", "timesOf": "{current}/{total} attempts", "passingScore": "Passing Score", "lateTime": "Late Time Allowed", "betweenTime": "{startTime} to {endTime}", "clickUpload": "Click to upload  photo for verification", "lookOut": "Attention", "faceTip1": "Verified photo is used for exams using face recognition scanning purpose. Be sure to upload real photos. Teachers will check them. If they find that the photos are not consistent with the students, students will be regarded as cheating, which may result in the cancellation of examination results.", "faceTip2": "Verified photo cannot be modified after uploading", "faceTip3": "，Please confirm it’s applicable before uploading", "authPhoto": "Verified photo", "faceTip4": "Verified photo cannot be modified after uploading. Please be sure to face verification successfully and upload again.", "testFace": "Test if the face verification is successful", "reUpload": "Upload again", "confirmPhoto": "Sure to use this photo", "auditor": "Auditor", "auditTime": "Audit Time", "refuseReason": "Reasons for refusal", "faceNotPass": "Face verification failed.", "retry": "Retry", "faceTip5": "Face verification failed? Click to use verification code to verify", "faceTip6": "Your face identity is verified and the exam will start in 3 seconds.", "authPass": " Verification passed", "enterCode": "Please enter verification code", "enterCodeTip": "Please ask invigilator for the verification code.", "faceAuth": "Face Verification", "codeWrong": "Incorrect verification code!", "waitAuth": "To be audited by teacher", "authPass2": "<PERSON><PERSON> passed", "authNotPass": "Not passed", "uploading": "Uploading", "uploadError": "Failed to upload", "faceTip7": "Face-scanning is not available, please download the new version of APP", "authing": "Verifying", "faceTip8": "Please verify that the photo can be successfully verified in face verification", "examCode": "Exam Verification Code", "faceTip9": "Teacher currently hasn’t set verification codes or the verification code has expired", "examTip": "Examination Note", "examTip1": "The exam is in progress", "examTip2": "Please do not leave the exam interface of ULearn APP", "examTip3": "，Every leaving will be recorded.", "examTip4": "Once the system detects {num} times of leaving exam halfway, it will force submitting your paper.", "examTip6": "If the exam is allowed to be re-sit, your score will be based on the one with the highest score.", "examTip7": "After the beginning of the exam, students need to answer questions carefully. Do not click \"Submit answer\" straight away without answering.", "examTip8": "Exam paper may be divided into several parts. Please check the exam paper carefully and check the answer card before submitting the exam paper so as to avoid missing the questions.", "examTip9": "The top of the exam paper will show the remaining time. When the remaining time is 0, system will submit paperautomatically.", "cancel": "Cancel", "confirm": "Confirm", "startExam": "<PERSON><PERSON>", "startExam2": "<PERSON><PERSON>", "reExam": "Re-sit", "examNotExist": "The exam doesn’t exist", "noAnswerRecord": "No answer record", "scoreFailed": "Scoring failed", "submitFailed": "Submission failed", "examDetailTip1": "We have detected that you have not submitted the exam paper. Now the exam is over, please submit your exam record.", "submitExam": "Submit", "photoNotAuthYet": "Photo has been not verified", "photoNotPassAuth": "Photo verification failed", "authTip1": "You haven't completed the photo verification and you can't enter the exam.", "goAuth": "Go for verification", "plzExitScreen": "Please exit the split-screen", "noExamTip1": "Can’t enter the exam now", "noExamTip2": "You’re late for the exam beyond the prescribed time, you can’t enter the exam.", "noExamTip3": "Exceeding the number of limits", "examTip10": "Do not leave the APP exam interface during the examination.", "examTip11": "During the examination, please do not leave the APP exam interface. Every time you leave, it will be recorded.", "examTip12": "Please pay attention to the remaining time when answering questions. When the time is up, the paper will be submitted automatically.", "examTip13": "Before submitting the paper, please check carefully to avoid missing the questions.", "examTip14": "If the exam is allowed to be answeredseveral times, your score will be based on the one with the highest score.", "examTip15": "The exam adopts face-scanning verification", "examTip16": "Failed to verify?", "examTip17": "Enter the exam by verification code", "examTip18": "In order to prevent cheating for exams, the system will record the equipment used to take the exam, IP address, behavior track of entering/leaving pages, etc., please do not log in the non-exam area.", "error": "Error", "examEnd": "Exam is over", "notJoined": "You did not take the exam.", "finishExam": "Congratulations on completing this exam", "viewScore": "Publish the exam result", "notViewScore": "The results of the exam won’t be published temporarily.", "scoring": "Please wait patiently while the subjective questions are being reviewed.", "notViewAnswer": "No answer records are recorded in this exam.", "refreshSuccess": "Refreshed", "refreshFailed": "Refresh failed", "error2": "Error", "teaNoExam": "Teacher hasn't published the exam yet.", "viewOnPC": "Please check the exam results on the computer.", "enterExamTip": "It is detected that you have not submitted exam paper. You can continue to take the exam and submit it later.", "enterExam": "Enter exam", "pcExam": "Exam through PC", "pcMobileExam": "Phone/PC", "ing": "In process", "notStart": "Not started yet", "end": "Ended", "notJoined2": "Absent from exam", "endSubmit": "The exam is over, submitting.", "lateExam": "Late for the exam, can’t enter the exam", "enterPage": "Enter page", "exception": "Error", "autoSaveFailed": "Failed to save automatically. Check the network", "savingAnswer": "Saving", "exitPage": "Leaving page", "submitSuccess": "Submitted", "reSubmit": "Submit again", "submitTip1": "You have {num} questions have been not answered, sure to submit？", "continueExam": "Continue to answer", "submitting": "Submitting", "unusualLeave": "Unusual leaving", "hintTip1": "System detects that you’ve left the exam.", "hintTip2": "The system detected that you left the exam at <strong> {time} </strong>.", "hintTip3": "You have left the exam {totalCount} times and system will forcibly submit your paper.", "hintTip4": "If you leave the exam screen for more than {totalCount} times, your exam will be submitted involuntarily. You still have {count} times remaining.", "submitSeconds": "Submit ({seconds})", "continueExam2": "Continue to take exam", "exitExamTip": "Reminder for leaving the exam", "correctAnswer": "Correct answer", "userAnswer": "Your answer", "noAnswer": "No answer", "analysis": "Answer Explanation", "answerScore1": "Score: To be graded, out of {num} points", "answerScore2": "Score: {n} out of {num} points ", "teaComment": "Teacher’s comment", "empty": "Empty", "correctAnswer2": "Reference Answer", "answerAnalysis": "Answer Explanation", "useUp": "The number of playback has been used up.", "useTimes": "Played for {count}/{limitCount} times", "playTip": "Note: This recording can only be played {num} times.", "inputAnswer": "Please enter your answer", "totalNum": "{num} questions in total", "question1": "Single choice", "question2": "Multiple choice", "question3": "Fill-in-the-blank", "question4": "Judgment", "question5": "Essay Question", "question6": "Sequencing question", "question7": "Oral subjective question", "question8": "Vocabulary fill-in-the-blank", "question9": "Oral objective question", "question10": "Pull-down selection", "question11": "Comprehensive question", "question12": "<PERSON><PERSON><PERSON> question", "question25": "Calculated Formula", "exitScreen": "Exit split-screen", "enterScreen": "Split-screen mode", "option": "Option", "dragTip": "Long press and drag Options", "example": "Example", "replay": "Replay", "record": "Record", "recordUploadFailed": "The recording upload failed, please check the network!", "recording": "Recording", "stopRecord": "Recording, click to pause", "reRecordTip": "The original recordings will be deleted. Are you sure to record again?", "speakTip1": "Please read the following.", "speakTip2": "within 3 minutes", "speakTip3": "to complete", "recordTip1": "Recording", "exitTip1": "Sure to leave？", "saveAndExit": "Save the draft and leave", "preview": "View", "delete": "Delete", "retryUpload": "Re-upload", "stuFilesMaxTips": "Add 10 files at most", "examSubmitConfirm": "Confirm to submit?", "fromCamera": " Take a photo ", "fromAlbum": " Choose from album ", "examInfo": "Exam <PERSON>", "lateLimit": "Late Start: ", "lateLimitTips": "Not allowed to take the exam after {time} minutes from the start of the exam", "submitLimit": "Submit after: ", "submitLimitTips": "Not allowed to submit within {time} minutes from the beginning of the exam", "securitySettings": "Security Settings", "IPMonitor": "IP Monitoring", "IPMonitorTip": "{ip} is on - Do not switch to another network during the exam!", "deviceMonitor": "Device Monitoring", "deviceMonitorTip": "{device} is on - Do not switch to another device or browser during the exam!", "photoMonitor": "Camera Monitoring", "photoMonitorTip": "{photo} is on - Keep your device's front-facing camera on!", "faceRecognition": "Face Verification", "faceRecognitionTip": "{face} is on - Need to pass face verification to start the exam!", "unOpened": "Not enabled", "IPException": "IP Abnormal", "IPExceptionTips1": "IP monitoring is on. Students are not allowed to switch to another network during the exam. Switching the network may cause the IP address to change.", "IPExceptionTips2": "If you need to change the network under special circumstances, please ask the proctor for the verification code, and use the code to enter the exam.", "deviceException": "Device abnormal", "deviceExceptionTips1": "The device monitoring is on and you are not allowed to change the device or browser during the exam. Please use the device or browser that entered the exam for the first time to answer the questions.", "deviceExceptionTips2": "If you need to change the device/browser under special circumstances, please ask the proctor for the verification code, and use the code to enter the exam.", "commitLimitTimeTips": "You can submit only {time} minutes after the start of the exam!", "examTip20": "Left the exam {count} times", "screenMonitor": "Screen Monitoring", "screenMonitorTips": "Screen monitoring is on. For students who leave the exam {num} times, the exam will be submitted involuntarily.", "recordShowAfterSubmit": "Right after submission", "leaveExamTimesTips": "Left the exam {count} times", "cameraAccess": "The camera access is not enabled. Face verification or camera monitoring is on for this exam -Please enable the camera access to take the exam!", "selectFile": "Select File", "fromAlbum1": "Select from album", "showAfterBatchEnd": "Will be posted after all exam sessions are done", "versionLowerTips": "The current version is too old - Please upgrade before taking the exam!", "monitoring": "Monitoring", "photoCountDown": "Start taking pictures in {n} seconds", "onlyPCWeb": "Only web", "onlyPCExe": "Only lockdown browser", "onlyMobile": "Only mobile device", "unlimitType": "Mobile device/Web/Lockdown browser", "viewOnPCExe": "Please take the exam via ULearning lockdown browser", "swipeTip": "Slide left/right to switch screens", "mobilePermissionTip1": "The camera access is disabled and cannot use this function - Please go to your device's Settings to enable it!", "mobilePermissionTip2": "The microphone access is disabled and cannot use this function - Please go to your device's Settings to enable it!", "screenMonitorTips1": "{screen} is on: If you leave the exam screen for more than {num1} seconds in one time or leave the exam screen for {num} times total, your exam will be submitted involuntarily.", "screenMonitorTips2": "{screen} is on: If you leave the exam screen for more than {num} seconds in one time, your exam will be submitted involuntarily.", "examMonitor": "<PERSON><PERSON> Proctoring", "exitTip2": "{screen} is on: Please do not leave the exam screen before you submit the exam.", "screenInterpolation": "Screen Monitoring", "hintTip5": "Have left the exam screen for more than {s} seconds, your exam has been submitted by the system involuntarily.", "getIt": "Got It!", "netWorkError": "Network error! Please try again after the network connection is restored.", "agreenInstructions": "I have read and agree to the items in the exam instructions", "agreenTip": "Please select", "activeBook": "Activate", "screenRecordTips": "Screen recording orsharing is prohibited in the exam.", "screenRecordTips1": "If screen recording or sharing is on, turn it off before entering the exam.", "inExam": "Exam in progress", "forbidScreenRecord": "Disable screen recording/sharing", "screenRecordTips2": "If you're recording or sharing a screen, close it before returning to the exam.", "textCount": "{n} words", "deviceSupportTip": "Please take the exam via mobile APP", "question9New": "Language - <PERSON>", "noSkippingTips": "<PERSON><PERSON>", "noSkippingMsg": "This exam prohibits skipping questions, please answer in order.", "netWorkErrorNew": "Network error! Please check the network connection.", "netWorkErrorMsg": "The current network is unstable, and continuing to answer questions may cause the answer record to be lost. If the network cannot be restored temporarily, it is recommended to withdraw from the exam with the consent of the teacher, and reschedule the exam later.", "quiteExam": "Exit the Exam", "quiteExamAgain": "Are you sure you want to exit?", "submitPaperErrorMsg": "Network error, failed to submit!<br/>You can try to resubmit. If the network continues to be abnormal and cannot be restored temporarily, it is recommended to withdraw from the exam with the consent of the teacher.", "examType1": "Web", "examType2": "Mobile APP Only", "examType3": "Mobile APP/Web/PC Client", "examType4": "PC Client", "examType5": "Mobile/Web", "examType6": "Web/Client", "examType7": "Mobile APP/PC Client", "examTypeTip": "Please take the exam with {examType}", "cameraMonitor": "Camera Monitoring", "cameraMonitorTip": "{camera} is on - Keep the front-facing camera on all the time through the exam!", "checkCamera": "Check Camera", "cameraMonitorTip1": "If camera monitoring is on, the system will detect and record suspicious behaviors of examinees, such as leaving seat, long-time deviated eye focuses, talking with others.", "cameraMonitorTip2": "Before starting the exam, it is recommended to fix your mobile phone, adjust the camera angle and your sitting posture, and keep it stable during the exam.", "leaveSeat": "Leaving Seat", "manualRefresh": "Load manually", "normal": "Normal", "multiPerson": "Talking with others", "statusNormal": "Normal status", "deviatedSight": "Deviated eye focus", "selectFileTip": "The file type is not supported.", "filePlayTip": "File transcoding failed.", "examSignin": "<PERSON>am Sign-in", "examSigninTip": "You will not be allowed to enter the exam without signing in.", "examSigninTip1": "The current device does not match the one used to sign in, use the device you signed in to enter the exam.", "faceTip10": "Face verification is enabled for this exam, and you can not enter the exam before completing face verification.", "faceTip11": "Can't recognize?", "exam": "Exam", "student": "Examinee", "signin": "Sign in", "signedIn": "Signed in", "signTime": "Sign-in Time:", "signLocation": "Sign-in Location:", "signTip": "The exam is ended and you didn't sign in on time.", "scanSignIn": "Scan Sign-in Code", "scanSignTip": "This exam enables sign-in, you can only enter the exam after you complete the sign-in.", "scanSignTip1": "Please note that after sign-in, you can only use the same device to enter the exam.", "scanTip": "Unrecognized, please scan the correct QR code.", "scanTip1": "You are not on the list for this exam.", "scanTip2": "The QR code has expired, please scan the code again.", "signPosition": "Sign-in Location", "surround": "Nearby locations", "locationWrong": "Inaccurate positioning", "posTitle": "Large positioning deviation", "posMsg1": "Please make sure location services is enabled for ULearning.", "posMsg2": "If you are connecting to a public hotspot, try turning off Wi-Fi, turning on cellular/mobile data, and positioning again.", "getPos": "Positioning", "getPosAgain": "Re-location", "openPos": "Please turn on the phone location permission", "requestFail": "Request failed!", "signinSuccess": "Sign-in succeeded.", "signinFail": "Sign-in failed.", "signinFail1": "Please complete the positioning before signing in.", "signingIn": "Signing", "mobilePluginTips1": "Please allow ULearning to use location service, and then try again.", "mobilePluginTips2": "Please turn on the location service in the settings of the mobile phone system, and allow ULearning to use the location service.", "mobilePluginTips3": "The location service is unstable, unable to get your location information, you can try again later.", "versionLowerTips1": "The current version is too low, please upgrade your APP first.", "forceSubmit": "Early Termination", "forceSubmitTip": "The teacher terminates the exam in advance, it will be submitted automatically after 5 seconds, please do not leave or close the page.", "forceSubmitting": "Submitting...", "forbitScreenShot": "No screenshots", "forbitScreenShotTip": "No screenshots are allowed in this exam.", "forbitScreenShotTip1": "This screenshot has been reported to the teacher, {forbitScreenShotTi2}", "forbitScreenShotTi2": "the system will submit the exam for you automatically when detects the screenshot again.", "forceSumitTitle": "Mandatory Submission", "forbitScreenShotSubmitTip": "No screenshots are allowed for this exam. You have violated the rules again, and the system will submit the exam for you automatically.", "studentExamRoom": "Exam Room: {n}", "uploadFileType": "Upload Attachment", "answerTime1": "Attempts", "cannotScanTip": "Can't scan the code to sign in?", "useCodeSignin": "Sign in with <PERSON><PERSON>"}