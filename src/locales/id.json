{"_name": "Bahasa Indonesia", "mobileExam": "<PERSON><PERSON><PERSON> melalui telepon seluler", "expand": "<PERSON><PERSON><PERSON>", "collapse": "Simpan", "answerRecord": "Catatan jawaban", "highestScore": "<PERSON><PERSON> paling tinggi", "points": "{n} poin", "examTime": "<PERSON><PERSON><PERSON>", "examLimitTime": "<PERSON><PERSON> waktu ujian", "minutes": "{n} menit", "answerTime": "<PERSON><PERSON><PERSON>", "unlimited": "<PERSON><PERSON><PERSON> tidak terbatas", "unlimited2": "Tidak terbatas", "timesOf": "{current}/{total} kali", "passingScore": "<PERSON><PERSON>ar", "lateTime": "<PERSON><PERSON><PERSON> telat", "betweenTime": "{startTime} - {endTime}", "clickUpload": "Klik untuk mengunggah foto verifikasi", "lookOut": "<PERSON><PERSON><PERSON>", "faceTip1": "Foto verifikasi digunakan untuk scan wajah pada saat ujian, mohon untuk mengunggah foto asli. <PERSON><PERSON><PERSON> foto diung<PERSON>h, dosen dapat melaksanakan pemeriks<PERSON>, apabila foto tidak sesuai, maka akan dianggap sebagai kecurangan, dan mungkin akan bisa menyebabkan pembatalan pada nilai ujian. ", "faceTip2": "Foto verifikasi tidak dapat diubah setelah di<PERSON>gah", "faceTip3": ", silahkan pastikan terlebih dahulu sebelum mengunggahnya.", "authPhoto": "Foto verifikasi", "faceTip4": "Foto verifikasi tidak dapat diubah setelah di<PERSON>h, silahkan tes scan wajah terlebih dahulu sebelum mengunggahnya.", "testFace": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> scan wajah ber<PERSON>il", "reUpload": "<PERSON><PERSON><PERSON> ul<PERSON>", "confirmPhoto": "Konfirmasi untuk menggunakan foto ini", "auditor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auditTime": "<PERSON><PERSON><PERSON> p<PERSON>", "refuseReason": "<PERSON><PERSON><PERSON>", "faceNotPass": "Verifikasi wajah tidak diterima.", "retry": "<PERSON><PERSON> ul<PERSON>", "faceTip5": "Verifikasi wajah gagal? Klik dan gunakan nomor verifikasi untuk melakukan verifikasi", "faceTip6": "<PERSON><PERSON><PERSON><PERSON><PERSON> telah <PERSON>, si<PERSON><PERSON> tunggu 3 menit dan anda dapat masuk ujian", "authPass": "Verifikasi telah diterima", "enterCode": "<PERSON>lah<PERSON> masukkan nomor verifikasi", "enterCodeTip": "Silahkan minta nomor verifikasi dari dosen pengawas", "faceAuth": "<PERSON><PERSON><PERSON><PERSON><PERSON> wajah", "codeWrong": "Nomor verifikasi salah", "waitAuth": "<PERSON><PERSON><PERSON> pem<PERSON><PERSON><PERSON> dari dosen", "authPass2": "<PERSON><PERSON>", "authNotPass": "Belum di<PERSON>i", "uploading": "Sedang mengunggah", "uploadError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han pada pengun<PERSON>ahan", "faceTip7": "Untuk saat ini tidak mendukung verifikasi wajah, silahkan unduh aplikasi versi terbaru", "authing": "Sedang dalam proses verifikasi", "faceTip8": "Silahkan verifikasi foto ini terlebih dahulu dan pastikan scan wajah ber<PERSON>il", "examCode": "Nomor verifi<PERSON>i u<PERSON>an", "faceTip9": "Saat ini dosen tidak mengatur nomor verifikasi atau nomor verifikasi telah kadaluarsa", "examTip": "Pemberitahuan mengenai ujian", "examTip1": "<PERSON><PERSON><PERSON> dalam proses ujian", "examTip2": "Mohon untuk tidak pergi dari ujian aplikasi ULearn", "examTip3": ", setiap pergi dari ujian akan dicatat.", "examTip4": "Sistem menemukan bahwa anda telah mening<PERSON>kan ujian di tengah ujian sebanyak {num} kali, kertas ujian akan secara otomatis dikumpulkan", "examTip6": "<PERSON><PERSON><PERSON>a diperbolehkan untuk ujian ulang, maka nilai anda yang paling tinggi yang akan menjadi acuannya. ", "examTip7": "<PERSON><PERSON><PERSON> u<PERSON>, para murid harus membaca soal dengan teliti, mohon untuk tidak klik “Kumpul kertas ujian” apabila belum menja<PERSON> per<PERSON>. ", "examTip8": "Kertas ujian mungkin dibagi menjadi banyak bagian, silahkan periksa kertas ujian dengan teliti, sebelum mengumpulkan kertas ujian, silahkan periksa kartu jawaban anda untuk menghindari terjadinya jawaban yang kosong.", "examTip9": "Bagian atas kertas ujian akan menampilkan waktu yang tersisa untuk mengumpulkan kertas ujian, apa<PERSON>a sisa waktu adalah 0, maka sistem akan secara otomatis mengumpulkan kertas jawaban.", "cancel": "Batalkan", "confirm": "OK", "startExam": "<PERSON><PERSON><PERSON>", "startExam2": "<PERSON><PERSON><PERSON>", "reExam": "<PERSON><PERSON><PERSON>", "examNotExist": "<PERSON><PERSON><PERSON> tidak ada", "noAnswerRecord": "Tidak ada catatan jawaban", "scoreFailed": "Gagal member <PERSON><PERSON>", "submitFailed": "<PERSON><PERSON> kertas ujian", "examDetailTip1": "<PERSON><PERSON><PERSON><PERSON> bahwa anda memiliki ujian yang belum dikump<PERSON>kan, saat ini ujian telah se<PERSON>, mohon untuk mengumpulkan catatan ujian.", "submitExam": "<PERSON><PERSON><PERSON> kertas u<PERSON>an", "photoNotAuthYet": "Foto verifikasi belum diperiksa", "photoNotPassAuth": "Pemeriksaan foto verifikasi belum disetujui", "authTip1": "Anda belum menyelesaikan verifikasi foto wajah, maka dari itu tidak dapat masuk ujian", "goAuth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plzExitScreen": "<PERSON><PERSON><PERSON> keluar dari layar terpisah", "noExamTip1": "Waktu saat ini tidak dapat ikut serta dalam ujian", "noExamTip2": "Waktu telat telah melebihi waktu yang ditentukan, se<PERSON>ga tidak dapat masuk ujian", "noExamTip3": "<PERSON><PERSON><PERSON> dari jumlah yang di<PERSON>asi", "examTip10": "<PERSON><PERSON><PERSON>g be<PERSON>, mohon untuk tidak meninggalkan halaman ujian aplikasi", "examTip11": " <PERSON><PERSON><PERSON> sedang be<PERSON>, mohon untuk tidak meninggalkan halaman ujian ap<PERSON>, karena setiap meninggalkan halaman ujian, akan dicatat.", "examTip12": "<PERSON><PERSON> untuk memperhatikan sisa waktu pada saat menjawab per<PERSON>, set<PERSON>h waktu habis, kertas ujian akan secara otomatis dikumpulkan.", "examTip13": "Sebelum men<PERSON><PERSON>an kertas u<PERSON>, mohon periksa dengan teliti apakah sudah dija<PERSON> se<PERSON>, untuk mengh<PERSON><PERSON> adanya jawaban yang kosong.", "examTip14": "Apabila pada saat ujian diperbolehkan untuk menjawab pertanyaan banyak kali, nilai yang paling tinggi akan menjadi acuannya.", "examTip15": "<PERSON><PERSON>an kali ini telah mengaktifkan verifikasi wajah", "examTip16": "Tidak bisa scan wajah untuk verifikasi?", "examTip17": "Gunakan nomor verifikasi untuk masuk ujian", "examTip18": "Untuk mencegah kecurangan dalam ujian, sistem akan merekam peralatan yang digunakan untuk mengikuti ujian, alamat IP, jalur perilaku memasuki / meninggalkan halaman, dll. <PERSON><PERSON> jangan login di area non-ujian.", "error": "<PERSON><PERSON><PERSON><PERSON>", "examEnd": "<PERSON><PERSON><PERSON> telah se<PERSON>", "notJoined": "Anda belum ikut serta dalam ujian kali ini", "finishExam": "Selamat telah menyelesaikan ujian kali ini", "viewScore": "<PERSON><PERSON><PERSON> u<PERSON>", "notViewScore": "<PERSON><PERSON><PERSON> kali ini tidak mempublikasi nilai ujian", "scoring": "Sedang memeriksa pertanyaan subjektif, silahkan menunggu", "notViewAnswer": "<PERSON><PERSON>an kali ini tidak menampilkan catatan jawaban", "refreshSuccess": "<PERSON><PERSON><PERSON><PERSON> refresh", "refreshFailed": "Gagal refresh", "error2": "<PERSON><PERSON><PERSON><PERSON>", "teaNoExam": "<PERSON><PERSON> belum merilis ujian", "viewOnPC": "<PERSON><PERSON><PERSON> kerjakan ujian di komputer dan lihat nilai ujian", "enterExamTip": "<PERSON><PERSON><PERSON><PERSON> bahwa anda memiliki ujian yang belum dikumpul, anda bisa terus ikut serta dalam ujian dan mengumpulkan kertas ujian.", "enterExam": "<PERSON><PERSON><PERSON>", "pcExam": "<PERSON><PERSON><PERSON> me<PERSON>ui komputer", "pcMobileExam": "Telepon seluler/komputer", "ing": "Sedang berl<PERSON>g", "notStart": "Belum dimulai", "end": "Telah selesai", "notJoined2": "Tidak ikut ujian", "endSubmit": "<PERSON><PERSON><PERSON> u<PERSON>, <PERSON>g men<PERSON><PERSON><PERSON>an kertas ujian", "lateExam": "Telat mengikuti ujian, tidak diperbolehkan untuk ikut ujian", "enterPage": "<PERSON><PERSON><PERSON>", "exception": "<PERSON><PERSON><PERSON><PERSON>", "autoSaveFailed": "<PERSON><PERSON> menyi<PERSON> secara otomati<PERSON>, silah<PERSON> periksa jaringan internet", "savingAnswer": "Sedang menyimpan jawaban", "exitPage": "Tinggalkan halaman", "submitSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> kertas u<PERSON>an", "reSubmit": "Ku<PERSON><PERSON> ulang kertas ujian", "submitTip1": "Anda memiliki {num} soal yang belum dijawab, apa<PERSON>h anda yakin untuk mengumpulkannya?", "continueExam": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Sedang mengumpulkan kertas ujian", "unusualLeave": "Meninggalkan ujian dalam kondisi yang tidak biasa", "hintTip1": "Sistem menemukan bahwa anda telah mening<PERSON> ujian.", "hintTip2": "Sistem menemukan bahwa anda telah mening<PERSON>kan ujian pada <strong>{time}</strong>.", "hintTip3": "<PERSON>a telah mening<PERSON> ujian sebanyak {totalCount} kali, maka kertas ujian anda akan secara otomatis dikumpulkan.", "hintTip4": "{totalCount} kali men<PERSON> ujian, kertas ujian akan secara otomatis dikump<PERSON>, anda masih tersisa {count} kali kesempatan.", "submitSeconds": "<PERSON><PERSON><PERSON><PERSON><PERSON> kertas ujian ({seconds})", "continueExam2": "<PERSON><PERSON><PERSON>", "exitExamTip": "<PERSON><PERSON><PERSON> men<PERSON> ujian", "correctAnswer": "Jawaban tepat", "userAnswer": "<PERSON><PERSON><PERSON> anda", "noAnswer": "<PERSON><PERSON> men<PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "answerScore1": "Skor: untuk diperiksa {num} poin", "answerScore2": "Skor: {n} poin, skor penuh {num} poin", "teaComment": "<PERSON><PERSON> dosen", "empty": "Tidak ada", "correctAnswer2": "<PERSON><PERSON><PERSON>i", "answerAnalysis": "<PERSON><PERSON><PERSON>", "useUp": "<PERSON><PERSON><PERSON> tampilan soal ini telah habis digunakan", "useTimes": "<PERSON>ah dimainkan {count}/{limitCount} kali", "playTip": "Perhatian: <PERSON><PERSON><PERSON> soal ini hanya dapat dimainkan {num} kali", "inputAnswer": "<PERSON><PERSON><PERSON> masukkan jawaban anda", "totalNum": "Total {num} soal", "question1": "<PERSON><PERSON> pilihan ganda", "question2": "Soal pilihan dengan beberapa jawaban", "question3": "Soal isian", "question4": "<PERSON><PERSON> benar atau salah", "question5": "<PERSON><PERSON> men<PERSON> singkat", "question6": "Soal pengurutan", "question7": "Pertanyaan subjektif per<PERSON>", "question8": "Soal memilih kata", "question9": "Pertanyaan objektif <PERSON>", "question10": "Dropdown", "question11": "<PERSON><PERSON> kom<PERSON><PERSON><PERSON><PERSON><PERSON>", "question12": "Soal mengisi kata", "question25": "<PERSON><PERSON><PERSON>", "exitScreen": "<PERSON><PERSON><PERSON> dari layar terpisah", "enterScreen": "Mode layar terpisah", "option": "<PERSON><PERSON><PERSON>", "dragTip": "<PERSON>kan dan tarik pilihan", "example": "<PERSON><PERSON><PERSON>", "replay": "<PERSON>ar kembali", "record": "<PERSON><PERSON><PERSON>", "recordUploadFailed": "<PERSON><PERSON> re<PERSON>, silah<PERSON> periksa jaringan internet", "recording": "Sedang merekam", "stopRecord": "Sedang <PERSON>, klik untuk berhenti", "reRecordTip": "<PERSON><PERSON><PERSON> direkam ulang, reka<PERSON> awal akan te<PERSON>, apakah yakin untuk rekam ulang?", "speakTip1": "<PERSON>lah<PERSON> baca isi konten di bawah ini,", "speakTip2": "<PERSON>am waktu 3 menit", "speakTip3": "<PERSON><PERSON><PERSON>", "recordTip1": "Sedang merekam... Apakah ingin mengganti tema", "exitTip1": "<PERSON><PERSON><PERSON><PERSON> anda yakin untuk keluar?", "saveAndExit": "Simpan draft dan keluar", "preview": "Lihat", "delete": "Hapus", "retryUpload": "<PERSON><PERSON><PERSON> ul<PERSON>", "stuFilesMaxTips": "Tambah maksimum 10 dokumen", "examSubmitConfirm": "Yakin untuk kumpul?", "fromCamera": "Ambil foto", "fromAlbum": "<PERSON><PERSON><PERSON> dari album", "examInfo": "Informasi ujian", "lateLimit": "Batas waktu terlambat:", "lateLimitTips": "Entri tidak diperbolehkan setelah {time} beberapa menit setelah ujian dimulai", "submitLimit": "Batas waktu untuk pengiriman:", "submitLimitTips": "Makalah tidak diperbolehkan dalam waktu {time} menit dari dimulainya ujian", "securitySettings": "<PERSON><PERSON><PERSON><PERSON> kea<PERSON>n", "IPMonitor": "Pemantauan IP", "IPMonitorTip": "{ip} <PERSON><PERSON><PERSON><PERSON><PERSON>, jangan beralih jaringan di tengah", "deviceMonitor": "<PERSON><PERSON><PERSON>", "deviceMonitorTip": "{device} di<PERSON><PERSON><PERSON><PERSON>, jangan mengubah perangkat atau browser di tengah", "photoMonitor": "<PERSON><PERSON><PERSON>", "photoMonitorTip": "{photo} <PERSON><PERSON><PERSON><PERSON>, harap tetap menyala di kamera depan", "faceRecognition": "<PERSON><PERSON><PERSON><PERSON><PERSON> wajah", "faceRecognitionTip": "{face} diaktifkan dan verifikasi wajah diperlukan untuk mengikuti ujian", "unOpened": "Belum diaktifkan", "IPException": "IP bermasalah", "IPExceptionTips1": "<PERSON>jian kali ini telah mengaktifkan pemantauan IP, tidak boleh mengganti alamat IP saat ujian berlangsung. Penggantian jaringan internet mungkin akan menyebabkan perubahan pada alamat IP.", "IPExceptionTips2": "<PERSON><PERSON> keadaan khusus perlu mengubah jaringan, silakan minta proctor untuk kode verifikasi, gunakan kode verifikasi untuk mengikuti ujian.", "deviceException": "<PERSON><PERSON><PERSON> be<PERSON>", "deviceExceptionTips1": "Pemantauan perangkat diaktifkan untuk ujian ini, dan penggantian perangkat atau browser tidak diperbolehkan di tengah, silakan gunakan perangkat atau browser yang memasuki ujian untuk pertama kalinya untuk menjawab pertanyaan.", "deviceExceptionTips2": "<PERSON><PERSON> te<PERSON><PERSON><PERSON> k<PERSON>, <PERSON><PERSON> per<PERSON> men<PERSON> peralatan, silakan minta proctor untuk kode verifikasi, gunakan kode verifikasi untuk mengikuti ujian.", "commitLimitTimeTips": "<PERSON><PERSON><PERSON> dimulai {time} be<PERSON><PERSON> menit sebelum Anda dapat menyerahkan kertas Anda!", "examTip20": "<PERSON> tengah ujian {count} kali", "screenMonitor": "<PERSON><PERSON><PERSON><PERSON> layar", "screenMonitorTips": "{screen} te<PERSON>, dan men<PERSON> {num} kali di tengah akan memaksa rollover", "recordShowAfterSubmit": "Se<PERSON>a tampilkan set<PERSON>h kumpul", "leaveExamTimesTips": "<PERSON> tengah ujian {count} kali", "cameraAccess": "<PERSON>an mengaktifkan izin kamera, ujian ini mengaktifkan pengenalan wajah atau menang<PERSON>p pemantauan, silakan aktifkan izin kamera untuk berpartisipasi dalam ujian!", "selectFile": "Pilih dokumen", "fromAlbum1": "<PERSON><PERSON><PERSON> dari album", "showAfterBatchEnd": "Pemeriksaan akan diumumkan pada akhir pemeriksaan.", "versionLowerTips": "Versi saat ini terlalu rendah, silakan tingkatkan dan kemudian ikuti ujian!", "monitoring": "<PERSON><PERSON><PERSON>", "photoCountDown": "<PERSON><PERSON> men<PERSON> gambar setelah {n} detik", "onlyPCWeb": "Hanya web komputer", "onlyPCExe": "Akun pelanggan komputer", "onlyMobile": "<PERSON><PERSON> telepon", "unlimitType": "Seluler/web/klien", "viewOnPCExe": "<PERSON><PERSON><PERSON> gunakan ujian <PERSON>n U<PERSON>an Tinggi AS di komputer Anda", "swipeTip": "Gesek ke kiri dan kanan untuk beralih halaman", "mobilePermissionTip1": "Anda telah menonaktifkan izin kamera dan tidak dapat menggunakan fitur ini, silakan buka Pengaturan ponsel Anda untuk mengotorisasi", "mobilePermissionTip2": "Anda telah menonaktifkan izin mikrofon dan tidak dapat menggunakan fungsi ini, silakan buka [Pengaturan] telepon untuk mengotorisasi", "screenMonitorTips1": "{screen} telah di<PERSON>, meninggal<PERSON> ujian untuk satu kali lebih dari {num1} detik, atau meninggalkan {num} kali secara total akan memaksa kertas untuk diserahkan", "screenMonitorTips2": "{screen} te<PERSON>, dan mening<PERSON> setengah jalan selama lebih dari {num} detik akan memaksa rollover", "examMonitor": "<PERSON><PERSON><PERSON> u<PERSON>", "exitTip2": "<PERSON><PERSON><PERSON> ini telah di<PERSON> {screen}, tolong jangan pergi di tengah jalan, <PERSON>a hanya dapat keluar setelah menyerahkan kertas Anda.", "screenInterpolation": "<PERSON><PERSON><PERSON><PERSON> layar", "hintTip5": "Sistem telah memaksa menyerahkan kertas setelah pergi selama lebih dari {s} detik.", "getIt": "<PERSON><PERSON> tahu", "netWorkError": "<PERSON><PERSON><PERSON> tidak normal dan <PERSON>i telah gagal, silakan coba lagi setelah koneksi jaringan dipulihkan.", "agreenInstructions": "<PERSON>a telah membaca dan setuju dengan hal-hal dalam catatan ujian", "agreenTip": "<PERSON><PERSON><PERSON> per<PERSON> dulu", "activeBook": "Aktivasi", "screenRecordTips": "<PERSON><PERSON><PERSON> layar atau berbagi layar dilarang untuk ujian.", "screenRecordTips1": "<PERSON><PERSON> atau berbagi layar di<PERSON>, matikan sebelum memasuki u<PERSON>.", "inExam": "<PERSON><PERSON><PERSON> sedang berlang<PERSON>g", "forbidScreenRecord": "Nonak<PERSON><PERSON><PERSON> perekaman layar / berb<PERSON> layar", "screenRecordTips2": "Jika Anda merekam atau berbagi layar, tutup sebelum kembali ke ujian untuk melanjutkan menjawab per<PERSON>aan", "textCount": "{n} kata", "deviceSupportTip": "<PERSON><PERSON><PERSON> ikuti ujian di ponsel <PERSON>", "question9New": "Membaca", "noSkippingTips": "Tips", "noSkippingMsg": "Melewatkan pertanyaan dilarang untuk ujian ini, jadi harap jawab secara be<PERSON>.", "netWorkErrorNew": "<PERSON><PERSON><PERSON>, silakan periksa koneksi jaringan", "netWorkErrorMsg": "<PERSON><PERSON>an saat ini tidak stabil, dan terus menjawab pertanyaan dapat menyebabkan catatan jawaban hilang. Jika jaringan tidak dapat dipulihkan sementara, disarankan untuk mundur dari ujian setelah mendapatkan persetujuan dari guru, dan kemudian mengatur pengambilan ulang di kemudian hari.", "quiteExam": "<PERSON><PERSON><PERSON> dari u<PERSON>an", "quiteExamAgain": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin berhenti sekarang?", "submitPaperErrorMsg": "<PERSON><PERSON><PERSON>, gagal mengirimkan file!<br/><PERSON>a dapat mencoba mengirim ulang. Jika jaringan terus menjadi tidak normal dan tidak dapat dipulihkan sementara, disarankan untuk mundur dari ujian dengan persetujuan guru.", "examType1": "Web", "examType2": "<PERSON><PERSON> telepon", "examType3": "Seluler/web/klien", "examType4": "<PERSON><PERSON><PERSON>", "examType5": "Seluler/Web", "examType6": "Web/klien", "examType7": "Se<PERSON><PERSON>/klien", "examTypeTip": "<PERSON><PERSON><PERSON> ikuti ujian den<PERSON> {examType}", "cameraMonitor": "Pemantauan kamera", "cameraMonitorTip": "{camera} <PERSON><PERSON><PERSON><PERSON><PERSON>, si<PERSON>an n<PERSON>akan dan mengh<PERSON><PERSON> kamera", "checkCamera": "Debug kamera", "cameraMonitorTip1": "Jika pemantauan kamera dihidupkan untuk ujian, sistem akan mendeteksi dan merekam perilaku abnormal seperti kandidat yang meninggalkan tempat duduk mereka, penyimpangan jangka panjang mata mereka, dan menyentuh kepala dan telinga mereka.", "cameraMonitorTip2": "Sebelum me<PERSON><PERSON>, disar<PERSON><PERSON> untuk memperbaiki ponsel, menyesuaikan sudut kamera dan postur duduk, dan menjaga stabilitas selama proses penja<PERSON>.", "leaveSeat": "Meninggalkan kursi", "manualRefresh": "Pemuatan manual", "normal": "Normal", "multiPerson": "Bisik", "statusNormal": "Status normal", "deviatedSight": "Penyimpanan pandangan", "selectFileTip": "Jenis file ini tidak didukung", "filePlayTip": "Transcoding file gagal", "examSignin": "<PERSON><PERSON><PERSON> ma<PERSON>k", "examSigninTip": "<PERSON>jian tidak check-in dan tidak dapat dimasukkan.", "examSigninTip1": "Jika perangkat saat ini tidak cocok dengan yang digunakan untuk check-in, gunakan perangkat yang Anda check-in untuk mengi<PERSON><PERSON> ujian.", "faceTip10": "<PERSON><PERSON><PERSON> ini sudah mulai pengenalan wajah, <PERSON>a belum menyelesaikan otentikasi wajah dan tidak dapat memasuki ujian.", "faceTip11": "Tidak bisa mengenali?", "exam": "<PERSON><PERSON><PERSON>", "student": "<PERSON><PERSON><PERSON>", "signin": "<PERSON><PERSON><PERSON><PERSON>", "signedIn": "<PERSON><PERSON> absen", "signTime": "Waktu Check-in:", "signLocation": "Lokasi Check-in:", "signTip": "<PERSON><PERSON><PERSON> se<PERSON>ai dan <PERSON> tidak check-in tepat waktu", "scanSignIn": "Absen scan", "scanSignTip": "Ujian ini membuka check-in u<PERSON><PERSON>, dan <PERSON>a hanya dapat mengikuti ujian setelah menyelesaikan check-in.", "scanSignTip1": "Harap dicatat bahwa setelah check-in, <PERSON>a hanya dapat menggunakan perangkat yang Anda check-in untuk mengakses per<PERSON>aan ujian.", "scanTip": "<PERSON><PERSON><PERSON>, silakan pindai kode QR yang benar", "scanTip1": "Anda tidak ada dalam daftar untuk ujian ini dan tidak dapat masuk", "scanTip2": "Kode QR telah ked<PERSON>, silakan pindai kodenya lagi", "signPosition": "Lokasi check-in", "surround": "Lokasi terdekat", "locationWrong": "Lokasi tidak akurat", "posTitle": "Penyimpangan pemosisian besar", "posMsg1": "Konfirmasikan bahwa izin Baca Lokasi dari ULearning tidak dilarang", "posMsg2": "Jika Anda terhubung ke hotspot publik, coba matikan Wi-Fi, nyalakan data seluler, lalu temukan lagi.", "getPos": "<PERSON><PERSON><PERSON> dalam pem<PERSON>ian", "getPosAgain": "<PERSON><PERSON><PERSON>", "openPos": "Mengaktifkan lokasi telepon", "requestFail": "<PERSON><PERSON><PERSON><PERSON> gagal", "signinSuccess": "Check-in berhasil", "signinFail": "Check-in gagal", "signinFail1": "<PERSON><PERSON> pemosisian sebelum check-in", "signingIn": "Melakukan check-in", "mobilePluginTips1": "Izinkan ULearning untuk menggunakan layanan lokasi, lalu coba lagi.", "mobilePluginTips2": "Aktifkan layanan lokasi dalam pengaturan sistem ponsel, dan izinkan ULearning untuk menggunakan layanan lokasi.", "mobilePluginTips3": "Layanan lokasi tidak stabil, tidak bisa mendapatkan informasi lokasi Anda, <PERSON>a bisa mencoba lagi nanti.", "versionLowerTips1": "Versi saat ini terlalu rendah, harap tingkatkan APP Anda terlebih dahulu.", "forceSubmit": "Bungkus terlebih dahulu", "forceSubmitTip": "<PERSON> <PERSON><PERSON><PERSON><PERSON> u<PERSON>an te<PERSON>, itu akan diserahkan secara otomatis setelah 5 detik, tolong jangan tinggalkan atau tutup halaman.", "forceSubmitting": "Mengirimkan...", "forbitScreenShot": "Tangkapan layar dilarang", "forbitScreenShotTip": "Tangkapan layar dilarang untuk ujian ini, harap patuhi disiplin ujian dengan ketat.", "forbitScreenShotTip1": "<PERSON><PERSON><PERSON> layar ini telah dilaporkan kepada guru, {forbitScreenShotTi2}", "forbitScreenShotTi2": "Jika sistem mendeteksi perilaku tangkapan layar lagi, itu akan memaksa volume untuk diserahkan", "forceSumitTitle": "<PERSON><PERSON><PERSON> otomatis", "forbitScreenShotSubmitTip": "<PERSON><PERSON><PERSON> layar dilarang untuk ujian ini, <PERSON><PERSON> telah melanggar hukum lagi, dan sistem akan memaksa kertas untuk diserahkan.", "studentExamRoom": "<PERSON><PERSON>: {n}", "uploadFileType": "<PERSON><PERSON><PERSON>", "answerTime1": "<PERSON><PERSON><PERSON>", "cannotScanTip": "无法扫码签到？", "useCodeSignin": "使用验证码签到"}