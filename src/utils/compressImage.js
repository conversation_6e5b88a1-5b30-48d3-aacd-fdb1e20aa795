// class compressImage {
//   constructor (config) {
//     this.config = config
//   }

//   init () {

//   }
// }
import { EXIF } from 'exif-js'
const getData = (file) => {
  return new Promise((resolve, reject) => {
    EXIF.getData(file, function () {
      console.log(this)
      let orientation = EXIF.getTag(this, 'Orientation')// 获取Orientation信息
      resolve({ file, orientation })
    })
  })
}

const getImage = (file, orientation) => {
  let promise = new Promise((resolve, reject) => {
    let URL = window.URL || window.webkitURL || window.mozURL
    let url = URL.createObjectURL(file)
    let img = new Image()
    img.onload = () => {
      resolve({ img, orientation })
    }

    img.onerror = (error) => {
      reject(error)
    }
    img.src = url
  })
  return promise
}

// DataURL转Blob对象
const dataURLToBlob = (dataurl) => {
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

const compressImage = (file, options) => {
  return getData(file)
    .then(({ file, orientation }) => {
      return getImage(file, orientation)
    })
    .then(({ img, orientation }) => {
      // 缩放图片需要的canvas
      var canvas = document.createElement('canvas')
      canvas.style.display = 'none'
      document.body.appendChild(canvas)
      var context = canvas.getContext('2d')

      // 图片原始尺寸
      var originWidth = img.width
      var originHeight = img.height
      // 最大尺寸限制
      var maxWidth = options.maxWidth
      var maxHeight = options.maxHeight
      // 目标尺寸
      var targetWidth = originWidth
      var targetHeight = originHeight
      // 图片尺寸超过400x400的限制
      if (originWidth > maxWidth || originHeight > maxHeight) {
        if (originWidth / originHeight > maxWidth / maxHeight) {
          // 更宽，按照宽度限定尺寸
          targetHeight = maxHeight
          targetWidth = Math.round(maxHeight * (originWidth / originHeight))
        } else {
          targetWidth = maxWidth
          targetHeight = Math.round(maxWidth * (originHeight / originWidth))
        }
      }

      // canvas对图片进行缩放
      let width = targetWidth
      let height = targetHeight
      let degree = 0
      canvas.width = targetWidth
      canvas.height = targetHeight

      // 判断图片方向，重置canvas大小，确定旋转角度，iphone默认的是home键在右方的横屏拍摄方式
      switch (orientation) {
        // iphone横屏拍摄，此时home键在左侧
        case 3:
          degree = 180
          targetWidth = -width
          targetHeight = -height
          break
        // iphone竖屏拍摄，此时home键在下方(正常拿手机的方向)
        case 6:
          canvas.width = height
          canvas.height = width
          degree = 90
          targetWidth = width
          targetHeight = -height
          break
        // iphone竖屏拍摄，此时home键在上方
        case 8:
          canvas.width = height
          canvas.height = width
          degree = 270
          targetWidth = -width
          targetHeight = height
          break
      }
      // 清除画布
      // context.clearRect(0, 0, targetWidth, targetHeight)
      context.rotate(degree * Math.PI / 180)
      // 图片压缩
      context.drawImage(img, 0, 0, targetWidth, targetHeight)
      // canvas转为blob并上传
      return new Promise((resolve, reject) => {
        try {
          let dataurl = canvas.toDataURL('image/jpeg')
          let blob = dataURLToBlob(dataurl)
          if (blob.size > file.size) {
            resolve({
              dist: file
            })
          } else {
            blob.name = file.name
            resolve({
              dist: blob
            })
          }
          // canvas.toBlob((blob) => {
          //   if (blob.size > file.size) {
          //     resolve({
          //       dist: file
          //     })
          //   } else {
          //     blob.name = file.name
          //     resolve({
          //       dist: blob
          //     })
          //   }
          // }, file.type || 'image/png')
        } catch (e) {
          resolve({
            dist: file
          })
        }
      })
    })
}
export default compressImage
