import { keyBy, shuffle, get, isString } from 'lodash'
import { decimal2Letter } from '@/common/utils'
// 目前已经支持的题目类型
// const types = [1, 2, 3, 4, 5, 12, 16, 17, 19, 23, 24, 26]
// 复合题类型，即包含小题的题目类型
export const complexTypes = [17, 23, 24, 25]

const set = (object, values) => {
  Object.values(([value, key]) => {
    if (value !== undefined) {
      object[key] = value
    }
  })
}

export default class {
  constructor({ paper, stuAnswer, referAnswer, examStatus }, reportType, isNew, examInfo) {
    this.paper = paper
    this.reportType = reportType
    this.length = 0
    this._map = new Map()
    this._stuAnswer = keyBy(stuAnswer, it => {
      return it.ID || it.questionID
    })
    this._referAnswer = referAnswer || {}
    this.examStatus = examStatus || -1
    this.choiceItems = [] //保存选项乱序的选项顺序
    this.examInfo = examInfo
    this.init(isNew)
  }

  init(isNew) {
    const { part } = this.paper
    this.paper.part = part || []
    // 暂不排序
    // part.sort((a, b) => {
    //   return a.orderindex - b.orderindex
    // })
    // 修正旧数据
    part.forEach(it => {
      const { children = [] } = it
      it.children = children
      children.forEach((it, index) => {
        const { type, questionlist } = it
        if (!complexTypes.includes(type - 0)) {
          if (Array.isArray(questionlist) && questionlist.length > 0) {
            children[index] = questionlist[0]
          }
        }
        // 选项乱序
        const isComprehensive = type == 24 && !/<span class="question"[^/]*<\/span>/.test(it.title)
        if (isNew && this.examInfo.optionOrderBreak && (type == 1 || type == 2 || isComprehensive)) {
          if (type == 1 || type == 2) {
            it.item = shuffle(it.item)
          }
          if (isComprehensive && questionlist) {
            questionlist.forEach(subIt => {
              if (subIt.type == 1 || subIt.type == 2) {
                subIt.item = shuffle(subIt.item)
              }
            })
          }
        }
      })
      // 打乱顺序
      if (isNew && this.examInfo.questionOrderBreak) {
        it.children = shuffle(children)
      }
    })

    // 添加题号
    let order = 0
    // 生成题目
    part.forEach(it => {
      const { children } = it
      children.forEach((it, index) => {
        const { type, questionlist = [], title } = it
        it.type = type - 0
        // 将题干上的link和count绑定在一起
        if(it.link){
          it.linkList = it.link.map((res, j) => {
            return {
              link: res,
              linkLisCount: it.linkLisCount[j],
            }
          })
          // 因整合到linkList中，所以删除lisCount和link
          delete it.linkLisCount
          delete it.link
        }
        // 完型填空题和综合题的type是一样的, 通过检测title进行区分.
        // /<span class="question".+\[小题\d+]/
        if (it.type === 25) {
          it.type = 26 // 25被占用 修改至26
        }
        if (it.type === 24 && /<span class="question"[^/]*<\/span>/.test(title)) {
          it.type = 25
        }
        // 为口语题设置段落
        if (it.type === 19) {
          it.article = (it.correctAnswer || "").replace(/\\n/g, '<br>')
        }

        // 兼容子题音频播放次数缓存
        const parentLisCount = it.type === 24 && it.lisCount > 0 ? it.lisCount : 0

        if (complexTypes.includes(it.type)) {
          it.questionlist = questionlist
          questionlist.forEach((it, i) => {
            it.order = order++
            it.type = it.type - 0
            it.examStatus = this.examStatus
            // 将题干上的link和count绑定在一起
            if(it.link){
              it.linkList = it.link.map((res, j) => {
                return {
                  link: res,
                  linkLisCount: it.linkLisCount[j]
                }
              })
            }
            // 因整合到linkList中，所以删除lisCount和link
            delete it.linkLisCount
            delete it.link
            if (parentLisCount > 0) it.lisCount = parentLisCount
            const question = this.genQuestion(it)
            question.parent = children[index]
            questionlist[i] = question
            this._map.set(it.questionid, question)
          })
        } else {
          it.order = order++
        }
        it.examStatus = this.examStatus
        const question = this.genQuestion(it)
        children[index] = question
        this._map.set(it.questionid, question)
      })
    })
    this.length = order // 试卷题目长度
  }

  genQuestion(item) {
    const ID = item.questionid
    const studentAnswer = this._stuAnswer[ID] || {}
    const referAnswer = this._referAnswer[ID] || {}
    const { type, lisCount: limitCount = -1, linkList = [], questionlist = [] } = item
    const { grade, remark1, lisCount: count, answer } = studentAnswer
    item.grade = grade
    item.remark1 = remark1
    if (type == 5) {
      item.studentFiles = []
      if (studentAnswer.studentFiles) {
        item.studentFiles = typeof studentAnswer.studentFiles === 'string' ? JSON.parse(studentAnswer.studentFiles) : studentAnswer.studentFiles
      }
    }
    // 设置听力限制次数
    if (limitCount > -1 || (linkList && linkList.map(item=>item.linkLisCount).length > 0)) {
      const length = linkList.length
      this.getUsedCount(type, length, item, count).forEach((res,index) => {
        item.linkList[index].count = res
      });
    }

    // 设置答案 解析
    if (complexTypes.includes(type) && questionlist.length > 0) {
      let stat = ''
      for (let i = 0; i < questionlist.length; i++) {
        const item = questionlist[i]
        if (item.stat === 'answer' || item.stat === 'error') {
          stat = item.stat
          break
        }
        if (i === questionlist.length - 1) {
          stat = item.stat
        }
      }
      item.stat = stat
    } else {
      item.studentAnswer = this.convertAnswer(answer, type)
      // 用户作答情况 'answer' 表示已作答 'forgot' 表示忘记作答 'error' 错误 'right' 正确 'blank' 表示未作答
      // 目前没有区分 error 和 forgot，所以forgot 也当作是error处理了。
      if (this.reportType < 2) {
        // 显示用户是否作答
        if (this.existAnswer(item.studentAnswer)) {
          item.stat = 'answer'
        } else {
          item.stat = 'blank'
        }
      } else if (this.reportType === 2) {
        // 显示用户答题正确与否
        // 显示用户答题正确与否]
        let correctAnswer = referAnswer.correctAnswer
        // 根据选项id重新获取正确答案（选项乱序专用）
        if (referAnswer.correctChoiceIds && referAnswer.correctChoiceIds.length > 0) {
          correctAnswer = referAnswer.correctChoiceIds.map(it => {
            const index = item.item.findIndex(choice => choice.choiceId == it)
            return index > -1 ? decimal2Letter(index, 26).toUpperCase() : ''
          }).join(";")
        }
        item.referAnswer = this.convertAnswer(correctAnswer, type)
        item.referReply = referAnswer.correctReplay
        item.analy = referAnswer.analy
        item.answerlink = referAnswer.answerlink
        item.checkResult = this.check(type, item.studentAnswer, item.referAnswer, item.blackOrder)
        // 客观题不设置对错，只设置是否已作答
        if (type === 5 || type === 16 || type === 19) {
          item.stat = this.existAnswer(item.studentAnswer) ? 'answer' : 'blank'
        } else {
          if (Array.isArray(item.checkResult)) {
            item.stat = item.checkResult.every(it => it) ? 'right' : 'error'
          } else {
            item.stat = item.checkResult ? 'right' : 'error'
          }
        }
      }
    }
    return item
  }

  getUsedCount(type, length, item, count) {
    const convertCount = (arr, length) => {
      return Array.isArray(arr) && arr.length > 0 ?
        arr :
        new Array(length).fill(0)
    }
    if (complexTypes.includes(type)) {
      // 包含子题的设置次数从小题中取得
      const id = get(item, 'questionlist[0].questionid', null)
      if (this._stuAnswer[id]) {
        return convertCount(this._stuAnswer[id].parentLisCount, length)
      } else {
        return new Array(length).fill(0)
      }
    } else {
      return convertCount(count, length)
    }
  }

  // 答案转换
  convertAnswer(answer, type) {
    const answerToLowerCase = answer => {
      return isString(answer) ? answer.toLowerCase().trim() : ''
    }
    const answerToArr = answer => {
      // 正确答案中文分号替换成英文分号
      if (isString(answer)) return answer.replace(/；/g, ';').split(';').map(it => it.trim())
      else return Array.isArray(answer) ? answer : []
    }
    switch (type) {
      case 2:
        return answerToArr(answer).filter(it => it)
      case 3:
      case 12:
      case 23:
      case 26:
        return answerToArr(answer)
      case 4:
        if (Array.isArray(answer)) return answerToLowerCase(answer[0])
        else return answerToLowerCase(answer)
      case 5:
        return isString(answer) ? answer : ''
      default:
        return isString(answer) ? answer.trim() : ''
    }
  }

  // 判断答案是否存在
  existAnswer(answer) {
    if (Array.isArray(answer)) {
      if (answer.join('').trim()) {
        return true
      } else {
        return false
      }
    } else if (isString(answer)) {
      return !!(answer.trim())
    } else {
      return false
    }
  }
  // 填空题、公式计算题stat转换
  changeComplexTypes(question) {
    let flag = true
    if ([3, 26].includes(question.type) && question.studentAnswer && question.studentAnswer.length > 0) {
      question.studentAnswer.forEach(item => {
        if (item.trim() == '') {
          flag = false
        }
      })
    }
    return flag
  }
  /**
   * 检查答案是否正确
   * @param {number} type 题目类型
   * @param {*} studentAnswer 学生答案
   * @param {*} referAnswer 参考答案
   * @param {number} blankOrder 填空题相关
   */
  check(type, studentAnswer, referAnswer, blankOrder) {
    if (type === 2) {
      // 多选
      studentAnswer.sort()
      referAnswer.sort()
      return studentAnswer.join('') === referAnswer.join('')
    } else if (type === 3) {
      const checkResult = new Array(referAnswer.length).fill(false)
      // 半角字符转全角字符
      var charH2FUtil = (hAngleChar) => {
        let fAngleChar = '';
        var arr = hAngleChar.split('')
        for (var i = 0; i < arr.length; i++) {
          // /**
          //  * 特殊字符跳过处理：将对应的字符编码放入数组中
          //  * 例：65292为中文逗号，保留该符号不被转换
          //  */
          // if ([65292].indexOf(hAngleChar[i].charCodeAt(i)) > -1) {
          //   fAngleChar += String.fromCharCode(hAngleChar[i].charCodeAt(i));
          //   continue;
          // }
          if (arr[i].charCodeAt() > 65248 && arr[i].charCodeAt() < 65375) {
            fAngleChar += String.fromCharCode(arr[i].charCodeAt() - 65248);
          } else {
            fAngleChar += String.fromCharCode(arr[i].charCodeAt());
          }
        }
        return fAngleChar;
      };
      if (blankOrder === 0) {
        // 不可以互换
        const length = Math.min(studentAnswer.length, referAnswer.length)
        for (let i = 0; i < length; i++) {
          const refers = (referAnswer[i] || '').split('//')
          const stu = (studentAnswer[i] || '').trim().replace(/\s+/g, " ").replace(/[.。,，\"\\:]/g, "")
          const status = refers.some(it => {
            const subRefer = (it || '').trim().replace(/\s+/g, " ").replace(/[.。,，\"\\:]/g, "")
            return subRefer && charH2FUtil(subRefer.toLowerCase()) === charH2FUtil(stu.toLowerCase())
          })
          checkResult[i] = status
        }
      } else {
        // 填空可以互换
        for (let i = 0; i < referAnswer.length; i++) {
          const refers = (referAnswer[i] || '').split('//')
          for (let j = 0; j < refers.length; j++) {
            const subRefer = refers[j].trim().replace(/\s+/g, " ").replace(/[.。,，\"\\:]/g, "")
            if (subRefer) {
              const index = studentAnswer.findIndex((it, index) => {
                const stu = (it || '').trim().replace(/\s+/g, " ").replace(/[.。,，\"\\:]/g, "")
                return charH2FUtil(subRefer.toLowerCase()) === charH2FUtil(stu.toLowerCase()) && !checkResult[index]
              })
              if (index >= 0) {
                checkResult[index] = true
                break
              }
            }
          }
        }
      }
      return checkResult
    } else if (type === 12) {
      let checkResult = new Array(referAnswer.length).fill(false)
      referAnswer.forEach((it, index) => {
        if (it === studentAnswer[index]) {
          checkResult[index] = true
        }
      })
      return checkResult
    } else if (type === 4) {
      return studentAnswer.toLowerCase() === referAnswer.toLowerCase()
    } else {
      return studentAnswer === referAnswer
    }
  }

  // 保存学生作答
  saveStudentAnswer(ID, stuAnswer) {
    const question = this._map.get(ID)
    if (question) {
      const { type, parent } = question
      if (type == 26) {
        question.grade = stuAnswer.grade
        stuAnswer = stuAnswer.res
      }
      question.studentAnswer = this.convertAnswer(stuAnswer, type)
      question.stat = this.existAnswer(stuAnswer) && this.changeComplexTypes(question) ? 'answer' : 'blank'
      if (parent) {
        setTimeout(() => {
          parent.stat = parent.questionlist.every(it => it.stat === 'answer') ? 'answer' : 'blank'
        })
      }
    }
  }
  // 题目id
  saveAudioCount(ID) {
    const question = this._map.get(ID)
    return question.linkList ? question.linkList.map(item => item.count) : []
  }

  // 收集选项乱序的顺序
  collectOptionsOrder(question, answers) {
    if (this.examInfo.optionOrderBreak && (question.type == 1 || question.type == 2)) {
      answers.choiceId = []
      var choiceItem = {
        "ID": question.questionid,
        "choices": []
      }
      question.item && question.item.forEach((it, i) => {
        choiceItem.choices.push({
          "choiceId": it.choiceId,
          "orderIndex": i + 1
        })
        if (answers.answer && (answers.answer.indexOf(decimal2Letter(i, 26).toUpperCase()) > -1)) {
          answers.choiceId.push(it.choiceId)
        }
      })
      this.choiceItems.push(choiceItem)
    }
  }
  // 收集已答题
  collectStudentAnswers() {
    const tabs = []
    const { part, isChangeOrder } = this.paper
    this.choiceItems = []
    const get = it => {
      const { questionid, studentAnswer, score, type } = it
      const countArr = it.linkList ? it.linkList.map(item => item.count || 0) : undefined
      let answers = { ID: questionid, type, answer: studentAnswer, score, lisCount: countArr }
      if (type == 5) {
        answers.studentFiles = it.studentFiles.filter(file => {
          return file.progress >= 100
        })
      }
      if (type == 25) {
        answers.type = 24
      }
      if (type == 26) {
        answers.type = 25
        answers.grade = it.grade
      }
      return answers
    }
    let orderIndex = 1;
    part.forEach(it => {
      const { children } = it
      children.forEach(it => {
        const { type } = it
        if (complexTypes.includes(type)) {
          if (this.examInfo.questionOrderBreak) {
            tabs.push({ ID: it.questionid, type: type === 25 ? 11 : type, answer: "", score: 0, orderIndex: orderIndex })
            orderIndex++
          }
          let count = it.linkList ? it.linkList.map(item => item.count || 0) : undefined
          it.questionlist.forEach(it => {
            const data = get(it)
            if (count) data.parentLisCount = count
            if (this.examInfo.questionOrderBreak) {
              data.orderIndex = orderIndex
              orderIndex++
            }
            if (type === 24) {
              this.collectOptionsOrder(it, data)
            }
            tabs.push(data)
          })
        } else {
          const data = get(it)
          if (this.examInfo.questionOrderBreak) {
            data.orderIndex = orderIndex
            orderIndex++
          }
          this.collectOptionsOrder(it, data)
          tabs.push(data)
        }
      })
    })
    return tabs
  }

  // 获取已作答题目的数量
  getAnsweredLength() {
    const { part } = this.paper
    let length = 0
    part.forEach(it => {
      const { children } = it
      children.forEach(it => {
        const { type, stat, questionlist } = it
        if (complexTypes.includes(type)) {
          questionlist.forEach(it => {
            const { type, stat } = it;
            (stat === 'answer' || (type == 5 && it.studentFiles && it.studentFiles.length > 0)) && length++
          })
        } else {
          (stat === 'answer' || (type == 5 && it.studentFiles && it.studentFiles.length > 0)) && length++
        }
      })
    })
    return length
  }
}