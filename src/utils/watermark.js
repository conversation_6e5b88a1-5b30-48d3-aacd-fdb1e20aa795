export function getWaterMarkImg(data) {
  const getText = (ctx, str, maxWidth) => {
    const width = ctx.measureText(str).width
    if (width > maxWidth) {
      const length = str.length
      for (let i = 0; i < length; i++) {
        const strTemp = str.substring(0, i + 1) + '...'
        if (ctx.measureText(strTemp).width >= maxWidth) {
          return strTemp
        }
      }
    } 
    return str
  }
  let canvas = document.createElement("canvas");
  let ctx = canvas.getContext('2d');
  const name = getText(ctx, data.name || '', 80)
  const loginName = getText(ctx, data.loginName || "", 120)
  const maxWidth = Math.max(ctx.measureText(name).width, ctx.measureText(loginName).width);
  const radian = 30 * Math.PI / 180;
  canvas.width = maxWidth*Math.cos(radian) + 110;
  canvas.height = maxWidth*Math.sin(radian) + 100;
  ctx.translate(canvas.width/2, canvas.height/2);
  ctx.rotate(-radian);
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.font = "20px microsoft yahei";
  ctx.fillStyle = 'rgba(68, 68, 68, 0.15)';
  ctx.fillText(name, 0, -10);
  ctx.font = "14px microsoft yahei";
  ctx.fillText(loginName, 0, 10);
  return canvas.toDataURL();
}

export function setShadowRoot(img) {
  const style = `
    position: absolute;
    top: 30px;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    margin: 20px;
    overflow: hidden;
    background: url('${img}') !important;
  `;
  let waterMark = document.createElement("div")
  waterMark.className = "wm"
  waterMark.setAttribute('style', 'pointer-events: none !important; display: block !important;');
  let shadowRoot = null;
  if (typeof waterMark.attachShadow === 'function') {
    shadowRoot = waterMark.attachShadow({mode: 'open'});
  } else {
    shadowRoot = waterMark;
  }
  let waterMarkItem = document.createElement('div');
  waterMarkItem.setAttribute('style', style);
  shadowRoot.appendChild(waterMarkItem);
  return waterMark
}

export function mutationObserverInit(img, option) {
  const MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;
  let watermarkDom = new MutationObserver((records) => {
    try {
      if (records.length === 1) {
        let record = records[0]
        if (record.removedNodes.length >= 1 && record.target._prevClass === 'question-item') {
          console.log("record1", record)
          const dom = setShadowRoot(img);
          record.target.appendChild(dom);
          watermarkDom.observe(dom.shadowRoot, option);
        } else if (record.target.offsetParent && record.target.offsetParent._prevClass === 'swiper-slide' && !record.target._prevClass) {
          console.log("record2", record)
          if (record.target.offsetParent && record.target.offsetParent.querySelector(".question-item .wm")) {
            record.target.offsetParent.querySelector(".question-item .wm").remove()
          } else if (record.target.parentNode && record.target.parentNode._prevClass === 'question-item') {
            record.target.parentNode.lastChild.remove()
          }
        } else {
          console.log("record3", record)
        }
      }
    } catch(e) {
      console.log(e)
    }
  });
  return watermarkDom
}