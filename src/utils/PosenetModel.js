import i18n from '@/i18n'

export default class PosenetModel {
  constructor() {
    this.net = null
  }
  /**
   * @description: 加载检测模型
   * @param config: 
   * architecture: 可以是MobileNetV1或ResNet50。它决定加载哪个PoseNet体系结构,默认为MobileNetV1。
   * outputStride: 可以是8,16,32之一(ResNet架构支持Stride 16,32, MobileNetV1架构支持Stride 8,16,32。但是如果你使用的是stride 32，你必须将乘数设置为1.0)。它指定了PoseNet模型的输出步长。数值越小，输出分辨率越大，以速度为代价，模型越精确。将该值设置为较大的值以提高速度，但代价是精度，默认为16。 
   * inputResolution: 一个数字或类型为{width: number, height: number}的对象。默认为257。它指定了图像在输入到PoseNet模型之前被调整和填充的大小。数值越大，以速度为代价的模型就越精确。将该值设置为一个较小的值，以牺牲精度来提高速度。如果提供了数字，则图像将被调整大小并填充为具有相同宽度和高度的正方形。如果提供了宽度和高度，图像将被调整大小并填充到指定的宽度和高度。
   * multiplier: 可以是1.0,0.75或0.50之一(该值仅被MobileNetV1架构使用，不被ResNet架构使用)。它是所有卷积运算的深度(通道数量)的浮点乘法器。值越大，层的尺寸就越大，以速度为代价，模型就越精确。将该值设置为一个较小的值，以牺牲精度来提高速度。默认是0.75,移动端建议为0.5。
   * quantBytes: 此参数控制用于权重量化的字节。可用选项有:
      4: 每个浮点数4字节(没有量化)。导致最高的精度和原始模型大小(~90MB)。
      2: 每个浮点数2字节。导致精度略低，模型尺寸减少2倍(~45MB)。
      1: 每个浮点数1字节。导致较低的精度和4倍的模型尺寸缩小(~22MB)。
   * modelUrl: 一个可选字符串，指定模型的自定义url。eg:xxx/xxxx/xxx/model-stride16.json
   * @return {Array} 多人姿态数据
   */
  async loadNet() {
    if (!window.posenetBin) {
      window.posenetBin = await posenet.load({
        multiplier: 0.5
      })
    }
    this.net = window.posenetBin
    return this.net
  }
  /**
   * @description: 从图像获取姿态数据
   * @param image: ImageData|HTMLImageElement|HTMLCanvasElement|HTMLVideoElement通过网络传输的输入图像。
   * @param inferenceConfig: 
   * imageScaleFactor: 图像比例因子 在 0.2 和 1 之间的数字。默认为 0.50。在送入神经网络之前如何缩放图像。将此数字设置得较低以缩小图像，并以精度为代价增加通过网络的速度。
   * flipHorizontal: 默认为 false。表示是否姿态应该水平/垂直镜像。对于视频默认水平翻转（比如网络摄像头）的视频，应该设置为 true，因为你希望姿态能以正确的方向返回。
   * maxDetections: 可检测的最大姿态数。默认为5。
   * scoreThreshold: 只返回根部分得分大于或等于该值的实例检测。默认为0.5。
   * nmsRadius: 非最大抑制部分距离。它必须是正的。如果两个部分之间的距离小于nmsRadius像素，那么它们会相互抑制。默认为20。
   * @return {Array} 多人姿态数据
   */
  async getPose(image, callback) {
    const net = await this.loadNet()
    if (net) {
      const pose = await net.estimateMultiplePoses(image, {
        scoreThreshold: 0.1
      });
      this.drawKeypoints(pose, (status,result) => {
        console.log("检测结果", status, result)
        callback && callback(status, result)
      })
      return pose;
    }
    return [];
  }

  calculateHeadYaw(pose) {
    const points = pose.keypoints;
    let yaw = 0;
    let distanceRatio;
    let sideLookingAt;

    //计算鼻子到双眼的横坐标距离
    let eyeNoseDistance = {
      left: Math.abs(points[1].position.x - points[0].position.x),
      right: Math.abs(points[2].position.x - points[0].position.x)
    };

    // 通过距离计算偏移度
    if (eyeNoseDistance.left > eyeNoseDistance.right) {
      distanceRatio = 1 - eyeNoseDistance.right / eyeNoseDistance.left;
      sideLookingAt = 1;
    } else {
      distanceRatio = 1 - eyeNoseDistance.left / eyeNoseDistance.right;
      sideLookingAt = -1;
    }

    //计算角度
    yaw = (distanceRatio * 90 * sideLookingAt * Math.PI) / 180;

    return yaw;
  }

  calculateHeadPitch(pose) {
    let yEarAverage = 0;
    let numEarsFound = 0;
    let eyeDistance = 0;
    let distanceRatio = 0;
    let points = pose.keypoints;

    //计算两耳的平均Y坐标
    if (points[3].score >= 0.5) {
      numEarsFound++;
      yEarAverage += points[3].position.y;
    }
    if (points[4].score >= 0.5) {
      numEarsFound++;
      yEarAverage += points[4].position.y;
    }
    yEarAverage = yEarAverage / numEarsFound;
    //计算两个眼睛的X坐标距离
    eyeDistance = points[1].position.x - points[2].position.x;
    distanceRatio = (points[0].position.y - yEarAverage) / eyeDistance;

    return (90 * distanceRatio * Math.PI) / 180;
  }

  drawKeypoints(poses, callback) {
    let maxScorePose = undefined;
    let maxScore = 0;
    let personNum = 0;
    for (let i = 0; i < poses.length; i++) {
      // For each pose detected, loop through all the keypoints
      let pose = poses[i];
      if (pose.score >= 0.3 && pose.keypoints[0].score >= 0.9 && (pose.keypoints[1].score >= 0.9 || pose.keypoints[2].score >= 0.9)) {
        personNum++;
      }
      for (let j = 0; j < pose.keypoints.length; j++) {
        //记录鼻子score值最高的人的信息
        if (pose.keypoints[0].score >= 0.5 && pose.keypoints[0].score > maxScore) {
          maxScorePose = pose;
          maxScore = pose.keypoints[0].score;
        }
      }
    }
    if (!maxScorePose) {
      callback(7, i18n.t('leaveSeat'));
      return;
    }
    if (personNum > 1) {
      callback(9, i18n.t('multiPerson'));
      return;
    }

    let yaw = this.calculateHeadYaw(maxScorePose);
    let status = 0;
    if (yaw > Math.PI / 4) {
      status += 1;
    } else if (yaw < -Math.PI / 4) {
      status += 2;
    }
    let pitch = this.calculateHeadPitch(maxScorePose);
    if (pitch > Math.PI / 4) {
      status += 4;
    } else if (pitch < -Math.PI / 4) {
      status += 8;
    }
    if (status === 0) {
      callback(6, i18n.t('statusNormal'));
    } else if (status > 0 && status < 11) {
      callback(8, i18n.t('deviatedSight'));
    }
  }
}