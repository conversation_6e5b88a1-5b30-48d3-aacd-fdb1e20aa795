// 根据文件的扩展名或文件名 给出文件的类型
export function formatLimit (suffix) {
  suffix = suffix.substring(suffix.lastIndexOf('.') + 1)
  var fileLimit = {
    video: {
      format: 'mp4,mpg,mpeg,avi,rmvb,rm,wmv,mov,video,flv',
      size: 1024,
      transcoding: '自动转码(H.264编码的mp4格式)，切片(流媒体播放)，压缩(128~1024kbps)',
      type: 'video'
    },
    audio: {
      format: 'mp3,aac,wav,wma,ogg,m4a,audio',
      size: 50,
      transcoding: '自动转码(AAC编码的mp3格式)，切片(流媒体播放)，压缩',
      type: 'audio'
    },
    image: {
      format: 'png,jpg,jpeg,gif,bmp,image', // 加image是为了兼容客户端传回来的内容
      size: 2,
      transcoding: '--',
      type: 'image'
    },
    doc: {
      format: 'doc,docx',
      size: 50,
      transcoding: '转图片',
      type: 'doc'
    },
    xls: {
      format: 'xls,xlsx',
      size: 50,
      transcoding: '转图片',
      type: 'xls'
    },
    pdf: {
      format: 'pdf',
      size: 50,
      transcoding: '转图片',
      type: 'pdf'
    },
    ppt: {
      format: 'ppt,pptx',
      size: 50,
      transcoding: '转图片',
      type: 'ppt'
    },
    zip: {
      format: 'zip,rar,7z',
      size: 50,
      transcoding: '压缩',
      type: 'zip'
    },
    txt: {
      format: 'txt',
      size: 50,
      transcoding: '文本',
      type: 'txt'
    }
  }
  suffix = suffix.toLowerCase()
  let type = 'other'
  let fileType
  for (fileType in fileLimit) {
    var limit = fileLimit[fileType].format
    if (limit.indexOf(suffix) >= 0 && suffix) {
      type = fileLimit[fileType].type
      return type
    }
  }
  return type
}
// 根据给定的大小B转换成MB等单位
export function formatSize (size) {
  if (size === undefined || /\D/.test(size)) {
    return 'invalid data'
  }
  function round (num, precision) {
    return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision)
  }
  var boundary = Math.pow(1024, 4)
  // TB
  if (size > boundary) {
    return round(size / boundary, 1) + 'TB'
  }
  // GB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'GB'
  }
  // MB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'MB'
  }
  // KB
  if (size > 1024) {
    return Math.round(size / 1024) + 'KB'
  }
  if (size === '') {
    return ''
  }
  return size + 'b'
}
// 将秒转换为 00:00:00格式
export function formatSecond (second) {
  if (second === '' || second === undefined || second < 0 || /\D/.test(second)) {
    return ''
  }
  var hh = parseInt(second / 3600)
  if (hh < 10) hh = '0' + hh
  var mm = parseInt((second - hh * 3600) / 60)
  if (mm < 10) mm = '0' + mm
  var ss = parseInt((second - hh * 3600) % 60)
  if (ss < 10) ss = '0' + ss
  var result = hh + ':' + mm + ':' + ss

  if (second >= 0) {
    return result
  } else {
    return 'NaN'
  }
}
// 根据文件类型 返回后缀名
export function getIconfont (type, mimeType) {
  var fileLimit = {
    doc: {
      format: 'doc, docx',
      size: 50,
      transcoding: '转图片',
      type: 'doc'
    },
    xls: {
      format: 'xls, xlsx',
      size: 50,
      transcoding: '转图片',
      type: 'xls'
    },
    pdf: {
      format: 'pdf',
      size: 50,
      transcoding: '转图片',
      type: 'pdf'
    },
    ppt: {
      format: 'ppt, pptx',
      size: 50,
      transcoding: '转图片',
      type: 'ppt'
    }
  }
  var logo = 'other'
  switch (type) {
    case 1:
      logo = 'video'
      break
    case 2:
      logo = 'audio'
      break
    case 3:
      logo = 'image'
      break
    case 4:
      if (fileLimit.doc.format.indexOf(mimeType) >= 0) {
        logo = 'doc'
      }
      if (fileLimit.xls.format.indexOf(mimeType) >= 0) {
        logo = 'xls'
      }
      if (fileLimit.pdf.format.indexOf(mimeType) >= 0) {
        logo = 'pdf'
      }
      if (fileLimit.ppt.format.indexOf(mimeType) >= 0) {
        logo = 'ppt'
      }
      break
    case 5:
      logo = 'zip'
      break
    case 7:
      logo = 'link'
      break
    case 8:
      logo = 'image'
      break
    case 10:
      logo = 'kejian'
      break
    case 14:
      logo = 'weike1'
      break
    default:
      logo = 'other'
  }
  return logo
}
// 根据后缀名 返回文件类型
export function getFileType (ext) {
  var mimeTypes = [{
    title: 'document',
    extensions: 'doc,docx,xls,xlsx,ppt,pptx,pdf,txt'
  }, {
    title: 'image',
    extensions: 'png,jpg,gif,jpeg,bmp'
  }, {
    title: 'video',
    extensions: 'flv,mp4,avi,wmv,rm,rmvb,mpeg,mov,mpg'
  }, {
    title: 'audio',
    extensions: 'mp3,wav,wma'
  }, {
    title: 'rar',
    extensions: 'rar,zip'
  }]
  if (ext == '' || ext == null) {
    return 'richtext'
  } else {
    ext = ext.toLowerCase()
  }
  for (var i = 0; i < mimeTypes.length; i++) {
    var extArr = mimeTypes[i].extensions.split(',')
    for (var j = 0; j < extArr.length; j++) {
      if (extArr[j] == ext) {
        return mimeTypes[i].title
      }
    }
  }
  return 'other'
}

export function getSPOCFileTypeIcon (type, ext) {
  var className = ''
  if (type == 14) {
    className = 'weike1'
  } else if (type == 4) {
    className = 'weike'
  } else if (type == 3) {
    className = 'wenjianjia'
  } else if (type == 2) {
    className = 'link'
  } else if (type == 1) {
    var fileType = getFileType(ext)
    switch (fileType) {
      case 'video':
        className = 'video'
        break
      case 'audio':
        className = 'audio'
        break
      case 'image':
        className = 'image'
        break
      case 'richtext':
        className = 'richtext'
        break
      case 'rar':
        className = 'zip'
        break
      case 'document':
        if (ext.indexOf('doc') > -1) {
          className = 'doc'
        } else if (ext.indexOf('ppt') > -1) {
          className = 'ppt'
        } else if (ext.indexOf('pdf') > -1) {
          className = 'pdf'
        } else if (ext.indexOf('xls') > -1) {
          className = 'xls'
        } else if (ext.indexOf('txt') > -1) {
          className = 'txt'
        } else {
          className = 'other'
        }
        break
      default:
        className = 'other'
    }
  }
  return className
}

export function buildResourcePath (src) {
  var httpReg = /^(http|https)/

  if (httpReg.test(src)) {
    
  } else {
    src = 'https://leicloud.ulearning.cn/' + src
  }

  return src
}