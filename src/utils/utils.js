import { getPlatform } from '@/common/utils'
export function compareVersion(version1, version2) {
  version1 = version1 || '0'
  var version1List = version1.split('.');
  var version2List = version2.split('.');
  var maxLength = Math.max(version1List.length, version2List.length);
  for (var i = 0; i < maxLength; i++) {
    var v1 = Number(version1List[i]) || 0;
    var v2 = Number(version2List[i]) || 0;

    if (v1 > v2) {
      return 1;
    } else if (v1 < v2) {
      return -1;
    } else {
      continue;
    }
  }
  return 0;
};

export function getUrlParam(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURI(r[2]);
  return null;
}

export function textCount(content) {
  if (!content) {
    return 0
  }
  let str = content.trim().replace(/\r\n|\n|\t/g, ' ')
  const cnRegx = /[\u4E00-\u9FA5\uF900-\uFA2D。，、；：？！…—·ˉ¨‘’“”～々‖∶＂＇｀｜〃〔〕〈〉《》「」『』．〖〗【】（）［］｛｝]/g
  const cnwords = str.match(cnRegx) || []
  const enwordsStr = str.replace(cnRegx, '').trim()
  const enwords = enwordsStr.length > 0 ? enwordsStr.split(/\s+/g) : [];
  return enwords.length + cnwords.length
}

export function loadScript(url, callBack, errorCallback) {
  const script = document.createElement('script')
  script.src = url
  script.onload = script.onreadystatechange = function () {
    if (!this.readyState || this.readyState === 'loaded' || this.readyState === 'complete') {
      callBack && callBack();
      this.onload = this.onreadystatechange = null;
    }
  }
  script.onerror = () => {
    errorCallback && errorCallback()
  }
  document.head.appendChild(script)
}

export function loadStaticScript(path, callBack) {
  const { hostname } = location
  let site = 'www.tongshike.cn'
  const seps = hostname.split('.')
  const seps2 = seps[seps.length - 2]
  if (seps2 && (seps2.startsWith('ulearning') || seps2.startsWith('zambialearning'))) {
    site = 'static.' + seps.slice(-2).join('.')
  }
  const url = 'https://' + site + '/static/' + path
  loadScript(url, callBack)
}

export function getHashParam(name, url) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = (url || window.location.hash).substr(1).match(reg);
  if (r != null) return decodeURI(r[2]);
  return null;
}

export function getDeviceVersion() {
  let ua = navigator.userAgent
  if (getPlatform().isAndroid) {
    let sss = ua.split(';')
    let index = -1
    for (let i in sss) {
      if (sss[i].indexOf('Build/') > 0) {
        index = i
        break
      }
    }
    if (index > -1) {
      return sss[index].substring(0, sss[index].indexOf('Build/'))
    }
  } else {
    return 'iPhone'
  }
}
export function getUniqueValue() {
  return new Date().getTime()*Math.random()
}

// 富文本禁止学生依次输入多个字符防作弊  返回true需要清空处理
export function checkUpdateAnswerLength(value) {
  if (value.length > 5) {
    return value.length > 20 || /[\u4e00-\u9fa5]/g.test(value);
  }
  return false;
}