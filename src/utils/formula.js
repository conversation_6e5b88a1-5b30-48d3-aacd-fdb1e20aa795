import math from 'mathjs'
export function getComputedCorrect (param) {
  var formula = param.correct.formula
  var precision = param.correct.precision
  var formulaVar = param.formulaVar
  var decimalNumber = 0
  var scope = {}
  if (precision !== 1) {
    decimalNumber = (Math.round(0.1 / precision) + '').length
  }
  for (var j = 0; j < formulaVar.length; j++) {
    scope[formulaVar[j].name] = formulaVar[j].value
  }
  var result = parseFloat(math.format(math.eval(formula, scope), {
    precision: 14
  }))
  result = result.toFixed(decimalNumber)
  return result
}

export function evalFormula (param) {
  var precision = param.correct.precision
  var answerRange = param.correct.answerRange
  var reply = param.reply
  var decimalNumber = 0
  if (precision != 1) {
    decimalNumber = (Math.round(0.1 / precision) + '').length
  }

  var result = getComputedCorrect(param)
  var userAnswer = parseFloat(reply).toFixed(decimalNumber)
  if (reply != null && reply !== undefined && reply !== '') {
    if (userAnswer <= (parseFloat(result) + parseFloat(answerRange)) && userAnswer >= (parseFloat(result) - parseFloat(answerRange))) {
      return true
    }
  }
  return false
}