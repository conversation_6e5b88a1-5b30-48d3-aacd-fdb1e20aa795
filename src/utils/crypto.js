const CryptoJS = require("crypto-js")

export function decrypt(str, key) {
  if (!str) {
    return ''
  }
  str = str.replace(/\\|\n/g, '')
  const ciphertextBytes = CryptoJS.enc.Base64.parse(str)
  const decrypted = CryptoJS.AES.decrypt(
    { ciphertext: ciphertextBytes },
    CryptoJS.enc.Utf8.parse(key),
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }
  )
  return decrypted.toString(CryptoJS.enc.Utf8)
}
