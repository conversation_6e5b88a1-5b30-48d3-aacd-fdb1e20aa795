<script>
import { <PERSON>roll, Popup, Toast } from 'cube-ui'
import { <PERSON>, But<PERSON> } from '@/common/components'
import Details from './details'
import Report from './report'
import Hint from './hint'
import Instructions from './instructions'
import EnterTips from './enter-tips'
import { getExamInfo, getExamReport, getExamStatus, repayExam, terminalCheck } from '@/api'
import faceApi from '@/api/face'
import { getPlatform } from '@/common/utils'

import startExamBg from '@/assets/images/info_start_bj.png'
import finishExamBg from '@/assets/images/info_finish.png'
import absentExamBg from '@/assets/images/info_quekao.png'
import Loading from '@/common/components/loading'
import { mapMutations } from 'vuex'
import { compareVersion, getUrlParam } from '@/utils/utils'
import { getScreenRecordState, checkPermission, exitApp, updateActiveList, startScreenCaptureService } from '@/lib/cordovaPlugin/mobilePlugin'

export default {
  components: {
    Page,
    Scroll,
    Details,
    Popup,
    Hint,
    Report,
    UButton: Button,
    Toast,
    Loading,
    Instructions,
    EnterTips
  },
  data() {
    return {
      data: {},
      status: 'loading',
      interfaceName: '',
      examStatus: {
        hasRecord: false,
        status: -1,
      },
      options: {
        preventDefaultException: {
          className: /(records)/,
        },
        scrollBar: false,
      },
      showBack: getUrlParam('mp') === '1',
      // 屏幕监控弹窗防抖
      screenServiceChecking: false
    }
  },
  computed: {
    name() {
      return this.$route.name
    },
    user() {
      return this.$store.state.user
    },
    userID() {
      return parseInt(this.$route.params.userID)
    },
    examID() {
      return parseInt(this.$route.params.examID)
    },
    ui() {
      const { interfaceName: name } = this
      if (name === 'info') {
        return {
          bg: `url(${startExamBg})`,
          btnText: this.$t('startExam2'),
        }
      } else if (name === 'report') {
        const { status, recordArr } = this.data
        let bg = `url(${finishExamBg})`
        let btnText = this.$t('reExam')
        if (status === 3 && recordArr.length <= 0) {
          bg = `url(${absentExamBg})`
          btnText = ''
        }
        return { bg, btnText }
      }
      return {}
    },
    // 屏幕监控开启
    openScreenException() {
      // 鸿蒙没做适配 暂时让用户进入考试
      return (this.data.screenExceptionVerify > 0 || this.data.screenExceptionTimes > 0) && !getPlatform().isHarmony
    }
  },
  methods: {
    ...mapMutations(['changeEntry']),
    fetch() {
      const { userID, examID, name } = this
      const query = { userID, examID }
      if (name === 'info') {
        return this.getExamInfo(query)
      } else if (name === 'report') {
        return this.getExamReport(query)
      } else if (name === 'details') {
        this.changeEntry(1)
        return this.getExamStatus(query)
      }
    },
    refresh() {
      return this.fetch()
        .then(() => {
          this.status = 'success'
        })
        .catch((e) => {
          this.status = 'failed'
          throw e
        })
    },
    async loadPosenet() {
      if (this.data.enableSnapshotMonitor && !window.posenetBin) {
        window.posenetBin = await posenet.load({
          multiplier: 0.5
        })
      }
    },
    getExamInfo(query) {
      return getExamInfo(query).then(({ data }) => {
        if (data.status === -1) {
          this._exit(this.$t('examNotExist'))
          return
        }
        this.data = data
        console.log(data)
        this.interfaceName = 'info'
        this.loadPosenet()
        return data
      })
    },

    getExamReport(query) {
      return getExamReport(query).then(({ data }) => {
        if (data.status === -1) {
          this._exit(this.$t('examNotExist'))
          return
        }
        this.data = data
        this.interfaceName = 'report'
        return data
      })
    },

    getExamStatus(query) {
      return getExamStatus(query).then(({ data }) => {
        this.examStatus = data
        if (data.status === -1) {
          this._exit(this.$t('examNotExist'))
          return
        }
        if (this.detectStatus()) {
          return this.getExamInfo(query)
        } else {
          return this.getExamReport(query)
        }
      })
    },
    detectStatus() {
      const { hasRecord, status } = this.examStatus
      if (hasRecord) {
        return true
      } else if (status === 3 || status === 5) {
        return false
      } else {
        return true
      }
    },
    submitAnswer() {
      const { examUserID } = this.examStatus
      return repayExam({ examUserID })
        .then(({ data }) => {
          const { status } = data
          this.examStatus = { hasRecord: false, status: -1 }
          this.fetch()
          if (status === 1) {
            // 成功
            this.saveBehavior()
          } else if (status === 2) {
            // 没有答题记录
            this.$createToast({
              type: 'error',
              time: 1000,
              txt: this.$t('noAnswerRecord'),
            }).show()
          } else if (status === 0) {
            // 评分失败
            this.$createToast({
              type: 'error',
              time: 1000,
              txt: this.$t('scoreFailed'),
            }).show()
          }
        })
        .catch((error) => {
          this.$createToast({
            type: 'error',
            time: 1000,
            txt: this.$t('submitFailed') + error,
          }).show()
        })
    },
    saveBehavior() {
      let data = {
        examId: this.examID,
        examUserId: this.examStatus.examUserID,
        behaviorType: 4,
        terminal: 2,
        isForceSubmit: 8,
        deviceInfo: getPlatform().deviceInfo,
        extra: '',
      }
      faceApi.saveBehavior(data)
    },
    // 考前提示注意事项及再次确认
    show() {
      this.$refs.requestextendPopup.show()
    },
    hide() {
      this.$refs.requestextendPopup.hide()
    },
    showInstructions() {
      this.$refs.examInstructionsPopup.show()
      this.$refs.instructions.initTimer()
    },
    hideInstructions() {
      this.$refs.examInstructionsPopup.hide()
    },
    showEnterTips(params) {
      this.$refs.enterTips.init(params)
      this.$refs.enterTipsPopup.show()
    },
    hideEnterTips() {
      this.$refs.enterTipsPopup.hide()
    },
    getScreenRecordState() {
      let status = new Promise(resolve => {
        getScreenRecordState((status) => {
          resolve(status)
        })
        setTimeout(() => {
          resolve(0)
        }, 1000)
      })
      return status
    },
    async nextStep() {
      let isScreenRecord = false
      try {
        if (getPlatform().platform === 'ios' && compareVersion(window.appVersion, '1.9.18') > 0) {
          const status = await this.getScreenRecordState()
          if (status === 1) {
            this.$createDialog({
              type: 'alert',
              content: this.$t('screenRecordTips') + '<br>' + this.$t('screenRecordTips1')
            }).show()
            isScreenRecord = true
          }
        }
      } catch(e) {
        console.log(e)
      }
      if(this.openScreenException && !this.screenServiceChecking) {
        if(compareVersion(window.appVersion, '1.9.63') < 0) {
          this.$createDialog({
            type: 'alert',
            content: this.$t('versionLowerTips')
          }).show()
          return
        }
        this.screenServiceChecking = true
        setTimeout(() => {
          this.screenServiceChecking = false
        }, 200)
        this.checkScreenMonitor()
        return
      }
      if (!isScreenRecord && !this.screenServiceChecking) {
        this.toExam()
      }
    },
    // 考前检测是否迟到
    async checkIsLate() {
      if (this.data.status === 0) {
        const { userID, examID, name } = this
        const query = { userID, examID }
        const { data } = await getExamInfo(query)
        this.data.status = data.status
      } 
      this.detectBeforeExam()
    },
    // 考前检测试卷状态
    detectBeforeExam() {
      const { hasRecord, status, examUserID } = this.examStatus
      if (
        hasRecord &&
        (status === 1 || status === 3 || status === 4) &&
        examUserID > 0
      ) {
        this.$createDialog({
          type: 'alert',
          maskClosable: true,
          content:
            '<p class="exam-recordtip">' + this.$t('examDetailTip1') + '</p>',
          confirmBtn: { text: this.$t('submitExam') },
          onConfirm: () => {
            this.submitAnswer()
          },
        }).show()
      } else {
        const { status, currentState, examUserId, enableDeviceMonitor, enableIPMonitor, verificationMethod, enableSnapshotMonitor, openSignCode } = this.data
        if (status === 0) {
          if (!this.showBack && getPlatform().isIOS && getPlatform().isIPadOrMac) {
            this.$createDialog({
              type: 'alert',
              content: this.$t('deviceSupportTip')
            }).show()
            return
          }
          try {
            let orgId = this.user.orgId || -1
            if (!this.showBack && (((getPlatform().isAndroid || getPlatform().isHarmony) && [47, 49, 3469, 3473, 3483, 5167].includes(Number(orgId)) && compareVersion(window.appVersion, '1.9.13') <= 0) || ([49, 67, 75, 3473, 3483, 4005, 3479].includes(Number(orgId) && compareVersion(window.appVersion, '1.9.19') <= 0)) || compareVersion(window.appVersion, '1.9.5') <= 0 || window.isOldApp)) {
              this.$createDialog({
                type: 'alert',
                content: this.$t('versionLowerTips')
              }).show()
              return
            }
          } catch(e) {
            console.log(e)
          }
          this.toExam = () => {
            // 准备进入考试
            if ( verificationMethod && verificationMethod === 1) {
              faceApi
                .getFaceInfo({})
                .then(({ data }) => {
                  if (data && data.url) {
                    if (
                      data.status === 2 ||
                      (this.data.allowUnchecked &&
                        this.data.allowUnchecked === 1)
                    ) {
                      this.$router.push({
                        path: `/face/${this.userID}/${this.examID}/exam`,
                      })
                    } else if (data.status === 1) {
                      this._warnToast(this.$t('photoNotAuthYet')).show()
                    } else if (data.status === 3) {
                      this._warnToast(this.$t('photoNotPassAuth')).show()
                    } else {
                      this._warnToast('未知人脸验证status').show()
                    }
                  } else {
                    this.showEnterTips({
                      title: this.$t('noSkippingTips'),
                      content: this.$t('faceTip10'),
                      isFace: true
                    })
                  }
                })
                .catch((error) => {
                  this.$createToast({
                    type: 'error',
                    time: 1000,
                    txt: error,
                  }).show()
                })
            } else {
              // this.$router.push({
              //   name: 'exam',
              //   params: { userID: this.userID, examID: this.examID },
              // })
              goPage(this, {
                name: 'exam',
                params: { userID: this.userID, examID: this.examID },
              })
            }
          }

          this.continueExam = () => {
            if ((examUserId && (enableIPMonitor || enableDeviceMonitor)) || openSignCode) {
              terminalCheck({
                examUserId,
                examId: this.examID,
                terminalId: window.deviceID
              })
              .then(({data}) => {
                console.log(data)
                if (openSignCode && !data.signStatus) {
                  this._warnToast(this.$t('examSigninTip')).show()
                  return
                }
                if (data.signTerminalChanged && openSignCode) {
                  this.showEnterTips({
                    title: this.$t('noSkippingTips'),
                    content: '<p>' + this.$t('examSigninTip1') + '</p>' + '<p>' + this.$t('deviceExceptionTips2') + '</p>'
                  })
                  return
                }
                if (data.ipChanged && enableIPMonitor) {
                  this.showEnterTips({
                    title: this.$t('IPException'),
                    content: '<p>' + this.$t('IPExceptionTips1') + '</p>' + '<p>' + this.$t('IPExceptionTips2') + '</p>'
                  })
                  return
                }
                if (data.terminalChanged && enableDeviceMonitor) {
                  this.showEnterTips({
                    title: this.$t('deviceException'),
                    content: '<p>' + this.$t('deviceExceptionTips1') + '</p>' + '<p>' + this.$t('deviceExceptionTips2') + '</p>'
                  })
                  return
                }
                this.showInstructions()
              })
            } else {
              this.showInstructions()
            }
          }
          var self = this
          if (verificationMethod || enableSnapshotMonitor) {
            if (enableSnapshotMonitor && !this.checkAppVersion()) {
              return 
            }
            checkPermission((res) => {
              self.continueExam()
            }, (e) => {
              if (e == 9) {
                if(!getPlatform().isIOS) { 
                  self.$createDialog({
                    type: 'alert',
                    content: this.$t('mobilePermissionTip1')
                  }).show()
                }
              } else {
                self.$createDialog({
                  type: 'alert',
                  content: this.$t(e)
                }).show()
              }
            })
          } else {
            self.continueExam()  // checkPermission 方法兼容 没有更新版本的用户也能进入考试
          }
          
          // if (getPlatform().isAndroid && false) {
          //   // 多屏算一次离开
          //   multiWindowMode((result) => {
          //     if (result != 1) {
          //       this.toExam()
          //     } else {
          //       this._warnToast(this.$t('plzExitScreen')).show()
          //     }
          //   }, (error) => {
          //     console.error(error)
          //     this.toExam()
          //   })
          // } else {
          //   this.toExam()
          // }
        } else if (status === 3 || status === 1) {
          // 1 迟到 3 未开始
          const txtNew =
            status === 3 ? this.$t('noExamTip1') : this.$t('noExamTip2')
          this._warnToast(txtNew).show()
        } else if (status === 4) {
          // 已结束
          const txt =
            currentState === 3 ? this.$t('noExamTip1') : this.$t('noExamTip2')
          this._warnToast(txt).show()
        } else if (status === 2) {
          // 超出答题次数限制
          this._warnToast(this.$t('noExamTip3')).show()
        } else {
          this._warnToast('未知考试status').show()
        }
      }
    },
    checkAppVersion(version = '1.9.22', message) {
      if (compareVersion(window.appVersion, version) <= 0) {
        this.$createDialog({
          type: 'alert',
          content: message || this.$t('versionLowerTips')
        }).show()
        return false
      }
      return true
    },
    goCheckCamera() {
      if (!this.checkAppVersion()) {
        return 
      }
      let self = this
      checkPermission((res) => {
        self.$router.push({
          path: "/cameraPose"
        })
      }, (e) => {
        if (e == 9) {
          if(!getPlatform().isIOS) { 
            self.$createDialog({
              type: 'alert',
              content: this.$t('mobilePermissionTip1')
            }).show()
          }
        } else {
          self.$createDialog({
            type: 'alert',
            content: this.$t(e)
          }).show()
        }
      })
    },
    goSignin() {
      if (!this.checkAppVersion('1.9.24', this.$t('versionLowerTips1'))) {
        return 
      }
      this.$router.push({
        path: `/signin`,
        query: { examId: this.examID }
      })
    },
    back() {
      if (this.$route.query.unitId) {
        exitApp()
        return false
      }
      if (this.name === 'details') {
        updateActiveList()
        exitApp()
        return false
      } else {
        return true
      }
    },
    _warnToast(txt) {
      return this.$createToast({ type: 'warn', time: 1500, txt })
    },
    _exit(txt) {
      this.$createToast({
        type: 'warn',
        time: 1500,
        txt,
        onTimeout: () => {
          if (this.name === 'details') {
            updateActiveList()
            exitApp()
          } else {
            this.$router.back()
          }
        },
      }).show()
    },
    backList() {
      goPage(this, { name: 'examList', query: { userid: this.userID } })
    },
    // 屏幕监控权限的校验
    checkScreenMonitor() {
      startScreenCaptureService()
    },
    // 屏幕监控服务相关回调
    handleCheckScreen(res) {
      // ● resultCode：录屏权限请求结果码
      //   ● 0：获取权限成功，启动录屏服务
      //   ● 1：获取权限失败
      //   ● 2：录屏权限被中断
      //   ● 3：分析成功
      //   ● 4：分析失败，设置了不能检测
      //   ● 5：分析失败，屏幕截图异常
      //   ● 6：分析失败，超过最大上传次数
      // ● resultId：客户端自增的 ID
      // ● id：analyze 方法中前端自定义的回调 ID（如果是自动检测则为 -1）
      // ● similarity：相似度结果（建议将检测结果记录入数据库，便于后续问题分析）
      // ● imageUrl：屏幕截图的图片地址
      // ● abnormal：true 表示异常，false 表示正常
      let tempRes = {}
      try {
        tempRes = JSON.parse(res)
      } catch (error) {
        console.log(error,'error');
      }
      console.log(tempRes,'tempRes');
      switch (tempRes.resultCode) {
        case 0:
          this.toExam()
          break;
        case 1:
          this.$createDialog({
            type: 'alert',
            content: this.$t('screenMonitorTip3'),
            confirmBtn: { text: this.$t('confirm'), active: true }
          }).show()
          break
        default:
          break;
      }
    }
  },
  mounted() {
    this.refresh()
    // 录屏服务监听
    window.onScreenCaptureServiceResult = (res) => {
      this.handleCheckScreen(res)
    }
    window.posenetImage = null
    window.unitId = this.$route.query.unitId
  },
  destroyed() {
    // 退出清空位置信息
    this.$store.commit('changeLocationInfo', {})
  }
}
</script>

<template>
  <Page :title="data.info || data.title" :class="$style.page" @back="back">
    <Loading v-if="status === 'loading'"></Loading>
    <div :class="$style['view-body']" v-if="status === 'success'">
      <Report v-if="interfaceName === 'report'" :data="data" />
      <div :class="$style['scroll-wrapper']">
        <Scroll ref="scroll" :options="options">
          <div :class="$style.container">
            <!-- <div :class="$style.bg" :style="{backgroundImage: ui.bg}"></div> -->

            <Details :desc="data" />
            <div
              :class="$style.security"
              v-if="
                data.enableDeviceMonitor ||
                data.enableIPMonitor ||
                data.verificationMethod ||
                data.leaveLimit || data.leaveLimitTime ||
                data.enableSnapshotMonitor || 
                (data.screenExceptionTimes || data.screenExceptionVerify)
              "
            >
              <h4>{{ $t('examMonitor') }}</h4>
              <div v-if="data.enableDeviceMonitor" :class="$style.item">
                <i class="iconfont icon-shebeijiankong1"></i>
                <i18n path="deviceMonitorTip">
                  <span :class="$style.green" slot="device">{{ $t('deviceMonitor') }}</span>
                </i18n>
              </div>
              <div v-if="data.enableIPMonitor" :class="$style.item">
                <i class="iconfont icon-IPjiankong"></i>
                <i18n path="IPMonitorTip">
                  <span :class="$style.green" slot="ip">{{ $t('IPMonitor') }}</span>
                </i18n>
              </div>
              <div v-if="data.leaveLimit || data.leaveLimitTime" :class="$style.item">
                <i class="iconfont icon-qiepingjiankong"></i>
                <i18n path="screenMonitorTips1" v-if="data.leaveLimit && data.leaveLimitTime">
                  <span :class="$style.green" slot="screen">{{ $t('screenMonitor') }}</span>
                  <span :class="$style.red" slot="num">{{ data.leaveLimit }}</span>
                  <span :class="$style.red" slot="num1">{{ data.leaveLimitTime }}</span>
                </i18n>
                <i18n path="screenMonitorTips" v-else-if="data.leaveLimit">
                  <span :class="$style.green" slot="screen">{{ $t('screenMonitor') }}</span>
                  <span :class="$style.red" slot="num">{{ data.leaveLimit }}</span>
                </i18n>
                <i18n path="screenMonitorTips2" v-else-if="data.leaveLimitTime">
                  <span :class="$style.green" slot="screen">{{ $t('screenMonitor') }}</span>
                  <span :class="$style.red" slot="num">{{ data.leaveLimitTime }}</span>
                </i18n>
              </div>
              <div v-if="data.verificationMethod" :class="$style.item">
                <i class="iconfont icon-renlianjiankong"></i>
                <i18n path="faceRecognitionTip">
                  <span :class="$style.green" slot="face">{{ $t('faceRecognition') }}</span>
                </i18n>
              </div>
              <div v-if="data.screenExceptionTimes || data.screenExceptionVerify" :class="$style.item">
                <i class="iconfont icon-screen-monitor"></i>
                <i18n path="screenMonitorTip4">
                  <span :class="$style.green" slot="screenMonitor1">{{ $t('screenMonitor1') }}</span>
                </i18n>
              </div>
              <div v-if="data.enableSnapshotMonitor" :class="$style.item">
                <i class="iconfont icon-shexiangjiankong"></i>
                <i18n path="cameraMonitorTip">
                  <span :class="$style.green" slot="camera">{{ $t('cameraMonitor') }}</span>
                </i18n>
              </div>
            </div>
            <div :class="$style['exam-tips']">
              <h4>{{ $t('examTip') }}</h4>
              <div v-html="data.examInstructions"></div>
            </div>
          </div>
        </Scroll>
      </div>
      <div :class="$style.btn">
        <UButton
          v-if="data.openSignCode"
          @click="goSignin"
          >{{ $t('examSignin') }}</UButton
        >
        <UButton
          v-if="data.enableSnapshotMonitor"
          @click="goCheckCamera"
          >{{ $t('checkCamera') }}</UButton
        >
        <UButton
          v-if="showBack"
          :class="$style.hasback"
          @click="backList"
          >返回</UButton
        >
        <UButton
          v-if="ui.btnText"
          :primary="true"
          :disabled="data.status !== 0"
          :class="{[$style.hasback]: showBack}"
          @click="checkIsLate"
          >{{ ui.btnText }}</UButton
        >
      </div>
    </div>
    <Toast
      v-else-if="status === 'failed'"
      :visible="true"
      :type="'error'"
      :txt="$t('error')"
      :maskClosable="true"
      :time="0"
      :mask="true"
    />
    <Popup ref="requestextendPopup" :center="true" :mask-closable="true">
      <Hint
        :examID="data.examID"
        :userID="userID"
        @hide="hide"
        :totalCount="parseInt(data.leaveLimit)"
      />
    </Popup>
    <Popup 
      ref="examInstructionsPopup" 
      :center="true">
      <Instructions
        ref="instructions"
        :instructions="data.examInstructions"
        @hide="hideInstructions"
        @nextStep="nextStep"
      />
    </Popup>
    <Popup 
      ref="enterTipsPopup" 
      :center="true">
      <EnterTips
        ref="enterTips"
        :examID="data.examID"
        :userID="userID"
        @hide="hideEnterTips"
      />
    </Popup>
  </Page>
</template>

<style lang="stylus" module>
.page {
  background: url('@/assets/images/list_bj.png') no-repeat 0 0 / cover;
}

.view-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  overflow: hidden;
  color: #444;
}

.scroll-wrapper {
  flex: 1;
  overflow: hidden;
  padding: 12px 0;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;

  :global(.cube-scroll-wrapper) {
    height: 100%;
    width: 100%;
  }
}

.container {
  position: relative;
  // min-height calc(100vh - 65px)
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;

  /.bg {
    width: 265px;
    height: 160px;
    margin: 0 auto;
    background-size: contain;
  }
}

.security {
  padding: 0 10px;
  margin-bottom: 10px;

  h4 {
    font-weight: bold;
  }

  .item {
    padding: 2px 6px;
    margin-top: 6px;
    border: solid 1px #deded9;
    font-size: 12px;
    line-height: 20px;

    .green {
      color: #5abc8c;
    }
    .red{
      color: #ea5947;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
      color: #5abc8c;
    }
  }
}

.exam-tips {
  padding: 0 16px;
  font-size: 15px;
  line-height: 22px;

  .red {
    color: #ea5947;
  }

  h4 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.btn {
  margin-top: 20px;
  // margin-bottom: 22px;
  font-size: 14px;
  text-align: center;

  .tips {
    line-height: 26px;

    a {
      color: #8d77fb;
    }
  }

  :global(.uButton) {
    display: inline-block;
    width: 100%;
    height: 40px;
    padding: 0;
    margin-bottom: 10px;
  }

  :global(.uButton-disabled) {
    border-color: #fff;
  }

  .hasback {
    width: calc(50% - 10px);
    &:last-child {
      margin-left: 20px;
    }
  }
}
</style>
