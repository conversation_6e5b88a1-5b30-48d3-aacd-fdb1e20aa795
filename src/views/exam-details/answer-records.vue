<script>
import { Popup } from 'cube-ui'
export default {
  name: 'answerRecords',
  components: {
    Popup
  },
  props: {
    recordArr: {
      type: Array,
      default () {
        return []
      }
    }
  },
  methods: {
    show () {
      const popup = this.$refs.extendPopup
      popup.show()
    },
    hide () {
      const popup = this.$refs.extendPopup
      popup.hide()
    },
    goto (paperID, examUserID) {
      this.$emit('goto', paperID, examUserID)
    }
  }
}
</script>

<template>
  <Popup ref="extendPopup" :position="'bottom'" :mask-closable="true">
    <div :class="$style.container" @touchmove.stop.passive="() => {}">
      <p :class="$style.title">{{ $t('answerRecord') }}</p>
      <ul>
        <li
          v-for="(item, index) in recordArr"
          @click="goto(item.paperID, item.examUserID)"
          :class="$style.item"
          :key="index"
        >
          {{item.endtime}}
          <span v-if="item.isHigh" :class="$style.highScore">{{ $t('highestScore') }}</span>
          <span :class="$style.score">
            {{ $t('points', { n: item.score }) }}
            <i class="iconfont icon-yemian-xiayiye"></i>
          </span>
        </li>
      </ul>
    </div>
  </Popup>
</template>

<style lang="stylus" module>
.container
  // max-height 70%
  max-height 344px
  overflow auto
  background-color #fff
  -webkit-overflow-scrolling touch

/.title
  height 44px
  font-size $font-size-medium
  line-height 44px
  color $text-color-default
  text-align center

/.item
  height 60px
  padding 0 16px
  line-height 60px
  font-size $font-size-medium
  color $text-color-default
  overflow hidden
  cursor pointer
  border-top 1px solid $color-border-default

  /.highScore
    padding 2px
    font-size $font-size-small
    color #fff
    background-color $color-main
    border-radius 2px
    margin-left 6px

  /.score
    float right
    color $color-main
    font-size $font-size-medium

    i
      margin-left 8px
      color $color-intro
      font-size $font-size-medium
</style>
