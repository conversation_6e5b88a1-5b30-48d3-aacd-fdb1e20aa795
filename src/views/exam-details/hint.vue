<script>
import { Button } from '@/common/components'
export default {
  components: {
    UButton: Button
  },
  props: ['userID', 'examID', 'count', 'totalCount'],
  methods: {
    hide () {
      this.$emit('hide')
    },
    goto () {
      const { userID, examID } = this
      // this.$router.push({ name: 'exam', params: { userID, examID } })
      goPage(this, { name: 'exam', params: { userID, examID } })
    }
  }
}
</script>

<template>
  <div :class="$style.wrapper">
    <h2 :class="$style.title">{{ $t('examTip') }}</h2>
    <ol :class="$style.main">
      <li v-if="totalCount > 0">{{ $t('examTip1') }}<strong>{{ $t('examTip2') }}</strong>{{ $t('examTip3') }}<strong>{{ $t('examTip4', { num: totalCount}) }}</strong>。</li>
      <li>{{ $t('examTip6') }}</li>
      <li>{{ $t('examTip7') }}</li>
      <li>{{ $t('examTip8') }}</li>
      <li>{{ $t('examTip9') }}</li>
    </ol>
    <div :class="$style.btns">
      <div :class="$style.btn">
        <UButton :radius="true" @click="hide">{{ $t('cancel') }}</UButton>
      </div>
      <div :class="$style.btn">
        <UButton :primary="true" :radius="true" @click="goto">{{ $t('startExam') }}</UButton>
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.wrapper
  background-color #fff
  padding 0 16px 30px
  border-radius 4px
  margin 0 30px

.title
  height 50px
  margin 0
  font-size $font-size-medium-x
  line-height 50px
  color $text-color-default
  font-weight bold
  text-align center
  border-bottom 1px solid $color-border-default
.main
  padding 16px 0
  // border-top 1px solid $color-border-default
  counter-reset counter 0

  > li
    margin-bottom 4px
    font-size $font-size-medium
    line-height 1.4285
    color $text-color-default

    &::before
      counter-increment counter 1
      content counter(counter) '.'
      color $color-main
  strong
    color #f00
    font-style normal
    font-weight normal

.btns
  display flex
  margin-top 20px
  justify-content space-between

  /.btn
    flex 1
    margin-right 10px
    // pointer-events none

  /.btn:last-child
    margin-right 0
</style>
