<template>
  <div>
    <div :class="$style.main">
      <p :class="$style.title">{{ $t('examTime') }}</p>
      <p v-for="(relation, index) in desc.relationArr" :class="$style.time" :key="index">
        <i class="iconfont icon-shichang1"></i>
        {{ $t('betweenTime', { startTime: formatStartDate(relation.startDate), endTime: formatEndDate  (relation.endDate) }) }}
        <br><span v-if="relation.examRoomName">{{ $t('studentExamRoom', {n : relation.examRoomName}) }}</span>
        <span v-if="relation.seatNo">{{ $t('seatNumber', {n : relation.seatNo}) }}</span>
      </p>
      <p :class="$style.title">{{ $t('examInfo') }}</p>

      <ul :class="$style.items">
        <li><label>{{ $t('passingScore') }}</label><p>{{ $t('points', { n: desc.passScore }) }}</p></li>
        <li><label>{{ $t('examLimitTime') }}</label><p>{{ $t('minutes', { n: desc.examTime }) }}</p></li>
        <li v-if="desc.isLimitTimes==='0' && !desc.joinNum"><label>{{ $t('answerTime1') }}</label><p>{{ $t('unlimited') }}</p></li>
        <li v-else-if="desc.isLimitTimes==='0' && desc.joinNum>0"><label>{{ $t('answerTime1') }}</label><p>{{ $t('timesOf', { current: desc.joinNum || 0, total: 'N'}) }}</p></li>
        <li v-else-if="desc.isLimitTimes==='1'"><label>{{ $t('answerTime1') }}</label><p>{{ $t('timesOf', { current: desc.joinNum || 0, total: desc.limitTimes }) }}</p></li>
        
        <!-- <li v-if="desc.isLate ==='0'"><label>{{ $t('lateTime') }}</label><p>{{ $t('unlimited2') }}</p></li>
        <li v-else-if="desc.isLate ==='1'"><label>{{ $t('lateTime') }}</label><p>{{ $t('minutes', { n: '< ' + desc.lateTime }) }}</p></li> -->
      </ul>
      <div :class="$style.limit" v-if="desc.lateTime > 0">
        <i class="iconfont icon-menu_int"></i>
        <span>{{$t('lateLimit')}}</span>
        <span>{{$t('lateLimitTips',{time:desc.lateTime})}}</span>
      </div>
      <div :class="$style.limit" v-if="desc.commitPaperTime > 0">
        <i class="iconfont icon-menu_int"></i>
        <span>{{$t('submitLimit')}}</span>
        <span>{{$t('submitLimitTips',{time:desc.commitPaperTime})}}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils'
export default {
  props: ['desc', 'name'],
  methods: {
    formatStartDate (d) {
      return formatDate(d, 'yyyy-MM-dd HH:mm')
    },
    formatEndDate (d) {
      return formatDate(d, 'MM-dd HH:mm')
    }
  }
}
</script>
<style lang="stylus" module>
.main
  padding 10px 16px
  border-radius 4px
  background-color #fff
  /.title
     margin-bottom 6px
     font-weight bold
     font-size $font-size-medium-x
     color $text-color-default
  /.time
    margin-bottom 10px
    font-size 15px
    color $text-color-default
    line-height 22px
    &:last-child
      margin-bottom 30px
    i
      margin-right 4px
      color $color-intro
    span
      padding-left: 22px
      color: $color-main
  /.items
    display flex
    margin 15px -4px 10px -4px
    > li
      flex 1
      text-align center
      border 1px solid $color-border-default
      margin 0 4px
      padding 12px 0
      label
       font-size 12px
       line-height 18px
      i
       font-size 25px
       color $color-main
      p
       margin-top 8px
       font-size 14px
       color $color-main
.dateIcon
  color #e4e4f4!important
.limit
  font-size 12px
  line-height 20px
  i
    font-size 12px
    margin-right 4px
</style>
