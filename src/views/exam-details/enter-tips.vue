<script>
import { But<PERSON> } from "@/common/components";
export default {
  components: {
    UButton: Button,
  },
  props: ["userID", "examID"],
  data() {
    return {
      title: '',
      content: '',
      isFace: false
    }
  },
  methods: {
    init(params) {
      const { title, content, isFace } = params;
      this.title = title
      this.content = content
      this.isFace = isFace
    },
    goAuth() {
      this.$router.push({
        path: `/face/${this.userID}/0/authentication`,
      })
    },
    goCode() {
      this.$router.push({
        path: `/face/${this.userID}/${this.examID}/code`,
      })
    },
    close() {
      this.$emit("hide");
    }
  },
};
</script>

<template>
  <div
    class="tips-content"
    @touchmove.stop.passive="() => {}"
  >
    <div class="header">
      <h2 class="title">{{ title }}</h2>
      <i
        class="iconfont icon-yemian-guanbi"
        @click="close"
      ></i>
    </div>
    <div class="content">
      <div class="scroll-wrap">
        <div
          class="tips"
          v-html="content"
        ></div>
      </div>
      <div class="bottom-area">
        <UButton
          v-if="!isFace"
          @click="close"
        >{{ $t('getIt') }}</UButton>
        <UButton
          v-else
          @click="goAuth"
        >{{ $t('goAuth') }}</UButton>
        <UButton
          @click="goCode"
        >{{ $t('examTip17') }}</UButton>
      </div>
    </div>
  </div>
</template>

<style lang="stylus">
.tips-content {
  position: relative;
  margin: 0 10%;
  background: #FFFFFF;
  box-shadow: 0 2px 34px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;

  .header {
    padding: 10px;
    border-radius: 8px 8px 0 0;
    text-align: center;

    .title {
      font-size: 18px;
      line-height: 30px;
    }

    .iconfont {
      position: absolute;
      top: 8px;
      right: 4px;
      padding: 6px;
      font-size: 18px;
    }
  }

  .content {
    padding: 16px 20px 30px;
    color: #444;

    .scroll-wrap {
      margin: 0 -10px 20px 0;
      max-height: 363px;
      padding-right: 10px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .tips {
      font-size: 14px;
      color: #444444;
      line-height: 21px;
    }

    .bottom-area {
      .uButton {
        padding: 10px;
        color: #ea5947;
        &:first-child {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
