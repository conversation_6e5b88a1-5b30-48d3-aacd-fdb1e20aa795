<template>
  <Page :title="$t('examCode')" class="page-code">
    <div ref="body" class="view-body">
      <div class="input-tips">{{ $t('enterCode') }}</div>
      <div class="code-tips">{{ $t('enterCodeTip') }}</div>
      <form name="code" ref="form" class="input-group">
        <input
          v-for="(item, index) in [1, 2, 3, 4]"
          :key="index"
          type="text"
          pattern="\d*"
          @keyup="codeKeyup"
          @keydown="codeKeydown"
          :data-index="index"
          required
          min="0"
          max="9"
          minlength="1"
          maxlength="1"
          onpaste="return false"
        />
        <div v-if="codeWarning.show" class="warning">
          {{ codeWarning.text }}
        </div>
      </form>
    </div>
  </Page>
</template>
<script>
import { Page } from '@/common/components'
import { signInByCode } from '@/api'
export default {
  components: {
    Page,
  },
  data() {
    return {
      codeWarning: {
        show: false,
        text: this.$t('codeWrong'),
      },
    }
  },
  mounted() {
    setTimeout(() => {
      this.$refs.form.querySelectorAll('input')[0].focus()
    }, 300)
  },
  methods: {
    codeKeyup(event) {
      if (event.keyCode !== 8) {
        if (event.target.dataset.index === '3') {
          this.signin()
        } else {
          event.target.nextElementSibling.focus()
        }
      }
      this.codeWarning.show = false
      if (event.target.value.length > 0) {
        event.target.value = event.target.value.substring(
          event.target.value.length - 1
        )
      }
    },
    codeKeydown(event) {
      if (event.keyCode === 8) {
        // eslint-disable-next-line
        if (event.target.value == '') {
          event.target.previousElementSibling.value = ''
          event.target.previousElementSibling.focus()
        }
      }
    },
    signin() {
      let inputs = this.$refs.form.querySelectorAll('input')
      let code = ''
      Array.prototype.forEach.call(inputs, (input) => {
        code += input.value
      })
      let params = {
        verificationCode: code,
        ...this.$store.state.signInParams,
      }
      const toast = this.$createToast({
        time: 0,
        txt: this.$t('signingIn'),
      })
      toast.show()
      signInByCode(params)
        .then(({ data }) => {
          toast.hide()
          if (data.code === 1) {
            this.$router.back()
          } else {
            this.codeWarning.text = data.message || this.$t('codeWrong')
            this.codeWarning.show = true
            this.$saveLog(
              `考试验证码签到失败_${JSON.stringify(params)}_${JSON.stringify(
                data
              )}`
            )
          }
        })
        .catch((error) => {
          toast.hide()
          this.codeWarning.text = this.$t('codeWrong')
          this.codeWarning.show = true
          this.$saveLog(
            `考试验证码签到失败_${JSON.stringify(params)}_${JSON.stringify(
              error
            )}`
          )
        })
    },
  },
  destroyed() {
    // 清除缓存中的签到信息
    this.$store.commit('saveSignInParams', {})
  },
}
</script>
<style lang="stylus">
.page-code {
  font-size: 14px;
  color: #444;
  background-color: #f5f5f5;
  .view-body {
    height: 100%;
    padding: 45px 35px 0;
    overflow: auto;
    text-align:left;
    .input-tips {
      font-size: 24px;
      font-weight: bold;
      line-height: 32px;
    }
    .code-tips {
      margin-top: 8px;
      line-height: 20px;
      color: #444;
    }
    .input-group {
      width: 220px;
      margin: 50px auto 0;
      input {
        font-size: 32px;
        line-height: 40px;
        font-weight: bold;
        border-bottom: solid 2px #444;
        width: 35px;
        padding:12px 0;
        margin: 0 10px;
        outline: none;
        text-align: center;
        background-color: transparent;
        color: #444;
        border-radius: 0;
      }
    }
    .warning {
      padding: 0 10px;
      margin-top: 15px;
      color: #f60000;
      line-height: 20px;
      text-align: left;
    }
  }
}
</style>
