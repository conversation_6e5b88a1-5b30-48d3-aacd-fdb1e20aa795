<template>
  <Page
    :title="$t('examSignin')"
    @back="back"
  >
    <div
      class="signin-body"
      v-if="!isLoading"
    >
      <div class="exam-info">
        <p class="exam">
          <span class="gray">{{$t('exam')}}</span>{{ info.examTitle }}
        </p>
        <p class="student">
          <span class="gray">{{$t('student')}}</span>{{ user.name }}
          <span v-if="user.studentId">({{ user.studentId }})</span>
        </p>
      </div>
      <div
        class="can-sigin-in"
        v-if="step === 1"
      >
        <div
          class="scan-btn"
          @click="scanQRCode"
        >
          <p class="iconfont icon-saoma"></p>
          <p>{{$t('scanSignIn')}}</p>
        </div>
        <Button class="location-area" icon="iconfont icon-juli" type="info" @click="goPosition">
          {{ posLoading ? $t('getPos') : posError ? $t('getPosAgain') : locationInfo.location }}
        </Button>
        <div class="intro">
          <p>{{$t('scanSignTip')}}</p>
          <p>{{$t('scanSignTip1')}}</p>
          <p>{{$t('cannotScanTip')}}<span class="use-code" @click="goCodePage">{{$t('useCodeSignin')}}&gt;&gt;</span></p>
        </div>
      </div>
      <div
        class="cannot-sigin-in gray"
        v-if="step === 2"
      >
        {{$t('signTip')}}
      </div>
      <div
        class="sigined-in"
        v-if="step === 3"
      >
        <div class="result">
          <p class="iconfont icon-chenggong"></p>
          <p>{{$t('signedIn')}}</p>
        </div>
        <div
          class="location-info intro"
          v-if="info.signInfo"
        >
          <p>{{$t('signTime')}}{{ info.signInfo.checkTime | formatTime}}</p>
          <p v-if="info.signInfo.location">{{$t('signLocation')}}{{ info.signInfo.location }}</p>
        </div>
      </div>
    </div>
  </Page>
</template>
<script>
import { Toast } from "cube-ui";
import { Page, Button } from '@/common/components'
import { scanQRCode, getLocation, exitApp } from '@/lib/cordovaPlugin/mobilePlugin.js'
import { getSignStatus, signIn } from '@/api'
import { getPlatform, formatDate } from '@/common/utils'
import { getHashParam, getDeviceVersion } from '@/utils/utils'
export default {
  name: 'signin',
  components: {
    Page,
    Button
  },
  data() {
    return {
      info: {},
      step: 0,
      isLoading: true,
      posLoading: false,
      posError: ''
    }
  },
  filters: {
    formatTime(time) {
      if (!time) {
        return "--";
      }
      return formatDate(time, "yyyy-MM-dd HH:mm:ss");
    },
  },
  computed: {
    examId() {
      return this.$route.query.examId
    },
    code() {
      return this.$route.query.code
    },
    user() {
      return this.$store.state.user
    },
    locationInfo() {
      return this.$store.state.locationInfo
    }
  },
  watch: {
    $route() {
      this.getData()
    },
    code() {
      this.getData()
    },
    locationInfo: {
      handler(val) {
        // app扫一扫进入获取定位直接签到
        if (this.code && val.location) {
          this.confirmSignin(this.code)
        }
      },
      immediate: true
    }
  },
  activated() {
    console.log("activated", this.examId)
    this.getData()
  },
  created() {
    console.log(this.examId)
    this.getData()
  },
  methods: {
    getData() {
      this.isLoading = true
      // 调后台接口
      getSignStatus({
        examId: this.examId
      }).then(({ data }) => {
        if (data.code === 1 && data.result) {
          this.info = data.result
          this.step = data.result.signInStatus ? 3 : data.result.examBatchHasEnd ? 2 : 1
          this.isLoading = false
          if (this.step === 1 && !this.locationInfo.location) {
            this.getPos()
          }
        }
      }).catch(() => {
        this.isLoading = false
      })
    },
    getPos() {
      this.posLoading = true
      if (getPlatform().inApp) {
        const _this = this
        getLocation((res) => {
          _this.$store.commit('changeLocationInfo', {
            lng: res.longitude,
            lat: res.latitude,
            location: res.poiList[0] || res.addr
          })
          _this.posLoading = false
        }, function() {
          _this.posLoading = false
          _this.posError = _this.$t('getPosAgain')
        });
      }
    },
    _toast(message, type = "error") {
      this.$createToast({
        type: type,
        time: 1500,
        txt: message
      }).show();
    },
    confirmSignin(code) {
      let params = {
        lnglat: Number(this.locationInfo.lng).toFixed(6) + "," + Number(this.locationInfo.lat).toFixed(6),
        location: this.locationInfo.location,
        signCode: code,
        terminal: 2,
        deviceInfo: getDeviceVersion(),
        terminalId: window.deviceID,
        examId: this.examId
      }
      if (!params.location || !params.lnglat) {
        this._toast(this.$t('signinFail1'))
        return 
      }
      const toast = this.$createToast({
        time: 0,
        txt: this.$t('signingIn')
      })
      toast.show()
      signIn(params).then(({ data }) => {
        toast.hide()
        if (data.code === 1) {
          this._toast(this.$t('signinSuccess'), 'correct')
          this.getData()
        } else {
          this._toast(data.message || this.$t('signinFail'))
          this.$saveLog(`考试签到失败_${JSON.stringify(params)}_${JSON.stringify(data)}`)
        }
      }).catch((error) => {
        toast.hide()
        this._toast(this.$t('signinFail'))
        this.$saveLog(`考试签到失败_${JSON.stringify(params)}_${JSON.stringify(error)}`)
      })
    },
    scanQRCode() {
      if (getPlatform().inApp) {
        scanQRCode(1, (res) => {
          const scanUrl = `${location.href.replace(location.hash, '').replace(location.search, '')}#/scanSignin`
          if (res && res.indexOf(scanUrl) === 0) {
            const code = getHashParam("code", res)
            this.confirmSignin(code)
          } else {
            this._toast(this.$t('scanTip'))
          }
        }, (err) => {
          this._toast(err)
        });
      }
    },
    goPosition() {
      this.$router.push({
        path: '/position'
      })
    },
    goCodePage() {
      this.$store.commit('saveSignInParams', {
        lnglat: Number(this.locationInfo.lng).toFixed(6) + "," + Number(this.locationInfo.lat).toFixed(6),
        location: this.locationInfo.location,
        terminal: 2,
        deviceInfo: getDeviceVersion(),
        terminalId: window.deviceID,
        examId: this.examId
      })
      this.$router.push({
        path: '/signinByCode'
      })
    },
    back() {
      if(this.$route.name === 'scanSignin') {
        exitApp()
      } else {
        this.$router.back()
      }
      return false
    }
  }
}
</script>
<style lang="stylus">
.signin-body {
  padding: 16px;

  .gray {
    color: $text-color-intro;
  }

  .intro {
    font-size: 14px;
    line-height: 1.5;
    color: #444444;
    .use-code {
      color: #8D77FB;
      cursor: pointer;
    }
  }

  .exam-info {
    padding: 16px;
    background: $overall-bg;
    box-shadow: 0px 2px 4px 0px rgba(222, 222, 222, 0.25);
    border-radius: 4px;
    border: 1px solid $color-border-default;
    font-size: 14px;
    line-height: 1.5;

    p {
      margin: 0;

      &.exam {
        margin-bottom: 8px;
      }

      span {
        margin-right: 8px;
      }
    }
  }

  .can-sigin-in {
    .scan-btn {
      margin: 70px auto 52px;
      width: 157px;
      height: 157px;
      padding-top: 35px;
      font-size: 16px;
      text-align: center;
      color: #ffffff;
      border-radius: 50%;
      background: linear-gradient(225deg, #F97652 0%, #EA5947 100%);
      box-shadow: 0px 0px 10px 1px rgba(234, 89, 71, 0.6);
      cursor: pointer;

      .iconfont {
        padding-bottom: 24px;
        font-size: 34px;
        line-height: 1;
      }
    }
    .location-area {
      margin-bottom: 24px;
      height: 46px;
      padding: 0 22px;
      line-height 46px;
      color: #9176FF;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
      background-color: #F5F5F5;
      border-radius: 25px;
      border: none;
      .iconfont {
        font-size: 14px;
      }
    }
  }

  .cannot-sigin-in {
    margin-top: 128px;
    font-size: 16px;
    text-align: center;
  }

  .sigined-in {
    .result {
      margin: 56px 0 68px;
      font-size: 24px;
      text-align: center;
      color: #444444;

      .iconfont {
        padding-bottom: 24px;
        font-size: 56px;
        line-height: 1;
        color: #1DBF88;
      }
    }

    .location-info {
      border-top: 1px solid #979797;
      padding-top: 16px;
    }
  }
}
</style>
