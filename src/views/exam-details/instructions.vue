<script>
import { Toast } from "cube-ui";
import { Button } from "@/common/components";
export default {
  components: {
    UButton: Button,
  },
  props: ["instructions"],
  data() {
    return {
      isAgree: false,
      timer: null,
      seconds: 5,
    };
  },
  computed: {
    btnText() {
      return this.seconds > 0 ? `${this.seconds}s` : this.$t("startExam2");
    },
  },
  methods: {
    initTimer() {
      this.timer = setInterval(() => {
        this.seconds--;
        if (this.seconds <= 0) {
          this.destroyTimer("timer");
        }
      }, 1000);
    },
    destroyTimer(name) {
      if (this[name]) {
        clearInterval(this[name]);
        this[name] = null;
      }
    },
    closeInstructions() {
      this.$emit("hide");
      this.seconds = 5;
      this.isAgree = false;
      this.destroyTimer("timer");
    },
    nextStep() {
      if (this.seconds > 0) {
        return;
      } else if (!this.isAgree) {
        this.$createToast({
          type: "warn",
          time: 1000,
          txt: this.$t("agreenTip"),
        }).show();
      } else {
        this.$emit("nextStep");
      }
    },
  },
};
</script>

<template>
  <div
    class="instructions-content"
    @touchmove.stop.passive="() => {}"
  >
    <div class="header">
      <h2 class="title">{{ $t('examTip') }}</h2>
      <i
        class="iconfont icon-yemian-guanbi"
        @click="closeInstructions"
      ></i>
    </div>
    <div class="content">
      <div class="scroll-wrap">
        <div
          class="instructions"
          v-html="instructions"
        ></div>
      </div>
      <div class="bottom-area">
        <label class="checkbox">
          <input
            class="input"
            type="checkbox"
            v-model="isAgree"
          >
          <i
            class="iconfont icon icon-checkbox"
            :class="{'checked': isAgree}"
          ></i>
          <p class="label">{{ $t('agreenInstructions') }}</p>
        </label>
        <UButton
          class="btn"
          :class="{'disabled': seconds > 0}"
          :radius="true"
          @click="nextStep"
        >{{ btnText }}</UButton>
      </div>
    </div>
  </div>
</template>

<style lang="stylus">
.instructions-content {
  position: relative;
  margin: 0 10%;
  background: #FFFFFF;
  box-shadow: 0 2px 34px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;

  .header {
    padding: 10px;
    background: url('~@/assets/images/<EMAIL>') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 8px 8px 0 0;
    text-align: center;

    .title {
      font-size: 18px;
      line-height: 30px;
      color: #FFFFFF;
    }

    .iconfont {
      position: absolute;
      top: 4px;
      right: 4px;
      padding: 6px;
      font-size: 12px;
      color: #FFFFFF;
    }
  }

  .content {
    padding: 16px 20px 30px;
    color: #444;

    .scroll-wrap {
      margin: 0 -10px 14px 0;
      min-height: 212px;
      max-height: 363px;
      padding-right: 10px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .instructions {
      font-size: 14px;
      color: #444444;
      line-height: 21px;
    }

    .bottom-area {
      .checkbox {
        margin-bottom: 14px;
      }

      .btn {
        padding: 10px 16px;
        background-image: linear-gradient(45deg, #F97652 0%, #EA5947 100%);
        box-shadow: 0 5px 10px 0 rgba(234, 89, 71, 0.3);
        border-radius: 6px;
        font-size: 18px;
        color: #FFFFFF;

        &.disabled {
          background: #DEDEDE;
          color: #969696;
          box-shadow: none;
        }
      }
    }
  }
}

.checkbox {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;

  .input {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .icon {
    flex: 0 0 auto;
    margin-right: 8px;
    width: 16px;
    height: 16px;
    line-height: 1.2;
    font-size: 12px;
    text-align: center;
    color: #ffffff;
    background: #fff;
    border: 1px solid #e3e3e9;
    border-radius: 4px;

    &::after {
      content: '\e83d';
      font-family: 'iconfont';
    }

    &.checked {
      background-color: $color-main;
      border-color: $color-main;
    }
  }

  .label {
    font-size: 14px;
    color: #444444;
    line-height: 21px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }
}
</style>
