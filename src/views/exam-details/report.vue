<script>
import { createAPI } from 'cube-ui'
import { Avatar, Button } from '@/common/components'
import { getUser } from '@/lib/cordovaPlugin/mobilePlugin'

import Vue from 'vue'
import answerRecords from './answer-records'
import { getUrlParam } from '@/utils/utils'

createAPI(Vue, answerRecords, [], true)

const createRecords = (VM, ...args) => {
  return VM.$createAnswerRecords(...args)
}
export default {
  components: {
    Avatar,
    UButton: Button
  },
  props: ['data'],
  data () {
    return {
      userInfo: {}
    }
  },
  computed: {
    examType () {
      const { status, recordArr, showExamRecord, showTime, showExamRecodAfterDate, joinNum } = this.data

      // 考试已结束,并且用户没有考试记录,并且joinNum为0,认为用户缺考
      if (status === 3 && recordArr.length <= 0 && joinNum == 0) {
        return { status: false, tip: this.$t('examEnd'), publishTime: this.$t('notJoined') }
      }
      if (showExamRecord === 3) {
        // 根据showTime 决定显示examFinish的值
        if (showExamRecodAfterDate == 999) {
          // 几天后公布
          return { status: false, tip: this.$t('finishExam'), publishTime: this.$t('showAfterBatchEnd') }
        }
        if (showTime) {
          // 几天后公布
          return { status: false, tip: this.$t('finishExam'), publishTime: this.$t('viewScore') }
        } else {
          // 不公布
          return { status: false, tip: this.$t('finishExam'), publishTime: this.$t('notViewScore') }
        }
      } else if (showExamRecord === 1) {
        // 显示examFinish字段的值
        return { status: true }
      }
    }
  },
  created () {
    console.log(this)
    if (getUrlParam('mp') !== '1') {
      getUser(user => {
        this.userInfo = user
      })
    } else {
      this.userInfo = this.$store.state.user
    }
  },
  methods: {
    gotoRecord () {
      let recordArr = this.data.recordArr
      if (recordArr.length === 1) {
        this.goto(recordArr[0].paperID, recordArr[0].examUserID)
      } else {
        // this.$refs.extendPopup.show()
        createRecords(this, {
          $props: {
            recordArr: this.data.recordArr
          },
          $events: {
            'goto': this.goto
          }
        }).show()
      }
    },
    goto (paperID, examUserID) {
      // this.$router.push({
      //   name: 'examReport',
      //   params: { examUserID, type: this.data.examFinish, userID: this.userInfo.userID }
      // })
      goPage(this, {
        name: 'examReport',
        params: { examUserID, type: this.data.examFinish, userID: this.userInfo.userID },
        query: { from: this.$route.name, id: this.$route.params.examID }
      })
    }
  }
}
</script>

<template>
  <div>
    <div :class="$style.zone">
      <!-- 不显示答题记录 成绩 答案等 -->
      <div :class="$style.late" v-if="examType.status === false ">
        <p :class="$style.lateTip">{{examType.tip}}</p>
        <p :class="$style.publishTime">{{data.showTime}}{{examType.publishTime}}</p>
      </div>
      <!-- 答题记录 成绩 答案等至少显示其一 -->
      <div :class="$style.complete" v-else>
        <div :class="$style.info">
          <div :class="$style.userInfo">
            <div :class="$style.userAvatar">
              <Avatar :user="userInfo"/>
            </div>
            <span :class="$style.userName">{{userInfo.name}}</span>
          </div>
          <i18n v-if="data.examFinish.includes('1')" tag="div" path="points" :class="$style.score">
            <span :class="$style.userScore" slot="n">{{data.score}}</span>
          </i18n>
        </div>
        <span v-if="data.examFinish.includes('1') && data.remarking" :class="$style.remarking">{{ $t('scoring') }}</span>
        <!-- 显示答题记录 -->
        <div
          v-if="data.examFinish.includes('1') && data.examFinish.includes('2')"
          :class="[$style.tip, $style.recordBtn]"
          @click="gotoRecord"
        >
          <UButton radius>{{ $t('answerRecord') }}
            <span v-if="data.recordArr.length > 1">
              <em :class="$style.recordBtnSplit">|</em>{{data.recordArr.length}}
            </span>
          </UButton>
        </div>
        <!-- 不显示答题记录 -->
        <p v-else :class="$style.tip">{{ $t('notViewAnswer') }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.zone
  background url('@/assets/images/score_bj.png')
  margin-bottom 8px
  background-size cover
  border-radius 4px
  padding-top 10px
  color #fff

  /.late
    text-align center
    padding-bottom 30px

    /.lateTip
      font-size 20px
      margin-bottom 8px
      line-height 1em

    /.publishTime
      font-size $font-size-medium
      line-height 1em

.info
  display flex
  justify-content space-between
  align-items center
  padding 0 30px 4px
  position relative

  /.userInfo
    display flex
    align-items center

  /.userAvatar
    width 40px
    height 40px
    margin-right 16px

  /.userAvatar :global(.ul-avatar)
    width 100%
    height 100%

  /.userName
    font-size $font-size-medium

  /.score
    font-size 14px;

  /.userScore
    font-size 32px
    line-height 1

  /.scoreUnit
    font-style normal
    font-size $font-size-medium

  /.remarking
    display block
    font-size 12px
    font-style normal
    line-height 1em
    margin-top 4px
    padding 0 30px 10px

  /.tip
    font-size $font-size-medium
    border-top 2px dashed #feafb8
    margin 0 16px
    padding 16.5px 0
    text-align center

  /.recordBtn
    padding 16px 0

    :global(.uButton)
      width auto
      padding 8px 16px
      margin 0 auto
      color $color-main
      border-color #fff

    /.recordBtnSplit
      vertical-align top
      margin 0 4px
      font-style normal
      color #e3e3e9
</style>
