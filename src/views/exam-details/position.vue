<template>
  <Page
    class="scan-page"
    :title="$t('signPosition')"
  >
    <div class="scan-body">
      <div v-if="locationAuth">
        <div class="mapWrapper">
          <baidu-map
            class="map"
            :center="circlePath.center"
            :zoom="zoom"
            :scoll-wheel-zoom="false"
            @ready="handler"
          >
            <bm-marker
              :key="resetkey"
              :position="position"
              :dragging="false"
            >
            </bm-marker>
          </baidu-map>
        </div>
        <div class="position">
          <div class="signPosition">
            <i class="iconfont icon-juli icon"></i>
            <span>{{ $t("signPosition") }}</span>
          </div>
          <div class="posName">
            <span>{{addr}}</span>
            <i
              @click="freshPos"
              class="iconfont icon-shuaxin- icon"
            ></i>
          </div>
        </div>
        <section class="morePos">
          <div class="tipName">
            {{ $t("surround") }}
            <span
              @click="showLocationWrongTip"
              class="locationWrong"
            >
              <i class="iconfont icon-bangzhu01"></i>
              <span>{{ $t("locationWrong") }}</span>
            </span>
          </div>
          <div class="morePosLi">
            <div
              @click="switchPos(item)"
              v-for="item in addrData.surroundingPois"
              :key="item.uid"
              class="posItem"
            >{{item.title}}</div>
          </div>
        </section>
        <div class="panel">
          <Button
            :primary="true"
            @click="confirmPosition"
          >{{ $t("confirm") }}</Button>
        </div>
      </div>
      <div class="pos-error" v-else>
        <div class="text">{{$t('openPos')}}</div>
        <Button @click="getPos">{{$t('getPosAgain')}}</Button>
      </div>
    </div>
  </Page>
</template>
<script>
import { Page, Button } from '@/common/components'
import { getPlatform } from '@/common/utils'
import { getLocation } from '@/lib/cordovaPlugin/mobilePlugin.js'
import Vue from 'vue'
import BaiduMap from 'vue-baidu-map'
Vue.use(BaiduMap, {
  // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
  ak: 'hwmqezPntaLpKQ71bfOEzmDrt5kdbj3X'
})
export default {
  name: 'position',
  components: {
    Page,
    Button
  },
  data() {
    return {
      locationAuth: true,
      map: null,
      BMap: null,
      // 图标定位点
      position: {
        lng: 0,
        lat: 0
      },
      // 原定位点
      circlePath: {
        center: {
          lng: 0,
          lat: 0
        },
        radius: 200
      },
      zoom: 17,
      resetkey: new Date().getTime(),
      addrData: {},
      addr: '',
    };
  },
  computed: {
    locationInfo() {
      return this.$store.state.locationInfo
    }
  },
  methods: {
    // 初始化地图
    handler({ BMap, map }) {
      this.BMap = BMap;
      this.map = map;
      this.getPos(true)
    },
    dragend({ type, target, pixel, point, isInit }) {
      const _this = this
      let dis = this.map.getDistance(this.getN(this.circlePath.center), point)
      console.log("dis", dis)
      if (dis <= 100) {
        this.position = point
        const gc = new this.BMap.Geocoder()
        gc.getLocation(point, function (rs) {
          _this.addrData = rs
          if (!_this.addrData.surroundingPois || _this.addrData.surroundingPois.length === 0) {
            _this.addrData.surroundingPois = [{
              title: rs.address,
              uid: 0
            }]
          }
          if (isInit && _this.locationInfo.lng) {
            const { lng, lat, location } = _this.locationInfo
            _this.position = { lng, lat }
            _this.addr = location
          } else {
            _this.addr = rs.address
          }
        })
        this.resetkey = new Date().getTime()
      } else {
        this.freshPos()
      }
    },
    getN(pos) {
      return new BMap.Point(pos.lng, pos.lat)
    },
    getPos(isInit) {
      if (getPlatform().inApp) {
        getLocation(
          (res) => {
            this.locationAuth = true;
            this.position = {
              lng: res.longitude,
              lat: res.latitude
            }
            this.circlePath.center = this.position
            this.mapKey = new Date().getTime()
            this.dragend({
              point: this.getN(this.circlePath.center),
              isInit: isInit
            })
            console.log("this.position", this.position)
          },
          (err) => {
            this.locationAuth = false;
          }
        );
      }
    },
    freshPos() {
      this.getPos()
      this.dragend({
        point: this.getN(this.circlePath.center)
      })
    },
    switchPos(item) {
      console.log(item)
      this.addr = item.title
      this.position = item.point
      this.resetkey = new Date().getTime()
    },
    confirmPosition() {
      this.$store.commit('changeLocationInfo', {
        lng: this.position.lng,
        lat: this.position.lat,
        location: this.addr,
      })
      this.$router.back()
    },
    showLocationWrongTip() {
      this.$createDialog({
        type: 'alert',
        content: `<p>${this.$t("posTitle")}：</p><p>1.${this.$t("posMsg1")}</p><p>2.${this.$t("posMsg2")}</p>`
      }).show()
    }
  },
  destroyed() {
  }
}
</script>
<style lang="stylus">
.scan-page {

  .mapWrapper {
    height: calc(100vh - 390px);
    position: relative;

    .map {
      height: 100%;
      width: 100%;

      .anchorBL {
        display: none !important;
      }
    }
  }

  .position {
    border-bottom: 1px solid #E3E3E9;

    .signPosition {
      padding-top: 10px;
      padding-bottom: 12px;
      text-align: center;

      i {
        color: #ea5947;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #444;
      }
    }

    .posName {
      position: relative;
      margin-left: 5%;
      margin-bottom: 20px;
      width: 90%;
      color: #DB9E00;
      height: 36px;
      background: #FFF5DB;
      border-radius: 4px;

      i {
        position: absolute;
        top: 8px;
        font-size: 18px;
        line-height: 22px;
        color: #DB9E00;
      }

      span {
        display: inline-block;
        margin-left: 16px;
        width: calc(100% - 48px);
        height: 36px;
        line-height: 36px;
        font-size: 16px;
        color: #DB9E00;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .morePos {
    height: 146px;
    padding-top: 15px;

    .tipName {
      position: relative;
      margin-left: 18px;
      font-size: 12px;
      font-weight: 400;
      color: #969696;
      line-height: 17px;

      .locationWrong {
        position: absolute;
        right: 22px;
        font-weight: 400;
        float: right;

        span {
          color: #969696;
          font-size: 12px;
        }

        i {
          margin-right: 5px;
          color: #969696;
          font-size: 12px;
        }
      }
    }

    .morePosLi {
      height: 116px;
      padding: 0 24px;
      overflow-y: scroll;

      .posItem {
        padding: 13px 0;
        border-bottom: 1px solid #F1F3F7;

        &:nth-last-of-type {
          border: none;
        }
      }
    }
  }

  .panel {
    padding: 8px 20px 0;

    .uButton {
      padding: 12px 16px;
    }
  }

  .pos-error {
    padding-top: 200px;
    text-align: center;
    .text {
      margin-bottom: 15px;
      font-size: 14px;
      color: #969696;
    }
    .uButton {
      display: inline-block;
      width: auto;
      padding: 10px 12px;
      font-size: 14px;
    }
  }
}
</style>
