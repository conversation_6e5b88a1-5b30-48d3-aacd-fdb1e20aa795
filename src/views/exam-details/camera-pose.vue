<template>
  <Page
    :title="$t('checkCamera')"
    @back="back"
  >
    <Posenet @poseResult="getPoseResult" />
    <div class="pose-container">
      <div class="desc">
        <p>{{ $t('cameraMonitorTip1') }}</p>
        <p>{{ $t('cameraMonitorTip2') }}</p>
      </div>
    </div>
  </Page>
</template>
<script>
import { Page } from '@/common/components'
import Posenet from '@/components/posenet';
import { cameraStream, posenetResult, showCameraPreview, forbidBackWithGesture } from '@/lib/cordovaPlugin/mobilePlugin';

export default {
  components: {
    Page,
    Posenet
  },
  created() {
    cameraStream({
      isOpenCamera: 1,
      intervalTime: 200
    })
  },
  mounted() {
    // 禁止手势返回
    forbidBackWithGesture(1)
    this.$once('hook:beforeDestroy', () => {
      forbidBackWithGesture(0)
    })
  },
  methods: {
    getPoseResult(data) {
     const { status, result } = data
      posenetResult({
        result: result,
        status: status === 6
      })
    },
    back() {
      // 销毁视频视图
      showCameraPreview(0)
      // 关闭摄像头
      cameraStream({
        isOpenCamera: 0
      })
      this.$router.back()
      return false
    }
  }
}
</script>
<style lang="stylus">
.pose-container {
  max-height: 100%;
  overflow-y: auto;
  .desc {
    margin: 0 auto;
    padding: 400px 30px 0;

    p {
      margin-bottom: 10px;
    }
  }
}
</style>
