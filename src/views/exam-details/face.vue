<template>
  <Page :title="title" class="page-face">
    <div ref="body" class="view-body">
      <div class="photo-not-uploaded" v-show="step == 'photoNotUploaded'">
        <input class="h5-file-input" type="file" ref="h5fileInput" capture="camera" accept="image/*" @change="fileChange">
        <div class="photo-wrapper">
          <div class="left-top flag"></div>
          <div class="left-bottom flag"></div>
          <div class="right-top flag"></div>
          <div class="right-bottom flag"></div>
          <!-- <input type="file" id="photoUploader" accept="image/*" /> -->
          <div class="photo">
            <div class="icon-wrapper" @click="getFile">
              <i class="iconfont icon-dianjishangchuan"></i>
            </div>
            <div class="tips" >{{ $t('clickUpload') }}</div>
          </div>
        </div>
        <p class="warning">
          <b>{{ $t('lookOut') }}:</b
          >  {{ $t('faceTip1') }}
          <span class="red">{{ $t('faceTip2') }}</span>{{ $t('faceTip3') }}
        </p>
      </div>
      <div class="photo-uploaded" v-if="step == 'photoUploaded'">
        <div class="photo-wrapper">
          <img class="photo-recognition" :alt="$t('authPhoto')" :src="faceInfo.url | ImageCropper">
        </div>
        <div class="recognition-tips">
          {{ $t('faceTip4') }}
        </div>
        <button type="button" class="button button-black-hollow" @click="test">{{ $t('testFace') }}</button>
        <button type="button" class="button button-red-hollow" @click="getFile">{{ $t('reUpload') }}</button>
        <button type="button" class="button button-red-solid" @click="saveFace">{{ $t('confirmPhoto') }}</button>
      </div>
      <div class="photo-uploaded" v-if="step == 'photoCheck'">
        <div class="photo-wrapper">
          <img class="photo-recognition" :alt="$t('authPhoto')" :src="faceInfo.url|ImageCropper">
          <div class="recognition-status" :class="{'uncheck':faceInfo.status == 1,'success':faceInfo.status == 2,'fail':faceInfo.status == 3}">{{checkStatus[faceInfo.status-1]}} <i class="iconfont icon-tishi" v-if="faceInfo.status == 3" @click="showReason"></i> </div>
          <div class="reason-wrapper" v-if="isShowCheckReason">
            <div>{{ $t('auditor') }}：{{faceInfo.checkUserName}}</div>
            <div>{{ $t('auditTime') }}：{{faceInfo.checkTime | TimeFormat('yyyy-MM-dd hh:mm')}}</div>
            <div>{{ $t('refuseReason') }}：{{faceInfo.comment}}</div>
          </div>
        </div>
        <div class="user-info">
          <div class="name">{{user.name}}</div>
          <div class="student-num">{{user.studentID}}</div>
        </div>
        <button type="button" class="button button-red-hollow" v-if="faceInfo.status == 3" @click="getFile">{{ $t('reUpload') }}</button>
      </div>
      <div class="component-face" v-show="step == 'camera'">
        <canvas ref="camera"></canvas>
      </div>
      <div class="face-fail" v-if="step == 'faceFail'">
        <img src="@/common/image/face/faceFail.svg" alt="" />
        <div class="fail-text">{{ $t('faceNotPass') }}</div>
        <div class="btn-retry" @click="retry()">{{ $t('retry') }}</div>
        <div class="code-tips" @click="toCode()" v-if="type != 'authentication'">
          {{ $t('faceTip5') }}>>
        </div>
      </div>
      <div class="face-success" v-if="step == 'faceSuccess'">
        <img src="@/common/image/face/faceSuccess.svg" alt="" />
        <div class="success-text">
          {{type != 'authentication'?$t('faceTip6'):$t('authPass')}}
        </div>
      </div>
      <div class="step-code" v-if="step == 'code'">
        <div class="input-tips">{{ $t('enterCode') }}</div>
        <div class="code-tips">{{ $t('enterCodeTip') }}</div>
        <form name="code" ref="form" class="input-group">
          <input
            v-for="(item, index) in [1, 2, 3, 4]"
            :key="index"
            type="text"
            pattern="\d*"
            @keyup="codeKeyup"
            @keydown="codeKeydown"
            :data-index="index"
            required
            min="0"
            max="9"
            minlength="1"
            maxlength="1"
            onpaste="return false"
          />
          <div v-if="codeWarning.show" class="warning">
            {{ codeWarning.text }}
          </div>
        </form>
      </div>
    </div>
  </Page>
</template>
<script>
import { Page } from '@/common/components'
import faceApi from '@/api/face'
import axios from 'axios'
import uploader from '@/common/components/m-uploader'
import compressImage from '@/utils/compressImage.js'
import * as qiniu from 'qiniu-js'
import { QINIU_RESOURCE_URL } from '@/common/config'
let IMAGE_CROPPER = '?imageslim|imageView2/1/w/295/h/413'
import { formatUrl } from "@/common/utils/formatCDNUrl"
import { faceRecognition, createUploade, selectFileFromiOS, getPictureFromMobile } from '@/lib/cordovaPlugin/mobilePlugin';
import { getPlatform } from '@/common/utils'
import { getFileType, fileTypeLimit } from '@/components/attach-file/file'
import { getUniqueValue } from '@/utils/utils'
export default {
  data () {
    return {
      title: this.$t('faceAuth'),
      step: 'camera',
      type: '',
      codeWarning: {
        show: false,
        text: this.$t('codeWrong')
      },
      faceInfo: {},
      isPhotoCanUse: false,
      checkStatus: [this.$t('waitAuth'), this.$t('authPass2'), this.$t('authNotPass')],
      isShowCheckReason: false,
      isSavingFace: false,
      successTimeout: ''
    }
  },
  components: {
    Page
  },
  computed: {
    user () {
      // return this.$store.getters.getUser('user')
      return this.$store.state.user
    }
  },
  filters: {
    ImageCropper: function (value) {
      // imageMogr2/auto-orient
      if (value.includes('imageView2')) {
        return value
      }
      return value + IMAGE_CROPPER
    },
    TimeFormat: function (input, fmt) {
      var date = new Date(input)
      var o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        'S': date.getMilliseconds() // 毫秒
      }
      if (/(y+)/.test(fmt)) { fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length)) }
      for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) { fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length))) }
      }
      return fmt
    }
  },
  mounted () {
    this.type = this.$route.params.type
    if (this.type === 'exam') {
      this.step = 'camera'
      this.initCamera()
    } else if (this.type === 'code') {
      this.toCode()
    } else {
      this.step = 'photoNotUploaded'
      this.getFaceInfo((data) => {
        this.faceInfo = data || {}
        if (data && data.url) {
          this.step = 'photoCheck'
        }
        // this.step = 'photoUploaded'
      })
    }
  },
  beforeDestroy () {
    this.destoryCamera()
    clearTimeout(this.successTimeout)
  },
  methods: {
    initCamera () {
      this.iosCamera() // iosCamera 客户端  andoridCamera h5实现(废弃)
      // this.andoridCamera()
      // if (/(iPhone|iPad|iPod|iOS)/i.test(window.navigator.userAgent)) {
      //   this.iosCamera()
      // } else {
      //   this.andoridCamera()
      // }
    },
    getFaceInfo (callback) {
      faceApi.getFaceInfo()
        .then(({ data }) => {
          callback(data)
        })
    },
    getFile () {
      // this.$refs.h5fileInput.click()
      this.$createActionSheet({
        // title: '我是标题~~~',
        active: 0,
        cancelTxt:this.$t("cancel"),
        data: [
          {
            content: this.$t('fromAlbum')
          },
          {
            content: this.$t('fromCamera')
          }
        ],
        onSelect: (item, index) => {
          if (index === 0) {
            if (getPlatform().isAndroid || getPlatform().isHarmony) {
              this.getAndroidFileNew()
            } else {
              this.getIosFile()
            }
          } else if (index === 1) {
            this.camera(true)
          } 
        }
      }).show()
    },
    getAndroidFileNew () {
      this.camera(false, true)
    },
    getIosFile () {
      let self = this
      selectFileFromiOS(false, (fileList) => {
        fileList.forEach((file) => {
          let ext = file.path.substring(file.path.lastIndexOf('.') + 1)
          if (fileTypeLimit(ext)) {
            let item = {
              title: file.name,
              type: getFileType(ext),
              contentSize: file.size,
              location: file.path,
              mimeType: ext,
              progress: 0
            }
            self._uploadFile(file.path,item)   
          } else {
            self.$createToast({
              type: 'error',
              time: 1500,
              txt: self.$t('selectFileTip'),
            }).show()
          }
        })
      }, (err) => {
        console.log(err)
      }, true, true)
    },
    camera (source, mediaType) {
      const self = this
      getPictureFromMobile(source, function (arr) {
        arr.forEach((item) => {
          console.log('下面是手机拍照的图片路径')
          console.log(item.filePath)
          if (fileTypeLimit(item.filePath)) {
            let file = {
              id: getUniqueValue(),
              title: '',
              type: getFileType(item.filePath),
              contentSize: 0,
              location: '',
              mimeType: '',
              progress: 0
            }
            file.title = item.fileName
            file.location = item.filePath
            file.mimeType = file.location.substring(file.location.lastIndexOf('.') + 1)
            self._uploadFile(file.location,file)   
          } else {
            self.$createToast({
              type: 'error',
              time: 1500,
              txt: self.$t('selectFileTip'),
            }).show()
          }
        })
      }, function (err) {
        if (err.toLowerCase() !== 'cancel select') { 
          self.$createDialog({
            type: 'alert',
            content: err
          }).show()
        }
      }, Boolean(mediaType), true, 1)
    },
    _uploadFile (path, file) {
      this.uploader = createUploade()
      let instance = this.$createUpopup({
        content: this.$t('uploading') + '...'
      })
      this.uploader.upload(path, 
        (filePath, remotePath) => {
          this.isPhotoCanUse = false
          this.$set(this.faceInfo, 'url', formatUrl(remotePath) + IMAGE_CROPPER)
          this.step = 'photoUploaded'
          instance.remove()
        }, 
        () => {
        }, 
        (filePath, progress) => {
          file.progress = Math.floor(progress * 100 >= 100 ? 99 : progress * 100)
        }, 
      )
    },
    fileChange () {
      let self = this
      const inputFile = this.$refs.h5fileInput.files[0]
      let instance = this.$createUpopup({
        content: this.$t('uploading') + '...'
      })
      let options = {
        quality: 0.92,
        noCompressIfLarger: true,
        maxWidth: 250,
        maxHeight: 350
      }
      try {
        compressImage(inputFile, options).then(data => {
          // var observable = qiniu.upload(data.dist, key, token, putExtra, config)
          callback(data.dist)
        })
      } catch (e) {
        callback(inputFile)
      }

      function callback (data) {
        uploader.upload(data, {
          observer: {
            next (res) {
              console.log('next')
            },
            complete (res) {
              console.log('complete')
              console.log(QINIU_RESOURCE_URL + res.key)
              self.isPhotoCanUse = false
              self.$set(self.faceInfo, 'url', QINIU_RESOURCE_URL + res.key + IMAGE_CROPPER)
              self.step = 'photoUploaded'
              instance.remove()
            }
          },
          success (subscription) {
            console.log('success')
          },
          error () {
            instance.remove()
            self.$createDialog({
              type: 'alert',
              confirmBtn: { text: this.$t('confirm'), active: true },
              content: this.$t('uploadError'),
            }).show()
          }
        })
      }
    },
    iosCamera () {
      let self = this
      try {
        let successCb = result => {
          let url = formatUrl(result + IMAGE_CROPPER)
          // eslint-disable-next-line eqeqeq
          if (self.type == 'authentication') {
            self.testFace(url)
          } else {
            self.compareFace(url)
          }
        }
        const isFromExamDetail = self.$route.params.type === 'exam'
        let errorCb = error => {
          console.error(error)
          if (isFromExamDetail) {
            error === 'back' && self.$router.back()
            error === 'checkByCode' && self.toCode()
          } else {
            self.step = 'faceFail'
          }
        }
        faceRecognition(isFromExamDetail ? 1 : 0, successCb, errorCb)
      } catch (e) {
        self.$createDialog({
          type: 'alert',
          content: this.$t('faceTip7'),
          confirmBtn: { text: this.$t('confirm'), active: true }
        }).show()
      }
    },
    andoridCamera () {
      let self = this
      var ctx = this.$refs.camera.getContext('2d')
      var config = {
        width: this.$refs.body.offsetWidth,
        height: this.$refs.body.offsetHeight
      }
      this.$refs.camera.setAttribute('width', config.width)
      this.$refs.camera.setAttribute('height', config.height)
      function rgbaToGrayscale (rgba, nrows, ncols) {
        var gray = new Uint8Array(nrows * ncols)
        for (var r = 0; r < nrows; ++r) {
          for (var c = 0; c < ncols; ++c) { // gray = 0.2*red + 0.7*green + 0.1*blue
            gray[r * ncols + c] = (2 * rgba[r * 4 * ncols + 4 * c + 0] + 7 * rgba[r * 4 * ncols + 4 * c + 1] + 1 * rgba[r * 4 * ncols + 4 * c + 2]) / 10
          }
        }
        return gray
      }
      var facefinderClassifyRegion = function (r, c, s, pixels, ldim) { return -1.0 }
      var updateMemory = window.pico.instantiate_detection_memory(5) // we will use the detecions of the last 5 frames
      function circleInFrame (circleX, circleY, circleR) {
        var $camera = self.$refs.camera
        var xMin = $camera.offsetLeft
        var xRange = $camera.offsetWidth
        var yMin = $camera.offsetTop
        var yRange = $camera.offsetHeight
        var total = 100000
        var numInCircle = 0
        var num = 0
        for (var i = 0; i < total; i++) {
          // 投点
          var x = Math.round(Math.random() * $camera.offsetWidth)
          var y = Math.round(Math.random() * $camera.offsetHeight)
          var inCircle = Math.pow(circleX - x, 2) + Math.pow(circleY - y, 2) - Math.pow(circleR, 2) < 0
          var inFrame = x > xMin && x < xMin + xRange && y > yMin && y < yMin + yRange
          if (inCircle) {
            numInCircle++
            if (inFrame) {
              num++
            }
          }
        }
        console.log(num, numInCircle)
        if (num / numInCircle > 0.9) {
          return true
        }
        return false
      }
      var processfn = function (video, dt) {
        // render the video frame to the canvas element and extract RGBA pixel data
        // if(self.myCanvas.video.paused) {
        //   return
        // }
        ctx.drawImage(video, 0, 0)
        var rgba = ctx.getImageData(0, 0, config.width, config.height).data
        // prepare input to `run_cascade`
        var image = {
          'pixels': rgbaToGrayscale(rgba, config.height, config.width),
          'nrows': config.height,
          'ncols': config.width,
          'ldim': config.width
        }
        var params = {
          'shiftfactor': 0.1, // move the detection window by 10% of its size
          'minsize': 100, // minimum size of a face
          'maxsize': 1000, // maximum size of a face
          'scalefactor': 1.5 // for multiscale processing: resize the detection window by 10% when moving to the higher scale
        }
        var dets = window.pico.run_cascade(image, facefinderClassifyRegion, params)
        dets = updateMemory(dets)
        dets = window.pico.cluster_detections(dets, 0.2) // set IoU threshold to 0.2
        for (var i = 0; i < dets.length; ++i) {
          if (dets[i][3] > 50.0) { // 检测到人脸出现
            if (circleInFrame(dets[i][1], dets[i][0], dets[i][2] / 2)) {
              try {
                self.getImage()
                self.myCanvas.video.pause()
              } catch (e) {
                console.error(e)
                self.myCanvas.video.play()
              }
            }
          }
        }
      }
      var cascadeurl = './static/face/facefinder'
      fetch(cascadeurl).then(function (response) {
        response.arrayBuffer().then(function (buffer) {
          var bytes = new Int8Array(buffer)
          facefinderClassifyRegion = window.pico.unpack_cascade(bytes)
          console.log('* cascade loaded')
        })
      })
      self.myCanvas = new self.Canvas(ctx, config, processfn)
    },
    getImage () {
      var dataUrl = this.$refs.camera.toDataURL('image/jpeg')
      // var $img = document.getElementById('faceImg')
      // $img.src = dataUrl
      var base64 = dataUrl.substr(dataUrl.indexOf('base64') + 7)
      faceApi.getUpToken({ fileName: 'base64.jpg' })
        .then(({ data }) => {
          axios({
            baseURL: 'https://up.qbox.me/putb64/-1',
            headers: { 'Authorization': 'UpToken ' + data.uptoken, 'Content-Type': 'application/octet-stream' },
            method: 'post',
            data: base64
          }).then(response => {
            let url = QINIU_RESOURCE_URL + response.data.key + IMAGE_CROPPER
            // eslint-disable-next-line eqeqeq
            if (this.type == 'authentication') {
              this.testFace(url)
            } else {
              this.compareFace(url)
            }
          })
        })
    },
    showReason () {
      this.isShowCheckReason = !this.isShowCheckReason
    },
    testFace (url) {
      let query = {
        'url1': this.faceInfo.url,
        'url2': url
      }
      faceApi.testFace(query)
        .then(({ data }) => {
          if (data.samePerson) {
            this.step = 'faceSuccess'
            this.isPhotoCanUse = true
            setTimeout(() => {
              this.step = 'photoUploaded'
            }, 1000)
          } else {
            this.step = 'faceFail'
          }
          this.destoryCamera()
        })
        .catch(error => {
          this.step = 'faceFail'
          console.log(error)
        })
    },
    compareFace (url) {
      let data = {
        'url': url,
        'status': '0'
      }
      let instance = this.$createUpopup({
        content: this.$t('authing') + '...'
      })
      faceApi.compareFace({ examId: this.$route.params.examID }, data)
        .then(({ data }) => {
          if (data.code === 1) {
            this.step = 'faceSuccess'
            this.successTimeout = setTimeout(() => {
              this.$router.push({ name: 'exam', params: { userID: this.$route.params.userID, examID: this.$route.params.examID } })
            }, 3000)
          } else {
            this.step = 'faceFail'
            this.destoryCamera()
            this.$saveLog(`${this.$route.params.examID}_人脸识别失败_${url}_${JSON.stringify(data)}`)
          }
          instance.remove()
        })
        .catch(error => {
          instance.remove()
          this.step = 'faceFail'
          this.$saveLog(`${this.$route.params.examID}_人脸识别失败_${url}_${JSON.stringify(error)}`)
          console.log(error)
        })
    },
    saveFace () {
      if (this.isSavingFace) {
        return
      }
      if (!this.isPhotoCanUse) {
        this.$createDialog({
          type: 'alert',
          content: this.$t('faceTip8'),
          confirmBtn: { text: this.$t('confirm'), active: true }
        }).show()
        return
      }
      var data = {
        status: 1,
        url: this.faceInfo.url
      }
      this.isSavingFace = true
      faceApi.saveFaceInfo(data)
        .then(({ data }) => {
          this.step = 'photoCheck'
          this.faceInfo = data
          this.isSavingFace = false
        })
        .catch(error => {
          this.isSavingFace = false
          console.log(error)
        })
    },
    test () {
      this.step = 'camera'
      this.initCamera()
    },
    destoryCamera () {
      if (this.myCanvas) {
        // this.myCanvas.video.srcObject.getTracks()[0].stop()
        this.myCanvas.destory()
      }
    },
    retry () {
      this.step = 'camera'
      this.initCamera()
    },
    toCode () {
      this.step = 'code'
      this.title = this.$t('examCode')
      setTimeout(() => {
        this.$refs.form.querySelectorAll('input')[0].focus()
      }, 300)
    },
    codeKeyup (event) {
      if (event.keyCode === 8) {
        // if (event.target.value === '') {
        //   event.target.previousElementSibling.value = ''
        //   event.target.previousElementSibling.focus()
        // }
      } else if (event.target.dataset.index === '3') {
        this.codeSubmit()
      } else {
        event.target.nextElementSibling.focus()
      }
      this.codeWarning.show = false
      if (event.target.value.length > 0) {
        event.target.value = event.target.value.substring(event.target.value.length - 1)
      }
    },
    codeKeydown (event) {
      if (event.keyCode === 8) {
        // eslint-disable-next-line eqeqeq
        if (event.target.value == '') {
          event.target.previousElementSibling.value = ''
          event.target.previousElementSibling.focus()
        }
      }
    },
    codeSubmit () {
      let inputs = this.$refs.form.querySelectorAll('input')
      let code = ''
      Array.prototype.forEach.call(inputs, (input) => {
        code += input.value
      })
      console.log(code)
      let xhr = faceApi.compareCode({
        examId: this.$route.params.examID,
        verificationCode: code
      })
      xhr.then(({ data }) => {
        if (data.code === 1) {
          this.step = 'faceSuccess'
          setTimeout(() => {
            this.$router.push({ name: 'exam', params: { userID: this.$route.params.userID, examID: this.$route.params.examID } })
          }, 3000)
        } else {
          this.codeWarning.show = true
          this.codeWarning.text = this.$t('codeWrong')
        }
      })
        .catch(() => {
          this.codeWarning.text = this.$t('faceTip9')
          this.codeWarning.show = true
        })
    },
    Canvas (ctx, config, callback) {
      var self = this
      this.ctx = ctx
      this.callback = callback

      // We can't `new Video()` yet, so we'll resort to the vintage
      // "hidden div" hack for dynamic loading.
      this.streamContainer = document.createElement('div')
      this.video = document.createElement('video')

      // If we don't do this, the stream will not be played.
      // By the way, the play and pause controls work as usual
      // for streamed videos.
      this.video.setAttribute('autoplay', '1')
      this.video.setAttribute('playsinline', '1')

      // The video should fill out all of the canvas
      this.video.setAttribute('width', config.width)
      this.video.setAttribute('height', config.height)
      this.video.style.display = 'none'

      this.streamContainer.appendChild(this.video)
      document.body.appendChild(this.streamContainer)

      // The callback happens when we are starting to stream the video.

      console.log(config)
      navigator.mediaDevices.getUserMedia({ video: { width: config.width, height: config.height }, audio: false }).then(function (stream) {
        // Yay, now our webcam input is treated as a normal video and
        // we can start having fun
        self.video.srcObject = stream
        // Let's start drawing the canvas!
        self.update()
      }, function (err) {
        throw err
      })

      // As soon as we can draw a new frame on the canvas, we call the `draw` function
      // we passed as a parameter.
      this.update = function () {
        var self = this
        var last = Date.now()
        var loop = function () {
          // For some effects, you might want to know how much time is passed
          // since the last frame; that's why we pass along a Delta time `dt`
          // variable (expressed in milliseconds)
          var dt = Date.now - last
          if (!self.video.paused) {
            self.callback(self.video, dt)
          }
          last = Date.now()
          window.requestAnimationFrame(loop)
        }
        window.requestAnimationFrame(loop)
      }
      this.destory = function () {
        this.video.srcObject.getTracks()[0].stop()
        document.body.removeChild(this.streamContainer)
      }
    }
  }
}
</script>
<style lang="stylus">
.page-face {
  font-size: 14px;
  color: #444;
  background-color: #f5f5f5;
.view-body {
  height: 100%;
  overflow: auto;
  text-align: center;
  .photo-wrapper {
    position: relative;
    height: 352px;
    width: 260px;
    padding: 14px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 4px 10px 0 rgba(0,0,0,0.05);
    .flag {
      position: absolute;
      width: 22px;
      height: 22px;
      border: solid 3px rgba(234, 89, 71, 0.2);
    }

    .left-top {
      top: 10px;
      left: 10px;
      border-right: none;
      border-bottom: none;
    }

    .left-bottom {
      bottom: 10px;
      left: 10px;
      border-right: none;
      border-top: none;
    }

    .right-top {
      top: 10px;
      right: 10px;
      border-left: none;
      border-bottom: none;
    }

    .right-bottom {
      bottom: 10px;
      right: 10px;
      border-left: none;
      border-top: none;
    }

    #photoUploader, #uploaderForQiniu {
      display: none;
    }

    .photo {
      height: 100%;

      .icon-wrapper {
        display: inline-block;
        height: 80px;
        width: 80px;
        margin-top: 60px;
        opacity: 0.8;
        border-radius: 100%;
        background: linear-gradient(to bottom, #F97652, #ea5947);
        box-shadow: 0 5px 10px 0 rgba(234, 89, 71, 0.4);

        i {
          line-height: 80px;
          font-size: 40px;
          color: #fff;
        }
      }

      .tips {
        margin-top: 14px;
        line-height: 24px;
        color: #969696;
      }
    }
  }

  .photo-not-uploaded {
    padding: 15px 0 50px 0;
    .h5-file-input{
      display none
    }
    .photo-recognition {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    .recognition-status {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      font-size: 22px;
      color: #fff;
      line-height: 48px;

      &.fail {
        background-color: rgba(243, 117, 88, 0.9);
      }

      &.success {
        background-color: rgba(94, 190, 124, 0.9);
      }

      &.uncheck {
        background-color: rgba(239, 181, 85, 0.9);
      }
    }

    .warning {
      display: inline-block;
      padding: 0 15px;
      margin-top: 48px;
      line-height: 24px;
      text-align: left;
      b{
        font-weight:bold;
      }
      .red {
        color: #F60000;
      }
    }
  }

  .photo-uploaded {
    padding: 15px 0 50px 0;
    width 260px
    margin 0 auto
    .photo-wrapper{
      padding 4px
    }
    .photo-recognition {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    .recognition-tips {
      margin: 10px 0;
      line-height: 20px;
      text-align: left;
      font-size: 12px;
    }

    .button {
      height: 40px;
      width: 100%;
      margin-bottom: 15px;
      line-height: 40px;
    }
    .recognition-status{
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      font-size: 16px;
      color: #fff;
      line-height: 40px;
      &.fail{
        background-color: rgba(243,117,88,0.9);
      }
      &.success{
        background-color: rgba(94,190,124,0.9);
      }
      &.uncheck{
        background-color: rgba(239,181,85,0.9);
      }
    }
    .user-info{
      .name{
        margin-top: 16px;
        font-size: 18px;
        line-height: 24px;
        font-weight: bold;
      }
      .student-num{
        margin-top: 4px;
        font-size: 16px;
        line-height: 22px;
      }
    }
    .reason-wrapper{
      position: absolute;
      left: 50%;
      bottom: 65px;
      width: 230px;
      padding: 14px 20px;
      margin-left: -115px;
      background-color: rgba(68,68,68,0.8);
      border-radius: 4px;
      color: #fff;
      line-height: 24px;
      text-align: left;
    }
  }

  .component-face {
    position: relative;
    font-size: 14px;
    height: 100%;
    overflow:hidden;
    canvas {
      height: 100%;
      width: 100%;
    }
  }

  .face-success, .face-fail {
    text-align: center;
  }

  .success-text {
    color: #5abc8c;
    line-height: 20px;
  }

  .fail-text {
    margin-bottom: 20px;
    line-height: 20px;
  }

  .btn-retry {
    display: inline-block;
    width: 240px;
    margin-bottom: 20px;
    line-height: 32px;
    color: #ea5947;
    border: solid 1px #e3e3e9;
  }

  .code-tips {
    color: #8d77fb;
    line-height: 20px;
  }

  .step-code {
    padding: 45px 35px 0;
    text-align:left;
    .input-tips {
      font-size: 24px;
      font-weight: bold;
      line-height: 32px;
    }

    .code-tips {
      margin-top: 8px;
      line-height: 20px;
      color: #444;
    }

    .input-group {
      width: 220px;
      margin: 50px auto 0;

      input {
        font-size: 32px;
        line-height: 40px;
        font-weight: bold;
        border-bottom: solid 2px #444;
        width: 35px;
        padding:12px 0;
        margin: 0 10px;
        outline: none;
        text-align: center;
        background-color: transparent;
        color: #444;
        border-radius: 0;
      }
    }

    .warning {
      padding: 0 10px;
      margin-top: 15px;
      color: #f60000;
      line-height: 20px;
      text-align: left;
    }
  }
}
}
</style>
