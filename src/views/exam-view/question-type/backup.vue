<script>
export default {
  props: {
    partname: String,
    titleContent: String,
    type: Number
  }
}
</script>

<template>
  <div :class="[$style.heading, columnMode && $style.columnModeHead ]" ref="heading">
    <h2 :class="$style.partname" v-if="partname">{{partname}}</h2>
    <div :class="$style.demand">
      <small v-if="!inComposite" :class="$style.commit">{{types[type].text}}</small>
      <button v-if="type === 24" :class="$style.columnBtn" @click="columnToogle">
        <i class="iconfont" :class="columnBtn.icon"></i>
        {{columnBtn.text}}
      </button>
      <div :class="$style.title">
        <i v-if="inComposite" :class="$style.order">{{item.order + 1}}</i>
        <div v-html="item.title" ref="title" :class="$style.titleText" :style="titleMaxWidth"></div>
      </div>
      <div :class="$style.attach" v-if="item.link && item.link.length>0">
        <AttachFile
          v-for="(src, index) in item.link"
          :key="index"
          :src="src"
          :limitCount="limitCount"
          :count="counts[index] || 0"
          @save="(id, value) => syncAudioCount(id, index, value)"
          :id="item.questionid"
        />
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.wrapper
  height 100%
  // margin-bottom 20px
  overflow-y auto
  -webkit-overflow-scrolling touch

.partname
  height 44px
  padding 0 16px
  font-size 14px
  line-height 44px
  color #444
  background-color #f1f3f7
  white-space nowrap
  overflow hidden
  text-overflow ellipsis

.demand
  padding 30px 16px
  position relative

  /.commit
    position absolute
    top 0
    left 0
    padding 0 16px 0 8px
    height 20px
    font-size 12px
    line-height 20px
    background-color #ffefe2
    color #ff9c62
    border-bottom-right-radius 20px

  /.title
    font-size $font-size-medium-x
    line-height 1.5
    display flex
    align-items center

    /.order
      flex 0 0 20px
      width 20px
      height 20px
      text-align center
      line-height 20px
      font-size 12px
      color #ff9c62
      background-color #ffefe2
      border-radius 50%
      font-style normal
      margin-right 8px

    /.titleText
      flex 1 1

.attach
  padding 8px 0 16px 0

// 分屏模式
.columnBtn
  position absolute
  top 0
  right 16px
  font-size 15px
  color $color-main
  background transparent
  border none
  outline none
  line-height 30px
  padding 0

  i
    font-size 18px

.columnMode
  height 100%
  display flex
  flex-flow column nowrap

.columnModeHead
  // max-height 80%
  // min-height 20%
  overflow-y auto
  flex 0 0 auto

.dragZone
  flex 0 0 auto
  height 56px
  position relative
  margin-bottom -24px
  z-index 10

  &::after
    content ''
    position absolute
    background #f1f3f7
    width 56px
    height 8px
    top 50%
    left 50%
    margin -4px 0 0 -28px

.columnModeMain
  background-color #f1f3f7
  overflow-y auto
  padding-bottom 20px
</style>
