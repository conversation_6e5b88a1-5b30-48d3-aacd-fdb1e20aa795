<script>
// const OBJ = [1, 2, 3, 4, 12, 17, 23]
import { getComputedCorrect, evalFormula } from '@/utils/formula'
import { getMediaType } from '@/components/attach-file/file'
import AttachFile from '@/components/attach-file'
const SUB = [5, 16, 19]
export default {
  props: ['data', 'reportType'], // type === 1 type === 2
  computed: {
    // 主观题还是客观题
    answerType () {
      const { type } = this.data
      if (SUB.includes(type)) {
        // 主观题 5 16 19
        return 'subjective'
      } else {
        return 'objective'
      }
    },
    grade () {
      const { grade } = this.data
      return grade || '--'
    },
    answerStyle () {
      const { type } = this.data
      if (type === 4) {
        return [this.$style.answer, this.$style.answerJudge]
      } else {
        return this.$style.answer
      }
    }
  },
  mounted () {
  },
  methods: {
    formatAnswer (ans, fallback, isCorrect) {
      let answer = ans
      const { type } = this.data
      // 对判断题单独处理
      if (type === 4) {
        if (answer === 'true') {
          answer = '\ue83d'
        } else if (answer === 'false') {
          answer = '\ue83e'
        }
      }
      if (Array.isArray(ans)) {
        // 对排序题单独处理
        if (type === 12) {
          answer = ans.join('-')
        } else {
          answer = ans.join(';')
        }
      }
      if (type == 26 && isCorrect) {
        answer = this.formulaCorrect()
      }
      return answer || fallback
    },
    formulaCorrect () {
      let arr = []
      this.data.referAnswer.map((item, index) => {
        item = JSON.parse(item)
        let str = getComputedCorrect({
          correct: item,
          formulaVar: this.data.formulaVar
        })
        arr.push(str + '±' + item.answerRange)
      })
      return arr.join(';')
    },
    getMediaType
  },
  components: {
    AttachFile
  }
}
</script>

<template>
  <div :class="$style.wrapper" v-if="reportType === 2">
    <div v-if="answerType === 'objective'" :class="$style.content">
      <div :class="answerStyle">{{ $t('correctAnswer') }}：{{formatAnswer(data.referAnswer, '', true)}}</div>
      <AttachFile v-if="data.answerlink" :type="getMediaType(data.answerlink)" :src="data.answerlink" :id="'answerlink' + data.questionid"></AttachFile>
      <p :class="answerStyle">{{ $t('userAnswer') }}：{{formatAnswer(data.studentAnswer, $t('noAnswer'))}}</p>
      <div v-if="data.referReply" :class="$style.explain">{{ $t('analysis') }}：
        <div v-html="data.referReply" :class="[$style.info, 'has-img']"></div>
      </div>
      <AttachFile v-if="data.analy" :type="getMediaType(data.analy)" :src="data.analy" :id="'analy' + data.questionid"></AttachFile>
    </div>
    <div v-else-if="answerType === 'subjective'" :class="$style.content">
      <p :class="$style.answer">{{data.examStatus === 1 ? $t('answerScore1', { num: data.score }) : $t('answerScore2', { n: grade, num: data.score })}}</p>
      <p :class="$style.answer">{{ $t('teaComment') }}：{{data.remark1 ? data.remark1 : $t('empty')}}</p>
      <div :class="$style.answer">{{ $t('correctAnswer2') }}：<div :class="[$style.info, 'has-img']" v-html="data.referAnswer"></div></div>
      <!-- <div :class="$style.correctImg" v-if="data.answerlink"><img :src="data.answerlink"></div> -->
      <AttachFile v-if="data.answerlink" :type="getMediaType(data.answerlink)" :src="data.answerlink" :id="'answerlink' + data.questionid"></AttachFile>
      <div v-if="data.referReply" :class="$style.explain">{{ $t('answerAnalysis') }}：
        <div :class="[$style.info, 'has-img']" v-html="data.referReply"></div>
      </div>
      <AttachFile v-if="data.analy" :type="getMediaType(data.analy)" :src="data.analy"  :id="'analy' + data.questionid"></AttachFile>
    </div>
  </div>
</template>

<style lang="stylus" module>
.wrapper
  margin 20px

.content
  padding 16px
  background #fff url('@/assets/images/anlysis_bj.png') no-repeat
  background-size cover
  font-size $font-size-medium-x
  line-height 1.875
  color $text-color-default
  border-radius 4px
  word-wrap break-word

  /.answer
    margin-bottom 4px
  /.answerJudge
    font-family iconfont

  /.info
    display inline-block
    vertical-align top
    ol
      list-style-type: decimal;
      list-style-position: inside;
    ul
      list-style-type: disc;
      list-style-position: inside;
    li
      list-style: inherit;
</style>
