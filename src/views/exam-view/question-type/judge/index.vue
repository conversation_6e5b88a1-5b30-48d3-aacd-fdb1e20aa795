<script>
import Correct from '@/assets/images/judge-correct.png'
import Error from '@/assets/images/judge-error.png'
import ActiveCorrect from '@/assets/images/judge-a-correct.png'
import ActiveError from '@/assets/images/judge-a-error.png'
import CheckCorrect from '@/assets/images/judge-c-correct.png'
import CheckError from '@/assets/images/judge-c-error.png'
export default {
  props: ['item', 'reportType'],
  data () {
    return {
      values: ['true', 'false'],
      judgeImgList:{
        'true': Correct,
        'false':  Error,
        'a-true': ActiveCorrect,
        'a-false': ActiveError,
        'c-true': CheckCorrect,
        'c-false': CheckError
      }
    }
  },
  computed: {
    id () {
      return this.item.questionid
    },
    stuAnswer: {
      get () {
        return this.item.studentAnswer
      },
      set (value) {
        this.$emit('syncStuAnswer', this.id, value)
      }
    }
  },
  methods: {
    handleImage(it) {
      // 是否答题
      if(this.stuAnswer){
        if(this.stuAnswer === it) return this.judgeImgList[`a-${this.stuAnswer}`]
      }
      return this.judgeImgList[it];
    },
    handleCoverImage(value) {
      // 答题记录
      if (this.reportType === 2) {
        const stuAnswer = this.stuAnswer
        const checkResult = this.item.checkResult
        if (value === stuAnswer) {
          // 用户答题记录
          return this.judgeImgList[`c-${checkResult}`]
        }
      }
    },
    checkStuAnswer (value) {
      if (this.reportType === 2) {
        const stuAnswer = this.stuAnswer
        const checkResult = this.item.checkResult
        if (value === stuAnswer) {
          // 用户答题记录
          return checkResult ? this.$style.checkRight : this.$style.checkError
        }
      }
      return ''
    }
  },
  mounted () {}
}
</script>

<template>
  <div :class="$style.content">
      <label :class="$style.item" v-for="(it, index) in values" :key="index">
        <input type="radio" :name="item.questionid" :class="$style.input"   v-model="stuAnswer" :value="it" :disabled="!!reportType">
        <img :class="[$style.image,{ [$style.active]: stuAnswer   === it }]" :src="handleImage(it)" >
        <!-- 答题记录的纠正误图片 -->
        <img v-if="reportType === 2 && it == stuAnswer" :class="[$style.cover_image,$style[`cover_${item.checkResult}`]]" :src="handleCoverImage(it)">
      </label>
  </div>
</template>

<style lang="stylus" module>
.content
   display flex
   justify-content center
  //  margin-top -2px
  //  background #fff
.image
  border 1px solid #e3e3e9
  border-radius 50%
  padding 20px
.cover_image
  position absolute
  top -10px
  right -10px
  padding 7px
  border 1px solid #e3e3e9
  border-radius 50%
.cover_true
  background-color $color-right
  
.cover_false
  background-color $color-error
  
.item
  display block
  width 60px
  height 60px
  position relative
  &:first-child
    margin-right 60px

  /.input
    position absolute
    top 0
    left 0
    width 100%
    height 100%
    opacity 0
    z-index 1
  /.label
    display block
    width 100%
    height 100%
    border 1px solid #e3e3e9
    border-radius 50%
    line-height 60px
    color #e3e3e9
    text-align center
    background-color #fff
  /.icon-true::after
    content '\e83d'
  /.icon-false::after
    content '\e83e'
  /.active
    color $color-main
    border-color $color-main
    position relative

icon()
  position absolute
  top -10px
  right -10px
  width 30px
  height 30px
  font-family "iconfont"
  text-align center
  line-height 30px
  border-radius 50%
  color #fff

// 答案错误
.checkError::before
  content '\E83E'
  background-color $color-error
  icon()

// 答案正确
.checkRight::before
  content: '\E83D'
  background-color $color-right
  icon()
</style>
