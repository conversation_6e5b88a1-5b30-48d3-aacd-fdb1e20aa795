<script>
import AttachFile from '@/components/attach-file'
import { getMediaType } from './file.js'
import { formatUrl } from '@/common/utils'
let index = 10000000
export default {
  components: {
    AttachFile,
  },
  props: {
    limitCount: Number,
    id: Number,
    resource: {
      type: Object,
      default() {
        return {}
      },
    }
  },
  data() {
    return {
      ended: true,
      options: {},
      canplay: false,
    }
  },
  computed: {
    computedSrc() {
      return formatUrl(this.resource.link)
    },
    type() {
      return getMediaType(this.resource.link)
    },
    // 禁止播放
    disabled() {
      return (
        this.resource.count >= this.limitCount &&
        this.ended === true && !this.resource.curTime
      )
    },
    countTip() {
      const { limitCount, disabled } = this
      if (disabled) {
        return this.$t('useUp')
      } else if (this.resource.count > 0) {
        return this.$t('useTimes', { count: this.resource.count, limitCount: limitCount })
      } else {
        return ''
      }
    },
    // 启动计数
    enableCount() {
      return this.limitCount < Infinity
    },
  },
  created() {
    // 虚拟slide设置随机index作为视频或者音频id，防止音视频重复渲染
    this.index = Math.floor(Math.random() * index)
  },
  mounted() {
    // 有播放次数限制和没播放次数限制的音频或视频
    this.media = this.enableCount
      ? this.$refs.audio.media
      : this.$refs.media.media
    if(!this.media) return 
    this.media.addEventListener('playing', () => {
      if (this.ended === true && this.disabled === false) {
        // 重新播放时，如果是第一次播放 && 有音频次数限制 => count+1
        if ((!this.resource.curTime || this.resource.curTime == 0) && this.enableCount) {
          this.resource.count += 1 
          // 保存录音次数（调用接口）
          this.$emit('save')
        }
        this.ended = false
      }
    })
    // 切换题目或其他事件触发暂停时，保存当前播放时间
    this.media.addEventListener('pause', () => {
      if (this.disabled != true) {
        this.resource.curTime = this.media.getCurrentTime() || this.resource.curTime
      }
    })
    this.media.addEventListener('ended', () => {
      this.resource.curTime = 0
      this.ended = true
    })
    this.media.addEventListener('play', () => {
      if (this.disabled === true) {
        this.media.pause()
        this.media.setCurrentTime(0)
      }
    })
  },
  beforeDestroy() {
    if (this.media) {
      this.resource.curTime = this.media.getCurrentTime() || this.resource.curTime
    }
  },

  destroyed() {
    index = 10000000
  },
}
</script>

// id 必须是一个字符串
<template>
  <div>
    <div v-if="enableCount && type === 'audio'">
      <div :class="[$style.container, disabled ? $style.disabled : '']">
        <AttachFile
          :type="type"
          :src="computedSrc"
          :id="'audio' + index"
          ref="audio"
          :curTime="resource.curTime || 0"
        ></AttachFile>
        <em :class="$style.countTip">{{ countTip }}</em>
      </div>
      <div :class="$style.tip">{{ $t('playTip', { num: limitCount }) }}</div>
    </div>
    <AttachFile
      v-else
      :type="type"
      :src="computedSrc"
      :id="'media-' + index"
      ref="media"
      :curTime="resource.curTime || 0"
    />
  </div>
</template>

<style lang="stylus" module>
.container
  display flex
  align-items center

  :global(.custom-audio)
    width 50% !important
    min-width 150px !important

  :global(.mejs__controls)
    width auto

  :global(.mejs__time-rail)
    display none

  :global(.custom-audio .mejs__currenttime-container)
    color #314767
    display block

.tip
  color #ea5947
  padding-top 0.5em

.countTip
  color #ea5947
  font-style normal

.disabled
  :global(.mejs__controls)
    background-color #ccc
</style>
