<script>
import { Ellipsis } from '@/common/components'
import * as app from '@/lib/cordovaPlugin/mobilePlugin'
import { fileIcon, getMediaType, formatSize, getFileType, fileTypeLimit } from '@/components/attach-file/file'
import AttachFile from '@/components/attach-file'
import Bus from '@/utils/bus'
import { textCount, getUniqueValue, checkUpdateAnswerLength } from '@/utils/utils'
import FilePreviewer from '../component-file-preview'
import { getPlatform } from '@/common/utils'
import { Popup } from 'cube-ui'
export default {
  components: {
    Ellipsis,
    AttachFile,
    FilePreviewer,
    Popup
  },
  props: ['item', 'reportType', 'examInfo'],
  data () {
    return {
      currentFile: null,
      fileSizeLimit: 200, // 附件大小限制单位M
    }
  },
  computed: {
    id () {
      return this.item.questionid
    },
    stuAnswer: {
      get () {
        return this.item.studentAnswer || ''
      },
      set (value) {
        this.$emit('syncStuAnswer', this.id, value)
      }
    },
    inputValue () {
      const stuAnswer = this.stuAnswer
      if (stuAnswer || this.item.studentFiles.length > 0) {
        return stuAnswer
      } else {
        return '<span class="error-breif">' + this.$t('noAnswer') + '</span>'
      }
    }
  },
  methods: {
    textCount,
    addFile () {
      if (this.item.studentFiles.length >= 10) {
        this.$createToast({
          txt: this.$t('stuFilesMaxTips'),
          type: 'txt',
          mask: true,
          time: 1000
        }).show()
        return
      }
      if(this.examInfo.enableAppUpload) {
        // 允许手机上传附件和照片
        this.$createActionSheet({
          data: [
            {
              content: this.$t('selectFile'),
            },
            {
              content: this.$t('fromAlbum1'),
            },
            {
              content: this.$t('fromCamera'),
            }
          ],
          onSelect: (item, index) => {
            if (index === 0) {
              if (getPlatform().isAndroid || getPlatform().isHarmony) {
                this.getAndroidFileNew()
              } else {
                this.getIosFile()
              }
            } else if (index === 1) {
              this.camera(false, true)
            } else if (index === 2) {
              this.camera(true)
            }
          }
        }).show()
      } else {
        // 只允许拍照
        this.$createActionSheet({
          data: [
            {
              content: this.$t('fromCamera'),
            }
          ],
          onSelect: (item, index) => {
            this.camera(true)
          }
        }).show()
      }
    },
    getIosFile () {
      let self = this
      Bus.$emit('camera-change', { 
        type: 'ios',
        value: true
      })
      app.selectFileFromiOS(false, (fileList) => {
        Bus.$emit('camera-change', { 
        type: 'ios',
        value: false
      })
        fileList.forEach((file) => {
          let ext = file.path.substring(file.path.lastIndexOf('.') + 1)
          if (fileTypeLimit(ext)) {
            let item = {
              title: file.name,
              type: getFileType(ext),
              contentSize: file.size,
              location: file.path,
              mimeType: ext,
              progress: 0
            }
            if(self.checkFileSize(item.size)) return
            if(!self.checkSameUploadFile(item)) {
                self.item.studentFiles.push(item)   
                self._uploadFile(file.path,item)   
            }
          } else {
            self.$createToast({
              type: 'error',
              time: 1500,
              txt: self.$t('selectFileTip'),
            }).show()
          }
        })
      }, (err) => {
        Bus.$emit('camera-change', { 
          type: 'ios',
          value: false
        })
        console.log(err)
      }, true, true)
    },
    getAndroidFile () {
      var self = this
      const fileType  = '*.doc;*.docx;*.xls;*.xlsx;*.pptx;*.ppt;*.pdf;*.txt;'
      // window.cordova.require('cordova/exec')('selectFile')
      Bus.$emit('camera-change', true)
      app.selectFileFromAndroid2(function (data) {
        Bus.$emit('camera-change', false)
        data.map(item => {
          let ext = item.filePath.substring(item.filePath.lastIndexOf('.') + 1)
          let file = {
            title: item.fileName,
            type: getFileType(ext),
            contentSize: item.fileSize,
            location: item.filePath,
            mimeType: ext,
            progress: 0
          }
          self.item.studentFiles.push(file)
          self._uploadFile(item.filePath,file)
        })
      },{
        type: fileType,
        maxTotalSize: 500,
        // maxNum: 10 - self.item.studentFiles.length,  防止上传错乱
        maxNum: 1,
        maxSize: 50,
        isHideBottomBtn: 0
      })
    },
    getAndroidFileNew () {
      this.camera(false, true)
    },
    camera (source, mediaType) {
      const self = this
      Bus.$emit('camera-change', true)
      app.getPictureFromMobile(source, function (arr) {
        arr.forEach((item) => {
          console.log('下面是手机拍照的图片路径')
          console.log(item.filePath)
          if (fileTypeLimit(item.filePath)) {
            let file = {
              id: getUniqueValue(),
              title: '',
              type: getFileType(item.filePath),
              contentSize: 0,
              location: '',
              mimeType: '',
              progress: 0
            }
            file.title = item.fileName
            file.location = item.filePath
            file.mimeType = file.location.substring(file.location.lastIndexOf('.') + 1)
            app.getFileSize(file.location, function(fileSize){
              file.contentSize = fileSize
              if(self.checkFileSize(fileSize)) return
              if(!self.checkSameUploadFile(file)) {
                self.item.studentFiles.push(file)   
                self._uploadFile(file.location,file)   
              }
            })
          } else {
            self.$createToast({
              type: 'error',
              time: 1500,
              txt: self.$t('selectFileTip'),
            }).show()
          }
        })
        Bus.$emit('camera-change', false)
      }, function (err) {
        Bus.$emit('camera-change', false)
        if (err.toLowerCase() !== 'cancel select') { 
          self.$createDialog({
            type: 'alert',
            content: err
          }).show()
        }
      }, Boolean(mediaType), true, 1)
    },
    _uploadFile (path, file) {
      this.uploader = app.createUploade()
      this.uploader.upload(path, 
        (filePath, remotePath) => {
          let completePath = 'file://' + filePath
          if (completePath == file.location) {
            file.location = remotePath
            file.mimeType = file.location.substring(file.location.lastIndexOf('.') + 1)
            file.progress = 100
          }
        }, 
        () => {
          file.progress = 0
        }, 
        (filePath, progress) => {
          file.progress = Math.floor(progress * 100 >= 100 ? 99 : progress * 100)
        }, 
      )
    },
    deleteFile (i) {
      this.item.studentFiles.splice(i, 1)
    },
    cancelFile (item, index) {
      this.uploader.cancel(item.location)
      this.deleteFile(index)
    },
    showImagePreview (item) {
      var type = getFileType(item.mimeType)
      if (type == 'image') {
        this.$createImagePreview({
          imgs: [item.location + '?imageView2/0/format/jpg']
        }, (h) => {
          return h('div', {
            slot: 'footer'
          }, '')
        }).show()
      } else if (type == 'document' || type == 'video') {
        this.previewFile(item)
      }
      
    },
    isUploadedFileCanView (item) {
      var type = getFileType(item.mimeType)
      if (type == 'document' || type == 'video' || type == 'image') {
        return true
      }
      return false
    },
    previewFile (item) {
      this.currentFile = item
      // this.$router.push({
      //   name: 'file-preview',
      //   params: item
      // })
    },
    closeFilePreview () {
      this.currentFile = null
    },
    fileIcon,
    getMediaType,
    getFileType,
    isUseAttachFile (item) {
      var type = getMediaType(item.mimeType)
      if (type == 'audio' || type == 'video' || type == 'image') {
        return true
      }
      return false
    },
    fileSize: formatSize,
    changeFun() {
      const { textarea } = this.$refs
      if (textarea) {
        this.stuAnswer = textarea.value
      }
    },
    handleClick(e) {
      // 绑定监听值改变
      e.changeFun = this.changeFun
      Bus.$emit('text-input-click', {
        e: e,
        parent: this.$parent
      })
    },
    handleScroll() {
      Bus.$emit('text-input-scroll')
    },
    // 校验fileSize
    checkFileSize(fileSize) { 
      if(Math.round(fileSize / 1024 / 1024, 2) > this.fileSizeLimit) {
        this.$createToast({
          type: 'error',
          time: 1500,
          txt: this.$t('maxFileSize'),
        }).show()
        return true
      }
      return false
    },
    // 检验是否有正在上传中的相同文件
    checkSameUploadFile(file) { 
      let fileUploading = false
      this.item.studentFiles.forEach((stuFile, i) => {
        if(stuFile.location == file.location && stuFile.progress != 100) {
          fileUploading = true
        }
      })
      if(fileUploading) {
        this.$createToast({
          type: 'warn',
          time: 1500,
          txt: this.$t('sameUploadFile'),
        }).show()
      }
      return fileUploading
    },
    // 查看允许上传的文件格式
    viewUploadType() {
      Bus.$emit('view-upload-type')
    },
    // 下载
    download(item) {
      app.openUrl(item.location);
    },
  },
  watch: {
    stuAnswer (newValue, oldValue) {
      let diffLength = newValue.length - oldValue.length
      if (diffLength > 5) {
        const str = newValue.replace(oldValue, '')
        if(checkUpdateAnswerLength(str)) {
          this.stuAnswer = oldValue
        }
      } 
    },
    'item.studentFiles.length': {
      handler(val) {
        this.$nextTick(() => {
          this.$emit("updateSlide")
        })
        if (val >= 1) {
          this.item.stat = 'answer'
        } else if (val == 0 && this.stuAnswer.trim() == '') {
          this.item.stat = 'blank'
        }
      },
      immediate: true
    }
  },
  mounted () {
    // console.log('简答题')
    // console.log(this.item)
    // this.reportType = true
    window.breif = this
    // this.item.studentFiles = []
  }
}
</script>
<template>
  <div>
    <div :class="$style.wrapper">
      <div :class="$style.textareaWrapper" v-if="!reportType">
        <textarea class="opacity" :class="$style.textarea" :placeholder="$t('inputAnswer')" v-model="stuAnswer" v-if="!examInfo.openSafeKeyboard" @focus="handleClick"></textarea>
        <div class="opacity text-input-wrap" v-else>
          <textarea ref="textarea" class="textarea" readonly :placeholder="$t('inputAnswer')" v-model="stuAnswer" @touchstart.passive.stop @scroll="handleScroll" @focus="handleClick" @click.stop="handleClick"></textarea>
          <div ref="textareaFollow" class="textarea textarea-follow"></div>
        </div>
        <div v-if="examInfo.enableUpload"  class="opacity" :class="$style.fileUploader">
          <i @click="addFile" class="iconfont icon-shangchuanwenjian" :class="$style.uploadBtn"></i>
          <i v-if="examInfo.enableAppUpload" class="iconfont icon-wenhao" :class="$style.viewUploadType" @click="viewUploadType"></i>
        </div>
        <span :class="$style.textCount">{{$t('textCount', {n: textCount(stuAnswer)})}}</span>
      </div>
      <Ellipsis v-else>
        <p :class="$style.inputValue" v-html="inputValue"></p>
      </Ellipsis>
    </div>
    <ul :class="$style.fileList"  v-if="!reportType  && item.studentFiles.length > 0">
      <li v-for="(item,index) in item.studentFiles" :key="item.id || index">
        <div v-if="getFileType(item.mimeType) == 'image'" :class="$style['img-wrapper']" :style="{'background-image':'url(\'' + item.location + '?imageView2/0/format/jpg' + '\')'}"></div>
        <div :class="$style['img-wrapper']" v-else><i v-if="item.location" class="iconfont" :class="fileIcon(item.mimeType)"></i></div>
        <div :class="$style['file-info']">
          <div :class="$style.title">{{item.title}}</div>
          <div :class="$style.extra">
            <span>{{fileSize(item.contentSize)}}</span>
            <div :class="$style.operation" v-if="item.progress >= 100">
              <a href="javascript:;" :class="$style.red" v-if="isUploadedFileCanView(item)" @click="showImagePreview(item)">{{$t('preview')}}</a>
              <a href="javascript:;" :class="$style.gray" @click="deleteFile(index)">{{$t('delete')}}</a>
            </div>
            <div :class="$style.operation" v-else>
              <a href="javascript:;" :class="$style.red">{{item.progress}}%</a>
              <a href="javascript:;" :class="$style.gray" @click="cancelFile(item, index)">{{$t('cancel')}}</a>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul v-if="reportType && item.studentFiles.length > 0" :class="$style.filePreviewList">
      <li  v-for="(item,index) in item.studentFiles" :key="item.id || index">
        <AttachFile v-if="isUseAttachFile(item)" :markedImage="item.markedImage" :angle="item.angle" :type="getMediaType(item.mimeType)" :src="item.location" :id="'media-' + index + item.location" ref="audio"></AttachFile>
        <div v-else-if="isUploadedFileCanView(item)" :class="$style['file-card']" @click="previewFile(item)">
          <i :class="['iconfont',fileIcon(item.mimeType)]"></i>
          <div :class="$style.title">{{item.title}}</div>
          <div :class="$style.size">{{fileSize(item.contentSize)}}</div>
          <i class="iconfont icon-arrow_up-copy-copy"></i>
        </div>
        <div v-else :class="$style['no-preview-file']">
          <i :class="['iconfont',fileIcon(item.mimeType)]"></i>
          <div :class="$style.title">{{item.title}}</div>
          <div :class="$style.size">{{fileSize(item.contentSize)}}</div>
          <div :class="$style.download" @click="download(item)">{{ $t('download') }}</div>
        </div>
      </li>
    </ul>
    <div :class="$style.filePreviewer" v-if="currentFile">
      <div :class="$style.comWrapper">
        <FilePreviewer :file="currentFile"></FilePreviewer>
      </div>
      <div :class="$style.iconWrapper">
        <i class="iconfont icon-yemian-guanbi"  @click="closeFilePreview"></i>
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.wrapper
  padding 16px
  border-top 1px solid #e3e3e9
  font-size 16px
  line-height 24px
  margin-top -14px
  /.textarea
    padding 8px
    maring-bottom 30px
    box-sizing border-box
    width 100%
    height 150px
    border-radius 4px
    color $text-color-default
    background-color #f1f3f7
    resize none
    outline none
    border none
    font-size 16px
    line-height 24px
    &::placeholder
      color #4567429
  /.textareaWrapper
    display flex
    flex-direction column
    position relative
    padding-bottom 25px
    background-color #f1f3f7
    margin-bottom 20px
  /.inputValue
    white-space pre-wrap
  /.fileUploader
    position absolute 
    left 0
    bottom 3px
    z-index 3
    display flex
    /.uploadBtn
      padding 0 8px
    /i
      font-size 20px
      color #969696
  /.textCount
    position absolute 
    right 10px
    bottom 2px
    z-index 3
    font-size 14px
    color #969696
.fileList
  margin 0 16px
  padding-bottom 50px
  border-top solid 1px #e3e3e9
  li
    display flex
    &:not(:last-child)
      .file-info
        border-bottom solid 1px #e3e3e9
  /.img-wrapper
    width 40px
    height 40px
    line-height 40px
    margin-right 12px
    margin-top 14px
    background-repeat no-repeat
    background-position center
    background-size cover
    i
      font-size 40px
  .file-info
    flex-grow 1
    width 0
    padding 14px 0
  .title
    font-size 16px
    line-height 22px
    height 22px
    overflow hidden
    text-overflow ellipsis
    white-space nowrap
  .extra
    display flex
    font-size 14px
    line-height 18px
    span
      width 60px
      color #969696
    a
      margin-right 16px
    .red
      color #ea5947
    .gray
      color #969696
.filePreviewList
  padding 0 16px
  li
    margin-bottom 16px
  .file-card
    display flex
    font-size 14px
    height 50px
    padding 0 8px
    align-items center
    background-color #fafafa
    border-radius 4px
    i
      font-size 20px
      &:last-child
        font-size 12px
        color #969696
    .title
      flex-grow 1
      margin-left 8px
      text-overflow ellipsis
      overflow hidden
      white-space nowrap
      width 0
    .size
      margin-right 10px
      font-size 12px
      color #969696
  .no-preview-file
    display flex
    font-size 14px
    height 50px
    padding 0 8px
    align-items center
    background-color #fafafa
    border-radius 4px
    i
      font-size 20px
      &:last-child
        font-size 12px
        color #969696
    .title
      flex-grow 1
      margin-left 8px
      text-overflow ellipsis
      overflow hidden
      white-space nowrap
      width 0
    .size
      margin-right 10px
      font-size 12px
      color #969696
    .download 
      color #A08EFA
.answer-record
  // max-height 120px         // 默认最多显示5行
  position relative
  overflow hidden
  .answer-expand
    position absolute
    right 0
    bottom 0
    background-image linear-gradient(to right, rgba(255, 255, 255, .2) 0, #fff 1em)
    padding-left 1em
  // .answer-collapse
  //   position absolute
  .operator
    font-style normal
    color #ed7161
  .op-hidden
    display none
.anlysis-zone
  anlysis-zone()
  .anlysis-answer
    margin-bottom 4px
  .main-info
    display inline-block
    vertical-align top
.filePreviewer
  position absolute
  z-index 999
  display flex
  flex-direction column
  top 0
  bottom 0
  left 0
  right 0
  padding: 30px 20px;
  background-color: rgba(0,0,0,0.8);
  .comWrapper
    flex 1
    height 0
  .iconWrapper
    height 40px
    text-align center
    line-height 40px
    i
      font-size 24px
      color #fff
.viewUploadType 
  padding-left: 8px;
  font-size: 18px;
  color: #999999;
:global(.error-breif) {
  color red
}
</style>
