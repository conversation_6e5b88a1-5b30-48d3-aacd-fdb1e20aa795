<script>
export default {
  beforeCreate: function () {
    this.$options.components.Question = require('../index').default
  },
  props: ['item', 'reportType', 'examInfo'],
  data () {
    return {
      isComposite: true
    }
  },
  computed: {
    id () {
      return this.item.questionid
    }
  },
  methods: {
    syncStuAnswer (id, stuAnswer) {
      this.$emit('syncStuAnswer', id, stuAnswer)
    },
    syncAudioCount (id) {
      this.$emit('syncAudioCount', id)
    },
    // 排序题选择时，是否可以滑动
    canSwiperSlide (moveFlag) {
      this.$emit('canSwiperSlide', moveFlag)
    },
    updateSlide() {
      this.$emit("updateSlide")
    }
  },
  mounted () {
  }
}
</script>

<template>
  <div :class="[$style.composite, 'composite-mark']">
    <Question v-for="(it, index) in item.questionlist" :item="it" :examInfo="examInfo"  :reportType="reportType" :data-index="index" :key="it.questionid" @syncStuAnswer="syncStuAnswer" @syncAudioCount="syncAudioCount" @updateSlide="updateSlide" @canSwiperSlide="canSwiperSlide"></Question>
  </div>
</template>

<style lang="stylus" module>
// .composite
//   border-top 1px solid #e3e3e9
</style>
