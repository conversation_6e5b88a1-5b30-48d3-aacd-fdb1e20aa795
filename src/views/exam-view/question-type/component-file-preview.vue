<template>
  <div class="file-component">
    <div v-if="type == 'document'" class="file-document">
      <iframe v-if="type=='document'" :src="getOfficeUrl()" class="iframe-detail-content"></iframe>
      <div class="file-wrapper" v-else>
        <i :class="['iconfont',fileIcon(file.mimeType)]"></i>
        <div class="title">{{file.title}}</div>
      </div>
    </div>
    <div v-if="type == 'video'">
      <AttachFile  :type="type" :src="file.location" :id="'media'" ref="video"></AttachFile>
    </div>
  </div>
</template>
<script>
import { fileIcon, getMediaType, formatSize, getFileType } from '@/components/attach-file/file'
import AttachFile from '@/components/attach-file'
export default {
  components: {
    AttachFile
  },
  data () {
    return {

    }
  },
  props:['file'],
  methods: {
    getMediaType,
    formatSize,
    fileIcon,
    getOfficeUrl () {
      let src = "https://docs.ulearning.cn/?i=16664";
      let location = this.file.location
      if (location.indexOf("https://") != -1) {
        src += "&ssl=1";
      }
      src += "&furl=" + location;
      return src
    }
  },
  computed:{
    type () {
      return getFileType(this.file.mimeType)
    }
  },
  mounted () {
  }
}
</script>



<style lang="stylus" scoped>
.file-component
  height 100%
  width 100%
.file-document
  background-color #fff
  height 100%
iframe
  width: 100%;
  height: 100%;
  overflow: auto;
.file-wrapper
  padding: 100px 32px;
  text-align: center;
  i{
    font-size 40px
  }
  .title
    margin-top 20px
</style>
