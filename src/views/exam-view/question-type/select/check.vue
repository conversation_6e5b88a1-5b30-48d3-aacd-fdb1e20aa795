<script>
import AttachFile from '../attach' // 附件
import { decimal2Letter } from '@/common/utils'
export default {
  components: {
    AttachFile
  },
  props: ['data', 'type', 'reportType', 'name', 'index', 'id'],
  computed: {
    stuAnswer: {
      get () {
        if (this.data.type === 17) {
          return this.data.questionlist[this.index].studentAnswer
        } else {
          return this.data.studentAnswer
        }
      },
      set (newVal) {
        if (!this.disabled) {
          this.$emit('input', newVal)
        }
      }
    },
    disabled () {
      return !!this.reportType
    },
    options () {
      return this.data.item || []
    }
  },
  methods: {
    format (index) {
      return decimal2Letter(index, 26).toUpperCase()
    },
    checkStuAnswer (index) {
      const { reportType, type } = this
      const item = this.format(index)
      const stuAnswer = this.stuAnswer
      if (reportType < 2) {
        if (type === 'radio') {
          if (item === stuAnswer) return this.$style.checked
        } else if (type === 'checkbox') {
          if (stuAnswer.includes(item)) return this.$style.checked
        }
      } else if (reportType === 2) {
        let checkResult = false
        let referAnswer = ''
        if (this.data.type === 17) {
          ({ referAnswer, checkResult } = this.data.questionlist[this.index])
        } else {
          ({ referAnswer, checkResult } = this.data)
        }
        if (type === 'radio') {
          if (item === stuAnswer) {
            return checkResult ? this.$style.checkRight : this.$style.checkError
          } else if (item === referAnswer) {
            return this.$style.correctAnswer
          }
        } else if (type === 'checkbox') {
          const referResult = referAnswer.includes(item)
          const stuResult = stuAnswer.includes(item)
          if (referResult && stuResult) {
            return this.$style.checkRight
          } else if (!referResult && stuResult) {
            return this.$style.checkError
          } else if (referResult && !stuResult) {
            return this.$style.correctAnswer
          }
        }
      }
    },
  },
  mounted () {}
}
</script>
<template>
  <ul :class="$style.wrapper">
    <li :class="$style.itemWrap" v-for="(it, index) in options" :key="index" data-role="list-item">
      <label :class="$style.item">
        <input
          :type="type"
          :class="[$style.input]"
          v-model="stuAnswer"
          :value="format(index)"
          :name="name"
          :disabled="disabled"
        >
        <i class="iconfont" :class="[$style.icon, $style['icon-' + type], checkStuAnswer(index)]"></i>
        <p :class="$style.label" v-html="it.title"></p>
      </label>
      <div v-if="it.link" :class="{[$style.link]: true, [$style.linkSp] :it.title.length <= 0}">
        <attach-file :src="it.link" :id="id" :resource="it"></attach-file>
      </div>
    </li>
  </ul>
</template>

<style lang="stylus" module>
.wrapper
  // background-color #fff
  border-top 1px solid #e3e3e9

.itemWrap
  counter-increment test 1
  padding 17px 0 16px
  border-bottom 1px solid #e3e3e9

  // pointer-events none
  /.label
    position relative
    padding-left 20px
    img 
      vertical-align middle
  /.label::before
    content counter(test, upper-alpha) '. '
    position absolute
    left 0

.item
  padding 0 10px
  display flex
  align-items center
  position relative

  /.input
    opacity 0
    position absolute
    width 100%
    height 100%
    top 0
    left 0
    z-index 1

  /.label
    line-height 26px
    word-wrap break-word
    overflow-wrap break-word
    ol
      list-style-type: decimal;
      list-style-position: inside;
    ul
      list-style-type: disc;
      list-style-position: inside;
    li
      list-style: inherit;

.icon
  flex 0 0 auto
  width 26px
  height 26px
  line-height 26px
  text-align center
  border 1px solid #e3e3e9
  border-radius 50%
  margin-right 0.5em
  color #e3e3e9
  background #fff

  &::after
    content '\e83d'
    font-family 'iconfont'

.icon-checkbox
  border-radius 4px

// .input:checked + .icon
// color $color-main
// border-color $color-main

// 用户选中
.checked
  color $color-main
  border-color $color-main

// 渲染正确答案
.correctAnswer
  background-color #fff
  color $color-error
  border-color $color-error

// 答案错误
.checkError
  background-color $color-error
  color #fff
  border-color $color-error

  &:after
    content '\e83e'
    font-family 'iconfont'

// 答案正确
.checkRight
  background-color $color-right
  color #fff
  border-color $color-right

.link
  margin 0 46px

.linkSp
  margin -20px 46px 0 66px
</style>
