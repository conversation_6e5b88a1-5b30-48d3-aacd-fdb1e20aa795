<script>
import RadioGroup from './check'
export default {
  components: {
    RadioGroup
  },
  props: ['item', 'reportType'],
  computed: {
    id () {
      return this.item.questionid
    }
  },
  methods: {
    input (value) {
      this.$emit('syncStuAnswer', this.id, value)
    },
  }
}
</script>

<template>
  <div>
    <RadioGroup
      type="radio"
      :name="item.questionid"
      :data="item"
      :reportType="reportType"
      @input="input"
      :id="id"
    >
    </RadioGroup>
  </div>
</template>

<style lang="stylus" module>
</style>
