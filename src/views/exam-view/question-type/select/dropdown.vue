<script>
import Check from './check'
import { Popup } from 'cube-ui'
import Answer from '../answer'

const renderInput = (value, index) => {
  return `<span class="select-input select-input-blank" data-index="${index}">${value}</span>`
}

const renderWithAnswer = (value, index, mark) => {
  return `<span class="select-input select-input-${mark}" data-index="${index}">${value}<i class="select-check-mark select-check-${mark}"></i></span>`
}

export default {
  components: {
    Popup,
    Check,
    Answer
  },
  props: ['item', 'reportType'],
  data () {
    return {
      index: 0,
      visible: false,
      scrollTop: 0
    }
  },
  computed: {
    parentTitle () {
      return this.$parent.$refs.title
    },
    type () {
      return this.item.type
    },
    subItem () {
      if (this.type === 17) {
        return this.item
      } else {
        return this.item.questionlist[this.index]
      }
    },
    subID () {
      return this.item.questionlist[this.index].questionid
    },
    records () {
      return this.item.questionlist[this.index]
    }
  },
    watch: {
    visible: {
      handler(val) {
        this.$emit('changeHeight', val, this.scrollTop)
      }
    }
  },
  methods: {
    genBlank (index) {
      const { reportType, type } = this
      let value = ''
      const item = this.item.questionlist[index]
      const stuAnswer = item.studentAnswer
      const order = item.order
      if (stuAnswer) {
        const listItem = type === 17 ? this.item.item : item.item
        value = stuAnswer + '.' + listItem[stuAnswer.toLowerCase().codePointAt(0) - 97].title
      } else {
        value = `-${order + 1}- `
      }
      if (reportType < 2) {
        return renderInput(value, index)
      } else if (reportType === 2) {
        const stat = item.stat
        return renderWithAnswer(value, index, stat)
      }
    },
    input (value) {
      this.$emit('syncStuAnswer', this.subID, value)
      // 这里因为需要等到下一个tick才能执行，要不然视图不更新
      setTimeout(() => {
        const arr = this.parentTitle.querySelectorAll('.question')
        arr[this.index].innerHTML = this.genBlank(this.index)
        this.visible = false
      });
    },
    genItems () {
      const { item } = this.item
      const node = document.createElement('div')
      node.className = 'words-list-wrap'
      const ul = node.appendChild(document.createElement('ul'))
      ul.className = 'words-list'
      ul.innerHTML = item.map(it => `<li>${it.title}</li>`).join(' ')
      return node
    }
  },
  mounted () {
    this.$nextTick(() => {
      const arr = this.parentTitle.querySelectorAll('.question')
      this.parentTitle.style.lineHeight = '30px'
      if (this.type === 17) {
        const parent = this.parentTitle.parentElement.parentElement
        parent.insertBefore(this.genItems(), parent.firstElementChild)
      }
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i]
        element.innerHTML = this.genBlank(i)
        element.addEventListener('click', () => {
          this.visible = true
          this.index = i
          this.scrollTop = document.querySelector(".composite-mark").scrollTop
          // 渲染公式(两个下拉的情况会出现不渲染)
          this.$emit('renderFormula')
        })
      }
    })
  },
}
</script>

<template>
  <Popup v-model="visible" position="bottom" style="position:absolute;" maskClosable :class="$style.popup" >
      <div :class="$style.content" @touchmove.stop="() => {}">
        <div :class="$style.title">
          <span :class="$style.index">{{records.order + 1}}</span>{{ $t('option') }}
        </div>
        <Check
          :data="subItem"
          type="radio"
          :reportType="reportType"
          @input="input"
          :name="subID"
          :index="index"
        />
        <Answer :data="records" :reportType="reportType"></Answer>
      </div>
  </Popup>
</template>

<style lang="stylus" module>
.popup :global(.cube-popup-content)
  max-height 70%
  overflow-y auto
  border-radius 8px 8px 0 0
.content
  background-color #fff
  border-radius 8px 8px 0 0
  // margin-right calc(100vw - 100%)
  padding-bottom 40px
  // max-height 70%
.title
  height 44px
  line-height 44px
  // border-radius 8px 8px 0 0/8px 8px 0 0
  font-size 14px
  color #969696
.index
  display inline-block
  width 24px
  height 24px
  text-align center
  line-height 24px
  // font-size 12px
  color #ff9c62
  background-color #ffefe2
  border-radius 50%
  font-style normal
  margin-right 8px
  margin-left 8px

// .content [data-role="list-item"]
//   padding 7px 0 6px
:global(.select-input)
  line-height 26px
  padding 5px 25px 5px 10px
  border-radius 4px
  position relative
  &::after
    content ''
    position absolute
    border-bottom 7px solid
    border-left 4px solid transparent
    border-right 4px solid transparent
    border-color transparent
    border-top 7px solid #d5d5d5
    margin-left 3px
    bottom 2px
:global(.select-input-blank)
  padding-top 4px
  padding-bottom 4px
  border 1px solid #e3e3e9
  background #ffffff
:global(.select-input-right)
  background-color #f1f3f7
:global(.select-input-error)
  background-color #ffeeee
  color #f60000
  &::after
    border-top-color #f60000
:global(.select-check-mark)
  position absolute
  width 28px
  height 28px
  border-radius 50%
  text-align center
  line-height 28px
  font-style normal
  bottom 9px
  // right -13px
  margin-left 8px
  transform scale(0.5)
:global(.select-check-error)
  background-color #f60000
  color #fff
  &::after
    content '\e83e'
    font-family 'iconfont'
    font-size 14px
:global(.select-check-right)
  background-color #41c80a
  color #fff
  &::after
    content '\e83d'
    font-family 'iconfont'
    font-size 14px

:global(.words-list-wrap)
  margin 0
  margin-bottom 16px
  padding 16px
  font-size 16px
  line-height 26px
  background-color #f1f3f7
  border-radius 4px
  /:global(.words-list)
    display flex
    flex-wrap wrap
    margin 0 -15px
    &>li
      min-width 50%
      padding 0 15px
      &:last-child
        margin-right 0
</style>
