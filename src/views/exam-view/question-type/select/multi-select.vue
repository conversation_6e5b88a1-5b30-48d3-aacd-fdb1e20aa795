<script>
import CheckboxGroup from './check'
export default {
  components: {
    CheckboxGroup
  },
  props: ['item', 'reportType'],
  computed: {
    id () {
      return this.item.questionid
    }
  },
  methods: {
    input (value) {
      this.$emit('syncStuAnswer', this.id, value)
    }
  }
}
</script>

<template>
  <div>
    <CheckboxGroup
      type="checkbox"
      :data="item"
      :name="item.questionid"
      :reportType="reportType"
      @input="input"
    >
    </CheckboxGroup>
  </div>
</template>

<style lang="stylus" module>
</style>
