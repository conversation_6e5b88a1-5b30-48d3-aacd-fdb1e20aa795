// 根据文件的扩展名或文件名 给出文件的类型
export const getMediaType = (suffix) => {
  suffix = suffix.slice(suffix.lastIndexOf('.') + 1).toLowerCase()
  const types = {
    video: {
      format: 'mp4, mpg, mpeg, avi, rmvb, rm, wmv, mov, video, flv, mkv',
      size: 1024,
      transcoding: '自动转码(H.264编码的mp4格式)，切片(流媒体播放)，压缩(128~1024kbps)'
    },
    audio: {
      format: 'mp3, aac, wav, wma, ogg, m4a, audio',
      size: 50,
      transcoding: '自动转码(AAC编码的mp3格式)，切片(流媒体播放)，压缩'
    },
    image: {
      format: 'png, jpg, jpeg, gif, bmp, image', // 加image是为了兼容客户端传回来的内容
      size: 2,
      transcoding: '--'
    },
    doc: {
      format: 'doc, docx',
      size: 50,
      transcoding: '转图片'
    },
    xls: {
      format: 'xls, xlsx',
      size: 50,
      transcoding: '转图片'
    },
    pdf: {
      format: 'pdf',
      size: 50,
      transcoding: '转图片'
    },
    ppt: {
      format: 'ppt, pptx',
      size: 50,
      transcoding: '转图片'
    },
    zip: {
      format: 'zip, rar, 7z',
      size: 50,
      transcoding: '压缩'
    },
    txt: {
      format: 'txt',
      size: 50,
      transcoding: '文本'
    }
  }
  for (let type in types) {
    if (types.hasOwnProperty(type)) {
      const exts = types[type].format
      if (exts.indexOf(suffix) >= 0) {
        return type
      }
    }
  }
  return 'other'
}
// 根据给定的大小B转换成MB等单位
export const formatSize = (size) => {
  if (size === undefined || /\D/.test(size)) {
    return 'invalid data'
  }
  const round = (num, precision) => {
    return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision)
  }
  let boundary = Math.pow(1024, 4)
  // TB
  if (size > boundary) {
    return round(size / boundary, 1) + 'TB'
  }
  // GB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'GB'
  }
  // MB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'MB'
  }
  // KB
  if (size > 1024) {
    return Math.round(size / 1024) + 'KB'
  }
  if (size === '') {
    return ''
  }
  return size + 'b'
}
