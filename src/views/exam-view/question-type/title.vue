<script>
export default {
  props: {
    partname: String,
    commit: String,
    order: Number,
    titleContent: String,
    inComposite: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<template>
  <div>
    <h2 :class="$style.partname" v-if="partname">{{partname}}</h2>
    <div :class="$style.head">
      <small v-if="!inComposite" :class="$style.commit">{{commit}}</small>
      <div :class="$style.title">
        <i v-if="inComposite" :class="$style.order">{{order + 1}}</i>
        <slot><div v-html="titleContent" :class="$style.titleText"></div></slot>
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.partname
  height 44px
  padding 0 16px
  font-size 14px
  line-height 44px
  color #444
  background-color #f1f3f7
  white-space nowrap
  overflow hidden
  text-overflow ellipsis

.head
  padding 30px 16px
  position relative

  /.commit
    position absolute
    top 0
    left 0
    padding 0 16px 0 8px
    height 20px
    font-size 12px
    line-height 20px
    background-color #ffefe2
    color #ff9c62
    border-bottom-right-radius 20px

  /.title
    font-size $font-size-medium-x
    line-height 1.5
    display flex
    align-items center

    /.order
      flex 0 0 20px
      width 20px
      height 20px
      text-align center
      line-height 20px
      font-size 12px
      color #ff9c62
      background-color #ffefe2
      border-radius 50%
      font-style normal
      margin-right 8px

    /.titleText
      flex 1 1
</style>
