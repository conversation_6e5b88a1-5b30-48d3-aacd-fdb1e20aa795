<script>
import Draggable from 'vuedraggable'
import AttachFile from '../attach'
import { decimal2Letter } from '@/common/utils'
export default {
  components: {
    Draggable,
    AttachFile
  },
  props: ['item', 'reportType'],
  data () {
    return {
      choosing: false,
      // actives: [],
      options: {
        sort: this.reportType === 0,
        animation: 30,
        touchStartThreshold: 5,
        ghostClass: this.$style.ghost,
        delay: 0,
        // dragClass: this.$style.drag,
        fallbackOnBody: true,
        chosenClass: this.reportType === 0 ? this.$style.chosen : undefined
      },
      list: [],
      noAnswer: false,
      answerList: []
    } 
  },
  watch: {
    list (val) {
      this.answerList = this.list2StuAnswer(val)
      this.$emit('syncStuAnswer', this.id, this.answerList)
      console.log(this.list2StuAnswer(val))
      this.noAnswer = false
    }
  },
  created () {
    const stuAnswer = this.item.studentAnswer
    const list = this.item.item || []
    if (Array.isArray(stuAnswer) && stuAnswer.length === list.length && stuAnswer.every(it => it)) {
      this.list =  this.stuAnswer2List(stuAnswer, list)
    } else {
      this.list = this.genList(list)
      this.noAnswer = true
    }
  },
  computed: {
    id () {
      return this.item.questionid
    },
    // list: {
    //   get () {
    //     // const list = this.item.item || []
    //     // return this.genList(list)
    //     const stuAnswer = this.item.studentAnswer
    //     const list = this.item.item || []
    //     if (Array.isArray(stuAnswer) && stuAnswer.length === list.length && stuAnswer.every(it => it)) {
    //       return this.stuAnswer2List(stuAnswer, list)
    //     } else {
    //       return this.genList(list)
    //     }
    //   },
    //   set (list) {
    //     this.$emit('syncStuAnswer', this.id, this.list2StuAnswer(list))
    //     console.log(this.list2StuAnswer(list))
    //   }
    // }
  },
  methods: {
    itemDragStart() {
      this.answerList = this.list2StuAnswer(this.list)
      this.$emit('syncStuAnswer', this.id, this.answerList)
      this.noAnswer = false
    },
    // 排序题选择时防止swiper滑动
    canSwiperSlide (moveFlag) {
      this.$emit('canSwiperSlide', moveFlag)
    },
    decimal2Letter (num) {
      return decimal2Letter(num, 26).toUpperCase()
    },
    genList (list) {
      return list.map((it, index) => {
        return {
          index: this.decimal2Letter(index),
          value: it
        }
      })
    },
    stuAnswer2List (stuAnswer, list) {
      let newList = this.genList(list)
      newList.sort((a, b) => {
        const prev = stuAnswer.indexOf(a.index);
        const next = stuAnswer.indexOf(b.index);
        return prev-next;
      })
      return newList
    },
    list2StuAnswer (list) {
      return list.map(it => this.noAnswer ? '' : it.index)
    },
    // choose () {
    //   this.choosing = true
    // },
    // start () {
    //   this.choosing = false
    // },
    // end (event) {
    //   this.choosing = false
    //   const newIndex = event.newIndex
    //   this.actives[newIndex] = true
    // },
    checkStuAnswer (index) {
      if (this.reportType === 2) {
        const checkResult = this.item.checkResult
        return checkResult[index] ? this.$style.checkRight : this.$style.checkError
      }
    }
  },
  mounted () {
    console.log(this)
  }
}
</script>

<template>
<div :class="$style.wrapper">
  <div :class="[$style.tip]">{{ $t('dragTip') }}</div>
  <Draggable v-model="list" :class="$style.sortWrapper" :options="options" @start="itemDragStart">
    <!-- <transition-group tag="div" name="sortlist"> -->
      <div v-for="(it, index) in list" :key="index" :class="[$style.item]" @touchstart="canSwiperSlide(false)" @touchend="canSwiperSlide(true)">
        <div :class="$style.text">
          <span :class="$style.order">{{ '[' + it.index + ']' }}</span>
          <div v-html="it.value.title" :class="$style.title"></div>
        </div>
        <div v-if="it.value.link" :class="{[$style.link]: true, [$style.linkSp] :it.title.length <= 0}">
          <attach-file :src="it.link"></attach-file>
        </div>
      </div>
    <!-- </transition-group> -->
  </Draggable>
  <div :class="$style.result" :style="{'justify-content': list.length >= 5 ? 'space-between' : '' }">
    <div v-for="(it, index) in answerList" :key="index" :class="[$style.resultItem, checkStuAnswer(index)]">{{ it ? '[' + it + ']' : '' }}</div>
  </div>
</div>
</template>

<style lang="stylus" module>
.wrapper
  padding 0 16px
.sortWrapper
  margin-bottom 16px
.tip
  text-align center
  font-size 14px
  padding-bottom 12px
  color $color-intro
  margin-top -14px
// .tipDrag
  // color $color-main

.item
  padding 8px 16px
  background #f1f3f7
  border-radius 4px
  margin-bottom 8px
  font-size 16px
  line-height 1.5
  cursor move
  color $text-color-default
  /.text
    display flex
    align-items center
  /.order
    flex 0 0 auto
  /.title
    flex 1 1 0
    margin-left 0.5em
.result
  display flex
  flex-wrap wrap
  // margin-right -10px
  margin-bottom 20px
  /.resultItem
    margin-right 10px
    margin-bottom 10px
    height 44px
    line-height 44px
    font-size 16px
    color $text-color-default
    text-align center
    box-sizing border-box
    flex 0 0 calc(20% - 10px)
    border 1px solid #e3e3e9
    position relative

.linkSp
  // margin -20px 46px 0 66px
  display inline-block
.drag
  box-shadow: 0 0 1px 1px rgba(44, 44, 44, .3) inset;
.ghost
  opacity 0
.chosen
  box-shadow none
  background #ffefe2

icon()
  position absolute
  top -10px
  right -10px
  width 25px
  height 25px
  font-family "iconfont"
  text-align center
  line-height 25px
  border-radius 50%
  color #fff
  transform scale(.8)
  font-size 12px

// 答案错误
.checkError
  background-color #ffeeee
.checkError::before
  content '\E83E'
  background-color $color-error
  icon()

// 答案正确
.checkRight
  background-color #f1f3f7
.checkRight::before
  content: '\E83D'
  background-color $color-right
  icon()
</style>
