<script>
import AttachFile from './attach' // 附件

// import Title from './title' // title

// 各个题型
// 1. 选择题
import SingleSelect from './select/single-select'
import MultiSelect from './select/multi-select'
// 2. 判断题
import Judge from './judge'
// 3. 填空题
import Blank from './blank'
// 4. 简答题
import BreifAnswer from './breif'
// 5. 排序题
import Sort from './sort'
// 5. 口语题
import ObjectSpeak from './speak/object-speak' // 主观题
import SubjectSpeak from './speak/subject-speak'
// 下拉选择题
import DropDown from './select/dropdown'
// 6. 综合题
import Composite from './composite'

// 25 公式计算题
import Formula from './formula'

// 答案
import Answer from './answer'

// 答题水印
import { setShadowRoot, mutationObserverInit } from "@/utils/watermark.js"

export { default as Head } from './head-part'
export default {
  components: {
    // Title,
    AttachFile,
    SingleSelect,
    MultiSelect,
    Judge,
    Blank,
    Sort,
    BreifAnswer,
    ObjectSpeak,
    SubjectSpeak,
    DropDown,
    Composite,
    Answer,
    Formula
  },
  props: ['item', 'partname', 'reportType','examInfo', 'waterMarkImg', 'isParent'],
  data () {
    return {
      types: {
        '1': { text: this.$t('question1'), component: 'SingleSelect' },
        '2': { text: this.$t('question2'), component: 'MultiSelect' },
        '3': { text: this.$t('question3'), component: 'Blank' },
        '4': { text: this.$t('question4'), component: 'Judge' },
        '5': { text: this.$t('question5'), component: 'BreifAnswer' },
        '12': { text: this.$t('question6'), component: 'Sort' },
        '16': { text: this.$t('question7'), component: 'ObjectSpeak' },
        '17': { text: this.$t('question8'), component: 'DropDown' },
        '19': { text: this.$t('question9New'), component: 'SubjectSpeak' },
        '23': { text: this.$t('question10'), component: 'DropDown' },
        '24': { text: this.$t('question11'), component: 'Composite' },
        '25': { text: this.$t('question12'), component: 'DropDown' },
        '26': { text: this.$t('question25'), component: 'Formula' }
      },
      columnMode: false
    }
  },
  mounted () {
    if (this.examInfo.waterMark && this.waterMarkImg) {
      this.setWaterMark()
    }
    if (this.isParent) {
      this.$refs.section.style.minHeight = `calc(100vh - ${this.$refs.section.getBoundingClientRect().top}px)`
    }
  },
  computed: {
    type () {
      return this.item.type
    },
    // 题目是被包含在综合题中
    inComposite () {
      return this.$parent.isComposite
    },
    columnBtn () {
      return this.columnMode
        ? { icon: 'icon-tuichu1', text: this.$t('exitScreen') }
        : { icon: 'icon-fenpingshangxia', text: this.$t('enterScreen') }
    },
    heading () {
      return this.$refs.heading
    },
    titleMaxWidth () {
      const width = this.inComposite ? 'calc(100% - 24px)' : '100%'
      return { 'max-width': width }
    },
    compositeClass () {
      const { type } = this
      return (type === 17 || type === 23 || type === 25) ? 'composite-mark' : ''
    }
  },
  methods: {
    // 限制次数
    limitCount (res) {
      const { lisCount } = this.item
      let count = 0
      if (res.linkLisCount) {
        count = res.linkLisCount > -1 ? res.linkLisCount : Infinity
      } else if (lisCount >= 0) {
        count = lisCount
      } else {
        count = Infinity
      }
      const { reportType } = this
      if (reportType === 0) {
        return count
      } else {
        return Infinity
      }
    },
    // 保存用户作答
    syncStuAnswer (id, answer) {
      this.$emit('syncStuAnswer', id, answer)
    },
    // 排序题选择时，是否可以滑动
    canSwiperSlide (moveFlag) {
      this.$emit('canSwiperSlide', moveFlag)
    },
    // 问题id
    syncAudioCount (id) {
      this.$emit('syncAudioCount', id)
    },
    columnToogle () {
      this.columnMode = !this.columnMode
      if (this.columnMode === true) {
        this.$nextTick(() => {
          this.$refs.section.style.height = `calc(100vh - ${this.$refs.section.getBoundingClientRect().top}px)`
          this.sectionHeight = this.$refs.section.getBoundingClientRect().height
          this.height = Math.min(this.heading.getBoundingClientRect().height, this.sectionHeight * 0.5)
          this.heading.style.height = this.height + 'px'
          document.querySelector(".swiper-wrapper").style.height = this.sectionHeight + 'px'
        })
      } else {
        this.$refs.section.style.height = "100%"
        this.height = this.heading.getBoundingClientRect().height
        this.heading.style.height = 'auto'
        this.updateSlide()
        this.$nextTick().then(() => this.$emit('onScroll'))
      }
    },
    onTouchStart (event) {
      const touch = event.touches[0]
      this.pointX = touch.pageX
      this.pointY = touch.pageY
      this.startTime = Date.now()
    },
    onTouchMove (event) {
      if (Date.now() - this.startTime < 80) {
        return
      }
      const touch = event.changedTouches[0]
      const deltaX = touch.pageX - this.pointX
      const deltaY = touch.pageY - this.pointY
      this.pointX = touch.pageX
      this.pointY = touch.pageY
      const absDeltaX = Math.abs(deltaX)
      const absDeltaY = Math.abs(deltaY)
      if (absDeltaY / absDeltaX > 1.7321) {
        this.height += deltaY
        const sectionHeight = this.sectionHeight
        if (this.height <= sectionHeight * 0.2) {
          this.height = sectionHeight * 0.2
        } else if (this.height >= sectionHeight * 0.8) {
          this.height = sectionHeight * 0.8
        }
        this.heading.style.height = this.height + 'px'
      }
    },
    onScroll () {
      this.$emit('onScroll')
    },
    // 添加水印
    setWaterMark() {
      const { questionItem } = this.$refs;
      if (questionItem) {
        const option = {
          'childList': true,
          'attributes': true,
          'subtree': true
        };
        let watermarkDom = mutationObserverInit(this.waterMarkImg, option)
        const dom = setShadowRoot(this.waterMarkImg);
        questionItem.appendChild(dom);
        watermarkDom.observe(questionItem, option);
        dom.shadowRoot && watermarkDom.observe(dom.shadowRoot, option);
      }
    },
    updateSlide() {
      this.$emit("updateSlide")
    },
    changeHeight(visible, top) {
      const wrapper = document.querySelector(".composite-mark")
      this.$refs.section.style.height = visible ? `${wrapper.clientHeight}px` : '100%'
      this.heading.style.position = visible ? 'absolute' : ''
      this.heading.style.top = visible ? `-${top}px` : ''
      this.updateSlide()
      if (!visible) {
        wrapper.scrollTo(0, top)
      }
    },
    renderFormula() {
      this.$emit('renderFormula')
    }
  }
}
</script>

<template>
  <section v-if="types[type]" :class="[$style.wrapper, compositeClass]" @copy.prevent @cut.prevent @paste.prevent ref="section">
    <div :class="[{'question-item': examInfo.waterMark && waterMarkImg}, columnMode && $style.columnMode]" ref="questionItem">
      <div :class="[$style.heading, columnMode && $style.columnModeHead ]" ref="heading">
        <h2 :class="$style.partname" v-if="partname">{{partname}}</h2>
        <div class="zIndex" :class="$style.demand">
          <small v-if="!inComposite && !examInfo.hideQuestionType" :class="$style.commit">{{types[type].text}}</small>
          <button v-if="type === 24" :class="$style.columnBtn" @click="columnToogle">
            <i class="iconfont" :class="columnBtn.icon"></i>
            {{columnBtn.text}}
          </button>
          <div :class="$style.title">
            <i v-if="inComposite" :class="$style.order">{{item.order + 1}}</i>
            <div v-html="item.title" ref="title" :class="[$style.titleText, 'has-img']" :style="titleMaxWidth"></div>
          </div>
          <div :class="$style.attach" v-if="item.linkList && item.linkList.length > 0">
            <AttachFile v-for="(res, index) in item.linkList" :key="index" :src="res.link" :limitCount="limitCount(res)" :resource="res" @save="syncAudioCount(item.questionid)" :id="item.questionid" />
          </div>
        </div>
      </div>
      <div v-if="type === 24 && columnMode" :class="$style.dragZone" @touchstart="onTouchStart" @touchmove="onTouchMove" class="drag-mark"></div>
      <div :class="[$style.main,  columnMode && $style.columnModeMain]" @scroll="onScroll">
        <component
          ref="question"
          class="zIndex"
          :is="types[item.type].component"
          :item="item"
          :examInfo="examInfo"
          :reportType="reportType"
          @syncStuAnswer="syncStuAnswer"
          @syncAudioCount="syncAudioCount"
          @canSwiperSlide="canSwiperSlide"
          @updateSlide="updateSlide"
          @changeHeight="changeHeight"
          @renderFormula="renderFormula"
        ></component>
        <Answer v-if="item.type !== 24 && item.type !== 23 && item.type !== 17 && item.type !== 25" :data="item" :reportType="reportType"/>
      </div>
    </div>
  </section>
</template>

<style lang="stylus" module>
.wrapper
  word-break: break-word;
  height 100%
  // margin-bottom 20px
  overflow-y auto
  -webkit-overflow-scrolling touch

.heading
  width 100%

.partname
  height 44px
  padding 0 16px
  font-size 14px
  line-height 44px
  color #444
  background-color #f1f3f7
  white-space nowrap
  overflow hidden
  text-overflow ellipsis

.demand
  padding 30px 16px
  position relative

  /.commit
    position absolute
    top 0
    left 0
    padding 0 16px 0 8px
    height 20px
    font-size 12px
    line-height 20px
    background-color #ffefe2
    color #ff9c62
    border-bottom-right-radius 20px

  /.title
    font-size $font-size-medium-x
    line-height 1.5
    display flex
    align-items center

    /.order
      flex 0 0 21px
      width 21px
      height 21px
      text-align center
      line-height 20px
      font-size 12px
      color #ff9c62
      background-color #ffefe2
      border-radius 50%
      font-style normal
      margin-right 8px

    /.titleText
      flex 1 1
      /b,strong
        font-weight bold
      ol
        list-style-type: decimal;
        list-style-position: inside;
      ul
        list-style-type: disc;
        list-style-position: inside;
      li
        list-style: inherit;
      img 
        vertical-align middle
/.MathJax_SVG
  /svg
    max-width 100%

.attach
  padding 8px 0 16px 0
  > div
    margin-bottom 8px

// .main
  // padding-bottom 20px

// 分屏模式
.columnBtn
  position absolute
  top 0
  right 16px
  font-size 15px
  color $color-main
  background transparent
  border none
  outline none
  line-height 30px
  padding 0

  i
    font-size 18px

.columnMode
  height 100%
  display flex
  flex-flow column nowrap

.columnModeHead
  // max-height 80%
  // min-height 20%
  overflow-y auto
  flex 0 0 auto

.dragZone
  flex 0 0 auto
  height 56px
  position relative
  margin-bottom -24px
  z-index 10
  &::after
    content ''
    position absolute
    background #f1f3f7
    width 56px
    height 8px
    top 50%
    left 50%
    margin -4px 0 0 -28px
.columnModeMain
  background-color #f1f3f7
  overflow-y auto
  padding-bottom 20px
</style>
<style>
.question-item .zIndex,
.question-item .opacity,
.question-item > div {
  z-index: 2;
}
.question-item .opacity,
.question-item .blank-input,
.question-item .select-input-blank,
.question-item .words-list-wrap {
  opacity: 0.8;
}
</style>

