<script>
// const map = new Map()
// window.customMap = map
export default {
  props: ['index', 'data'],
  data() {return {
    locale: ""
  }},
  created() {
    this.locale = this.$i18n.locale
    console.log("en:", this.locale == 'en')
  },
  computed: {
    totalScore () {
      return this.data.children.reduce((accu, curr) => accu + curr.score, 0)
    }
  }
}
</script>

<template>
  <div :class="$style.wrapper">
    <div :class="$style.title">
      <span :class="$style.index">
        <i>{{index + 1}}</i>
      </span>
      <span :class="$style.name">{{data.partname}}</span>
    </div>
    <p :class="$style.info">
      <span>
        <i class="iconfont icon-lianxishuliang"></i>
        <em>{{ $t('totalNum', { num: data.children.length }) }}</em>
      </span>
      <span>
        <i class="iconfont icon-jiangbei"></i>
        <em>{{ $t('points', { n: totalScore }) }}</em>
      </span>
    </p>
    <p :class="$style.desc">{{data.partDesc}}</p>
    <div :class="{ [$style.tip]: true, [$style.tip_en]: locale == 'en', [$style.tip_tw]: locale == 'tw', [$style.tip_th]: locale == 'th', [$style.tip_es]: locale == 'es', [$style.tip_id]: locale == 'id', [$style.tip_ar]: locale == 'ar' }" v-if="index === 0"></div>
  </div>
</template>

<style lang="stylus" module>
.wrapper
  padding 60px 30px 70px

  /.title
    display flex
    align-items center
    margin-bottom 16px

  /.index
    position relative
    float left
    width 70px
    height 60px
    margin-right 10px
    background url('@/assets/images/partnum.png')
    background-size contain

    i
      position absolute
      left 18px
      top 12px
      transform rotate(-15deg)
      font-size $font-size-medium-x
      font-style normal
      color $color-main

  /.name
    display -webkit-box
    flex 1
    -webkit-line-clamp 2
    -webkit-box-orient vertical
    font-size $font-size-large-m
    line-height 1.2
    font-weight bold
    word-wrap break-word
    overflow hidden
    text-overflow ellipsis
    color #444
  /.info
    display flex
    padding 25px 30px
    margin-bottom 20px
    align-items center
    background url('@/assets/images/tinum_bj.png') no-repeat center
    background-size cover
    text-align center
    border-radius 4px
    i
      color #fff
    span
      flex 1
      font-size $font-size-medium-x
      color #fff

      &:first-child
        margin-right 30px

      i
        margin-right 8px
        font-size $font-size-icon
        vertical-align middle

      em
        font-style normal
        vertical-align middle

  /.desc
    font-size $font-size-medium-x
    line-height 1.875
    color $text-color-default
    overflow hidden
    text-overflow ellipsis

  /.tip
    max-width 300px
    height 160px
    margin 20px auto 0
    background url('@/assets/images/i18n/zh/switch.png') no-repeat center
    background-size contain

  /.tip_en
    background-image: url('@/assets/images/i18n/en/switch.png');
  /.tip_es
    background-image: url('@/assets/images/i18n/es/switch.png');
  /.tip_id
    background-image: url('@/assets/images/i18n/id/switch.png');
  /.tip_th
    background-image: url('@/assets/images/i18n/th/switch.png');
  /.tip_tw
    background-image: url('@/assets/images/i18n/tw/switch.png');
  /.tip_ar
    background-image: url('@/assets/images/i18n/ar/switch.png');
</style>
