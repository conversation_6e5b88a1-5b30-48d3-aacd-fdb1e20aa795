<template>
  <div :class="$style.record" @click="toggle">
    <Icon :disabled="disabled" :title="title" icon="icon-kouyu_luyin"></Icon>
    <div v-show="isRecording" :class="[$style.panel]">
      <time :class="$style.time">{{formatDuration(duration)}}</time>
      <img src="@/assets/images/pf_recording.gif" :alt="$t('recording') + '...'">
      <div :class="$style.label">{{ $t('stopRecord') }}</div>
    </div>
  </div>
</template>

<script>
import Icon from './icon'
import stopAllMedia from '@/utils/stopAllMedia'
import Bus from '@/utils/bus'
import { getPlatform } from '@/common/utils'
import { checkRecorderPermission, startRecord, stopRecord } from '@/lib/cordovaPlugin/mobilePlugin'
export default {
  components: {
    Icon
  },
  props: {
    title: {
      default: ''
    },
    value: {},
    time: {
      default: 0
    },
    disabled: {
      default: false
    }
  },

  data () {
    return {
      isRecording: false,
      duration: 0
    }
  },

  methods: {
    // 开始录音
    startRecordFn () {
      this.isRecording = 0
      Bus.$emit('video-record-open', false)
      return checkRecorderPermission(() => {
        Bus.$emit('video-record-open', true)
        stopAllMedia()
        startRecord((src) => {
          this.onCompleteRecord(src)
        }, () => {
          this.onBeforeStartRecord()
        })
      },(e) => {
        Bus.$emit('video-record-open', true)
        // console.error(e)
        this.isRecording = false
        if (!getPlatform().isIOS) {
          this.$createDialog({
            type: 'alert',
            content: this.$t('mobilePermissionTip2')
          }).show()
        }
        throw e
      })
    },
    stopRecordFn () {
      return stopRecord(() => {
        this.onBeforeStopRecord()
      })
    },
    // 重录
    reRecord () {
      if (!this.value) {
        this.startRecordFn()
      } else {
        this.$createDialog({
          type: 'confirm',
          content: this.$t('reRecordTip'),
          confirmBtn: { text: this.$t('confirm'), active: true },
          cancelBtn: { text: this.$t('cancel') },
          onConfirm: () => {
            // this.$emit('input', '')
            this.startRecordFn()
          }
        }).show()
      }
    },
    // 录音完成后，同步录音音频的本地地址
    onCompleteRecord (src) {
      this.$emit('input', src)
    },
    // 开始录音前
    onBeforeStartRecord () {
      this.isRecording = true
      this.duration = 0
      this.timer = setInterval(() => {
        this.duration++
        if (this.time !== 0 && this.duration >= this.time) {
          this.stopRecordFn()
          clearInterval(this.timer)
        }
      }, 1000)
    },
    // 停止录音前
    onBeforeStopRecord () {
      this.isRecording = false
      clearInterval(this.timer)
    },
    toggle () {
      if (this.disabled === true) return
      if (this.isRecording === 0) {
        return
      }
      if (this.isRecording === true) {
        this.stopRecordFn()
      } else {
        this.reRecord()
      }
    },
    formatDuration (duration) {
      let minute = duration / 60 | 0
      minute = minute < 10 ? `0${minute}` : minute
      let second = duration % 60
      second = second < 10 ? `0${second}` : second
      return `${minute}:${second}`
    }
  },
  destroyed () {
    clearInterval(this.timer)
    if(this.isRecording){
      this.stopRecordFn()
    }
  }
}
</script>
<style lang="stylus" module>
.record :global(.speak-icon)
  width 60px
  height 60px
  margin-bottom 8px

.panel
  position absolute
  top 0
  left 0
  z-index 10
  background-color #fff
  padding-top 40px
  padding-bottom 80px
  text-align center
  img
    width 100%
    margin-bottom 20px
.time
  display block
  font-size 16px
  color #e3e3e9
.label
  display block
  font-size 12px
  color $color-intro
</style>
