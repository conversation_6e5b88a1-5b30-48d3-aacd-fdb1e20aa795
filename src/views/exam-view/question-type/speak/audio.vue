<template>
  <div @click="toogle" :class="$style.wrapper">
    <div :class="$style.progress">
      <svg width="60" height="60" viewbox="0 0 60 60" v-show="!paused && hasDuration" class="svg">
        <circle cx="30" cy="30" r="27.5" stroke-width="3" stroke="#fff" fill="none"></circle>
        <circle cx="30" cy="30" r="27.5" stroke-width="3" stroke="#ea5947" fill="none" transform="matrix(0,-1,1,0,0,60)" stroke-dasharray="0 100" class="ring-circle"></circle>
      </svg>
    </div>
    <Icon :disabled="disabled" :title="title" :icon="icon" :class="[$style.icon, (!paused && hasDuration) ? $style.pausedIcon : '']"></Icon>
    <audio
      data-role="speak-audio"
      :src="value"
      ref="player"
    >
      <source :src="value" type="audio/mpeg">
    </audio>
  </div>
</template>

<script>
import Icon from './icon'
// let index = 1
export default {
  components: {
    Icon
  },
  props: {
    title: {
      default: ''
    },
    value: {
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      paused: true,
      hasDuration: true
    }
  },
  computed: {
    icon () {
      if (this.title === this.$t('example')) {
        return this.paused ? 'icon-audio_paly' : 'icon-audio_pause'
      } else if (this.title === this.$t('replay')) {
        return this.paused ? 'icon-bofangluyin' : 'icon-audio_pause'
      }
    }
  },
  mounted () {
    const media = this.$refs.player
    media.addEventListener('playing', () => this.setPlay())
    media.addEventListener('pause', () => this.setPause())
  },

  methods: {
    play () {
      const media = this.$refs.player
      media.play()
      // 环形进度条播放一次后没有清空 
      if(!media.currentTime) {
        this.$el.querySelector('.ring-circle').setAttribute('stroke-dasharray', '0 180')
      }
    },
    pause () {
      const media = this.$refs.player
      media.pause()
    },
    toogle () {
      if (this.disabled) return
      if (this.paused) {
        this.play()
      } else {
        this.pause()
      }
    },
    /**
     * 使用环状进度条标记播放进度
     */
    setPlay () {
      this.paused = false
      const media = this.$refs.player
      const duration = media.duration
      // console.log(duration)
      if (Number.isFinite(duration)) {
        this.hasDuration = true
        const perimeter = 27.5 * Math.PI * 2
        const circle = this.$el.querySelector('.ring-circle')
        this.__timer = setInterval(() => {
          const percentage = media.currentTime / duration
          circle.setAttribute('stroke-dasharray', perimeter * percentage + ' ' + perimeter * (1 - percentage))
        }, 1000)
      } else {
        this.hasDuration = false
      }
    },
    setPause () {
      this.paused = true
      clearInterval(this.__timer)
    }
  },
  destroyed () {
    clearInterval(this.__timer)
  }
}
</script>

<style lang="stylus" module>
.wrapper
  position relative
  :global(.speak-icon)
    margin-bottom 13px

.progress
  width 60px
  height 60px
.icon
  position absolute
  top 5px
  left 5px
.pausedIcon button
  box-shadow none

.svg
  -webkit-transform: rotate(-0.05deg);
  transform: rotate(-0.05deg);
</style>
