<template>
  <div :class="$style.wrapper">
    <div :class="$style.topic">
      <p :class="$style.hint">{{ $t('speakTip1') }}<span :class="$style.prompt">{{ $t('speakTip2') }}</span>{{ $t('speakTip3') }}</p>
      <p :class="$style.desc" v-if="item.article" v-html="item.article"></p>
    </div>
    <!-- time 单位 秒 -->
    <MediaPanel :exampleSrc="item.speak || ''" :disabled="reportType !== 0" v-model="computedValue" :time="180" ref="media"/>
  </div>
</template>

<script>
import MediaPanel from './media-panel'
export default {
  components: {
    MediaPanel
  },
  props: ['item', 'reportType'],
  computed: {
    id () {
      return this.item.questionid
    },
    computedValue: {
      get () {
        return this.item.studentAnswer || ''
      },
      set (value) {
        this.$emit('syncStuAnswer', this.id, value)
      }
    }
  },
  mounted () {
    const ref = this.$refs.media.recordRef
    this.item.__recordRef = ref
  }
}
</script>

<style lang="stylus" module>
.wrapper
  margin-top -14px
.topic
  padding 16px
  border-top 1px solid #e3e3e9
  font-size 16px
  line-height 24px
  .hint
    margin-bottom 8px
  .prompt
    color $color-main
  .desc
    max-height 144px
    overflow-y auto
    line-height 24px
</style>
