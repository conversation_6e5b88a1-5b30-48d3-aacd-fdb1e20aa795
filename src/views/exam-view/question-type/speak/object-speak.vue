<template>
  <div :class="$style.wrapper">
    <MediaPanel :exampleSrc="item.speak || ''" :disabled="reportType !== 0" v-model="computedValue" ref="media"/>
  </div>
</template>

<script>
import MediaPanel from './media-panel'
export default {
  components: {
    MediaPanel
  },
  props: ['item', 'reportType'],
  computed: {
    id () {
      return this.item.questionid
    },
    computedValue: {
      get () {
        return this.item.studentAnswer || ''
      },
      set (value) {
        this.$emit('syncStuAnswer', this.id, value)
      }
    }
  },
  mounted () {
    const ref = this.$refs.media.recordRef
    this.item.__recordRef = ref
  }
}
</script>

<style lang="stylus" module>
.wrapper
  margin-top -14px
</style>
