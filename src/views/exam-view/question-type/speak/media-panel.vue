<template>
  <div :class="$style.container">
    <ul :class="$style.panel">
      <li>
        <UAudio v-show="computedExampleSrc" :value="computedExampleSrc" :title="$t('example')"></UAudio>
      </li>
      <li>
        <Record
          :title="$t('record')"
          @input="input"
          :value="value"
          :time="time"
          ref="record"
          :disabled="disabled"
        ></Record>
      </li>
      <li :class="$style.recordAudio">
        <UAudio v-show="computedReplySrc && !isUploading" :value="computedReplySrc" :title="$t('replay')"></UAudio>
      </li>
    </ul>
  </div>
</template>
<script>
import Audio from './audio'
import Record from './record'
import { formatUrl } from '@/common/utils'
import { createUploade } from '@/lib/cordovaPlugin/mobilePlugin';

let timer
export default {
  components: {
    UAudio: Audio,
    Record
  },
  props: {
    // 示范音
    exampleSrc: {
      type: String
    },
    // 录音同步
    value: {
      type: String
    },
    time: {
      type: Number,
      default: 0 // 主观题无录音限制 0表示无录音时间限制
    },
    disabled: {
      type: Boolean
    }
  },

  data () {
    return {
      isUploading: false
    }
  },

  computed: {
    computedExampleSrc () {
      const src = formatUrl(this.exampleSrc)
      return src
    },
    computedReplySrc () {
      return formatUrl(this.value)
    },
    recordRef () {
      return this.$refs.record
    }
  },

  methods: {
    input (value) {
      // 此处上传
      this.isUploading = true
      const toast = this.$createToast({ type: 'loading', txt: this.$t('uploading') + '...', mask: true, time: 0 }).show()
      clearTimeout(timer)
      timer = setTimeout(() => {
        toast.show()
      }, 1200)
      this.uploader = createUploade()
      this.uploader.upload(value, 
        (localPath, remotePath) => {
          this.isUploading = false
          clearTimeout(timer)
          toast.hide()
          this.$emit('input', remotePath)
        },
        (e) => {
          if(e === 'Invalid action') {
            // 安卓底层报错，不提示
            return
          }
          this.isUploading = false
          clearTimeout(timer)
          toast.hide()
          this.$createToast({
            type: 'error',
            txt: this.$t('recordUploadFailed'),
            time: 1800
          }).show()
          throw e
        }
      )
    }
  },
  mounted () {}
}
</script>
<style lang="stylus" module>
.container
  padding-top 50px
  padding-bottom 60px
  position relative
  border-top 1px solid #e3e3e9

.panel
  display flex
  justify-content center
  align-items flex-start

  > li
    flex 0 1 auto
    margin-right 20px

  > li:last-child
    margin-right 0
</style>
