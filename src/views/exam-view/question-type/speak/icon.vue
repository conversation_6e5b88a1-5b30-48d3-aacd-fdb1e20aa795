<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String // 传入一个表示字体的类
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<template>
  <div class="speak-icon-wrap">
    <button :class="['speak-icon', {'speak-icon-disabled': disabled}, icon]"></button>
    <span class="speak-icon-label">{{title}}</span>
  </div>
</template>

<style lang="stylus">
.speak-icon-wrap
  text-align center
  /.speak-icon
    display block
    box-sizing border-box
    color $color-main
    width 50px
    height 50px
    font-size 26px
    line-height 50px
    border-radius 50%
    box-shadow 0 0 5px 2px rgba(44, 44, 44, .3)
    border none
    outline: none
    font-family "iconfont"
    padding 0
    background-color #fff
    margin-bottom 8px
  /.speak-icon-disabled
    color #dedede
  /.speak-icon-label
    display block
    color $color-intro
    font-size 12px
    line-height 1
</style>
