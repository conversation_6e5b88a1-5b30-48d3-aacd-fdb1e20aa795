<script>
import Bus from '@/utils/bus'
import { debounce } from 'lodash'
import { getComputedCorrect, evalFormula } from '@/utils/formula'

const renderInput = (value, openSafeKeyboard) => {
  if (openSafeKeyboard) {
    return `<span class="text-input-wrap blank-input-wrap">
              <input type="text" class="blank-input" readonly value="${value}" />
              <span class="blank-input blank-input-follow"></span>
            </span>`
  }
  return ` <input type="text"  class="blank-input" value="${value}"> `
}

const renderNoAnswer = record => {
  return ` <span class="blank-echo"> ${record} </span> `
}

export default {
  components: {},
  props: ['item', 'reportType', 'examInfo'],
  computed: {
    id () {
      return this.item.questionid
    },
    parentTitle () {
      return this.$parent.$refs.title
    },
    stuAnswer () {
      return this.item.studentAnswer || []
    },
    referAnswer () {
      return this.item.referAnswer || []
    }
  },
  methods: {
    genTitle () {
      const blankReg = /[(（](\s|&nbsp;)+[)）]|_{5,}/gi
      const stuAnswer = this.stuAnswer
      const referAnswer = this.referAnswer
      let index = 0
      let html = ''
      if (this.reportType === 0) {
        html = this.item.title.replace(blankReg, (match) => {
          return renderInput(stuAnswer[index++] || '', this.examInfo.openSafeKeyboard)
        })
      } else if (this.reportType === 1) {
        html = this.item.title.replace(blankReg, (match) => {
          return renderNoAnswer(stuAnswer[index++] || '')
        })
      } else if (this.reportType === 2) {
        html = this.item.title.replace(blankReg, (match) => {
          const checkResult = evalFormula({
            correct: JSON.parse(referAnswer[index]),
            formulaVar: this.item.formulaVar,
            reply: stuAnswer[index]
          })
          const html = this.renderWithAnswer(checkResult || '', stuAnswer[index] || '', referAnswer[index] || '')
          index++
          return html
        })
      }
      return html
    },
    input () {
      const reportType = this.reportType
      if (!reportType) {
        const title = this.parentTitle
        const arr = Array.from(title.querySelectorAll('input.blank-input'))
        const res = new Array(arr.length).fill('')
        var correntNum = 0
        var correct = JSON.parse(this.item.correctAnswer)
        arr.forEach((it, index) => {
          res[index] = it.value.trim()
          var result = evalFormula({
            correct: correct[index],
            formulaVar: this.item.formulaVar,
            reply: res[index]
          })
          if (result) {
            correntNum++
          }
        })
        var grade = correntNum * this.item.score / arr.length
        grade = grade.toFixed(1)
        console.log(this.id, res, 'formula', grade)
        this.$emit('syncStuAnswer', this.id, {res,grade})
      }
    },
    renderWithAnswer (res, record, answer) {
      if (res) {
        return ` <span class="blank-check-right">${record}</span> `
      } else {
        return ` <span class="blank-check-error">${record || this.$t('noAnswer')}</span> <span class="blank-check-right">(` + this.formulaCorrect(answer) +  `)</span> `
      }
    },
    formulaCorrect (item) {
      let arr = []
      // this.referAnswer.map((item,index) => {
        item = JSON.parse(item)
        let str = getComputedCorrect({
          correct: item,
          formulaVar: this.item.formulaVar
        })
        // arr.push(str + '±' + item.answerRange)
      // })
      // return arr.join(';')
      return str + '±' + item.answerRange
    },
    handleClick(e) {
      e.stopPropagation();
      e.changeFun = debounce(this.input, 100)
      Bus.$emit('text-input-click', {
        e: e,
        parent: this.$parent
      })
    }
  },
  mounted () {
    const html = this.genTitle()
    this.parentTitle.innerHTML = html
    // this.item.correctAnswer = [{ 'answerRange': 0, 'formula': 'a-b', 'precision': 1 }, { 'answerRange': 0, 'formula': 'a-a', 'precision': 1 }]
    // this.item.formulaVar = [{ 'value': 3, 'name': 'a' }, { 'value': 3, 'name': 'b' }]
    console.log(this)
    if (!this.reportType) {
      this.parentTitle.style.lineHeight = '40px'
      const input = debounce(this.input, 100)
      this.parentTitle.addEventListener('input', input)
      if (this.examInfo.openSafeKeyboard || this.examInfo.enableCalculator) {
        const inputDoms = this.parentTitle.querySelectorAll('input.blank-input')
        inputDoms.forEach(it => {
          it.addEventListener('focus', this.handleClick)
          it.addEventListener('click', this.handleClick)
          it.addEventListener('touchstart', (e) => {
            e.stopPropagation()
          })
          it.addEventListener('touchend', (e) => {
            Bus.$emit('text-input-scroll')
          })
          it.addEventListener('scroll', () => {
            Bus.$emit('text-input-scroll')
          })
          it.addEventListener('blur', (e) => {
            Bus.$emit('text-input-item-blur', e)
          })
        })
      }
    }
      var doms = [...this.parentTitle.getElementsByClassName('formula-param')]
      var formulaVars = this.item.formulaVar
      doms.forEach(function (item) {
        var param = item.getAttribute('data-content')
        for (var i = 0; i < formulaVars.length; i++) {
          if (formulaVars[i].name == param) {
            item.innerHTML = formulaVars[i].value
            // item.innerHTML = 3
            break
          }
        }
      })
    this.$nextTick().then(() => {
      // this.parentTitle.innerHTML = html
      // if (!this.reportType) {
      //   this.parentTitle.style.lineHeight = '40px'
      //   const input = debounce(this.input, 100)
      //   this.parentTitle.addEventListener('input', input)
      // }
      // this.parentTitle.addEventListener('paste', function() {
      //   return false
      // })
      // this.parentTitle.addEventListener('copy', function() {
      //   return false
      // })
    })
  }
}
</script>

<template>
  <div :class="$style.formulaQuestion"></div>
</template>

<style lang="stylus" module>
.formulaQuestion
  color red
:global(.blank-input)
  border 1px solid $color-border-default
  box-sizing border-box
  border-radius 4px
  min-width 60px
  height 100%
  background-color transparent
  outline none
  vertical-align middle
  height 36px
  line-height 1.5em
  padding 6px 10px
  background-color #fff

:global(.formula-param)
  color #ea5947

// 渲染记录
:global(.blank-echo)
  display inline-block
  height 40px
  min-width 60px
  line-height 40px
  border 1px solid $color-border-default
  background-color #fff
  vertical-align middle
  word-wrap break-word
  padding 0 0.5em

// right error
:global(.blank-check-right)
  color $color-right

:global(.blank-check-error)
  color $color-error
  text-decoration line-through
  word-wrap break-word
</style>
