<script>
import { debounce } from 'lodash'
import Bus from '@/utils/bus'
const renderInput = (value, openSafeKeyboard) => {
  if (openSafeKeyboard) {
    return `<span class="text-input-wrap blank-input-wrap">
              <input type="text" class="blank-input" readonly value="${value}" />
              <span class="blank-input blank-input-follow"></span>
            </span>`
  }
  return ` <input type="text"  class="blank-input" value="${value}"> `
}

const renderNoAnswer = record => {
  if (record) {
    return ` <span class="blank-echo blank-echo-new"> ${record} </span> `
  } else {
    return ` <span class="blank-echo"> ${record} </span> `
  }
}

export default {
  components: {},
  props: ['item', 'reportType', 'examInfo'],
  computed: {
    id () {
      return this.item.questionid
    },
    parentTitle () {
      return this.$parent.$refs.title
    },
    stuAnswer () {
      return this.item.studentAnswer || []
    },
    referAnswer () {
      return this.item.referAnswer || []
    }
  },
  methods: {
    genTitle () {
      const blankReg = /[(（](\s|&nbsp;)+[)）]|_{5,}/gi
      const stuAnswer = this.stuAnswer
      const referAnswer = this.referAnswer
      let index = 0
      let html = ''
      if (this.reportType === 0) {
        html = this.item.title.replace(blankReg, (match) => {
          return renderInput(stuAnswer[index++] || '', this.examInfo.openSafeKeyboard)
        })
      } else if (this.reportType === 1) {
        html = this.item.title.replace(blankReg, (match) => {
          return renderNoAnswer(stuAnswer[index++] || '')
        })
      } else if (this.reportType === 2) {
        const checkResult = this.item.checkResult
        html = this.item.title.replace(blankReg, (match) => {
          const html = this.renderWithAnswer(checkResult[index] || '', stuAnswer[index] || '', referAnswer[index] || '')
          index++
          return html
        })
      }
      return html
    },
    input () {
      const reportType = this.reportType
      if (!reportType) {
        const title = this.parentTitle
        const arr = Array.from(title.querySelectorAll('input.blank-input'))
        const res = new Array(arr.length).fill('')
        arr.forEach((it, index) => {
          res[index] = it.value.trim()
        })
        console.log(this.id,res)
        this.$emit('syncStuAnswer', this.id, res)
      }
    },
    renderWithAnswer (res, record, answer) {
      if (res) {
        return ` <span class="blank-check-right">${record}</span> `
      } else {
        return ` <span class="blank-check-error">${record || this.$t('noAnswer')}</span> <span class="blank-check-right">(${answer})</span> `
      }
    },
    handleClick(e) {
      e.stopPropagation();
      e.changeFun = debounce(this.input, 100)
      Bus.$emit('text-input-click', {
        e: e,
        parent: this.$parent
      })
    }
  },
  mounted () {
    const html = this.genTitle()
    this.$nextTick().then(() => {
      this.parentTitle.innerHTML = html
      if (!this.reportType) {
        this.parentTitle.style.lineHeight = '40px'
        const input = debounce(this.input, 100)
        this.parentTitle.addEventListener('input', input)
        if (this.examInfo.openSafeKeyboard || this.examInfo.enableCalculator) {
          const inputDoms = this.parentTitle.querySelectorAll('input.blank-input')
          inputDoms.forEach(it => {
            it.addEventListener('focus', this.handleClick)
            it.addEventListener('click', this.handleClick)
            it.addEventListener('touchstart', (e) => {
              e.stopPropagation()
            })
            it.addEventListener('touchend', (e) => {
              Bus.$emit('text-input-scroll')
            })
            it.addEventListener('scroll', () => {
              Bus.$emit('text-input-scroll')
            })
            it.addEventListener('blur', (e) => {
              Bus.$emit('text-input-item-blur', e)
            })
          })
        }
      }
      // this.parentTitle.addEventListener('paste', function() {
      //   return false
      // })
      // this.parentTitle.addEventListener('copy', function() {
      //   return false
      // })
    })
  }
}
</script>

<template>
  <div></div>
</template>

<style lang="stylus" module>
// .title
// line-height 40px
:global(.blank-input)
  border 1px solid $color-border-default
  box-sizing border-box
  border-radius 4px
  min-width 60px
  height 100%
  background-color transparent
  outline none
  vertical-align middle
  height 36px
  line-height 1.5em
  padding 6px 10px
  background-color #fff

// 渲染记录
:global(.blank-echo)
  display inline-block
  height 40px
  min-width 60px
  line-height 40px
  border 1px solid $color-border-default
  background-color #fff
  vertical-align middle
  word-wrap break-word
  padding 0 0.5em

// right error
:global(.blank-check-right)
  color $color-right

:global(.blank-check-error)
  color $color-error
  text-decoration line-through
  word-wrap break-word
</style>

<style lang="stylus">
.blank-echo-new
  display: inline !important;
  border: none !important;
  text-decoration: underline;
  height: auto !important;
  vertical-align: initial !important;
</style>
