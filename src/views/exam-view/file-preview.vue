<template>
  <Page :title="file.title">
    <iframe v-if="type=='document'" :src="getOfficeUrl()" class="iframe-detail-content"></iframe>
    <div class="file-wrapper">
      <i :class="['iconfont',fileIcon(file.mimeType)]"></i>
      <div class="title">{{file.title}}</div>
    </div>
  </Page>
</template>
<script>
import { Page } from '@/common/components'
import { fileIcon, getMediaType, formatSize } from '@/components/attach-file/file'
export default {
  components: {
    Page
  },
  data () {
    return {

    }
  },
  methods: {
    getMediaType,
    formatSize,
    fileIcon,
    getOfficeUrl () {
      let src = "https://docs.ulearning.cn/?i=16664";
      let location = this.file.location
      if (location.indexOf("https://") != -1) {
        src += "&ssl=1";
      }
      src += "&furl=" + location;
      return src
    }
  },
  computed:{
    file () {
      return this.$route.params
    },
    type () {
      var type = getMediaType(this.file.mimeType)
      if (type == 'doc' || type == 'pdf' || type == 'txt' || type == 'ppt' || type == 'xls'){
        return 'document'
      }
      return 'other'
    }
  },
  mounted () {
  }
}
</script>



<style lang="stylus" scoped>
iframe
  width: 100%;
  height: 100%;
  overflow: auto;
.file-wrapper
  padding: 100px 32px;
  text-align: center;
  i{
    font-size 40px
  }
  .title
    margin-top 20px
</style>
