<template>
  <div :class="$style.container" v-if="order > 0" @click="input">
    <i class="iconfont icon-datiqia" :class="$style.iconLeft"></i>
    <span>{{order}}</span>/<span>{{total}}</span>
    <i class="iconfont icon-arrowdown03" :class="{[$style.iconRight]: true, [$style.collapse]: value}"></i>
  </div>
</template>
<script>
export default {
  props: {
    total: {
      type: Number,
      default: 0
    },
    order: {
      type: Number,
      default: 0
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    input () {
      this.$emit('input', !this.value)
    }
  }
}
</script>
<style lang="stylus" module>
  .container
    height 30px
    padding 0 8px
    font-size $font-size-medium
    line-height 30px
    border-radius 999px
    background url('@/assets/images/tinum_bj.png') no-repeat
    color #fff
    .iconLeft
      margin-right 5px
      font-size  14px
    .iconRight
      margin-left 5px
      font-size $font-size-small
      vertical-align top
    /.collapse
      display inline-block
      transform rotate(180deg)
    i 
      color #fff
</style>
