<template>
  <div
    class="screen-page"
    :class="platform"
  >
    <p>{{$t('inExam')}}</p>
    <p>{{$t('forbidScreenRecord')}}</p>
    <p class="small">{{$t('screenRecordTips2')}}</p>
  </div>
</template>
<script>
import { getPlatform } from '@/common/utils'

export default {
  data() {
    return {
      platform: getPlatform().platform
    }
  }
}
</script>
<style lang="stylus">
.screen-page {
  position: fixed;
  top: 65px;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 100vh;
  min-width: 100vw;
  z-index: 999999;
  padding-top: 200px;
  background-color: #000;
  &.ios {
    top: calc(constant(safe-area-inset-top) + 45px); //为导航栏+状态栏的高度 88px
    top: calc(env(safe-area-inset-top) + 45px); //为导航栏+状态栏的高度 88px
  }
  p {
    margin: 0 auto 15px;
    max-width: 230px;
    font-size: 20px;
    text-align: center;
    color: #ffffff;
    line-height: 1.5;
    word-break: break-word;
    &.small {
      font-size: 14px;
    }
  }
}
</style>