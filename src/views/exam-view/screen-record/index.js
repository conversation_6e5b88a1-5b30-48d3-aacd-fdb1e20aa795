

import Vue from "vue"
import i18n from '@/i18n.js'
import ScreenRecord from "./index.vue";

const ScreenRecordConstructor = Vue.extend(ScreenRecord);

let instance;

const showScreenRecord = () => {
  if (!instance) {
    instance = new ScreenRecordConstructor({
      el: document.createElement('div'),
      i18n: i18n
    })
  }
  document.body.appendChild(instance.$el);
}
const hideScreenRecord = () => {
  if (instance) {
    document.body.removeChild(instance.$el);
    instance = null
  }
}

export default function changeScreenRecordStatus(status) {
  status ? showScreenRecord() : hideScreenRecord()
}