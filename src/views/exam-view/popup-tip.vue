<template>
  <Popup type="teacher-submit-popup" ref="submitPopup" :positon="'center'">
    <div :class="$style.submitPopup">
      <div :class="$style.title">{{ title }}</div>
      <div :class="$style.content">
        <slot></slot>
      </div>
      <div :class="$style.footer">
        <button type="button" class="button" :disabled="disabled" @click="close">
          {{ btnText }} <span v-if="timerSeconds >= 0">({{timerSeconds}}s)</span>
        </button>
      </div>
    </div>
  </Popup>
</template>
<script>
import { Popup } from 'cube-ui'
export default {
  components: {
    Popup
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    btnText: {
      type: String,
      default: ''
    },
    seconds: {
      type: Number,
      default: 3
    },
    allDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      timerSeconds: this.seconds,
      disabled: true
    }
  },
  methods: {
    show() {
      this.timerSeconds = this.seconds
      this.$refs.submitPopup.show()
      const timer = setInterval(() => {
        if (this.timerSeconds <= 0) {
          if (this.$listeners.end) {
            this.close()
            this.$listeners.end()
          }
          this.disabled = this.allDisabled
          clearInterval(timer)
        }
        this.timerSeconds--
      }, 1000)
      this.$once('hook:beforeDestroy', () => {
        clearInterval(timer)
      })
    },
    close() {
      this.$refs.submitPopup.hide()
    }
  },
}
</script>
<style lang="stylus" module>
.submitPopup
  margin 0 30px 
  padding-bottom 30px
  background-color #fff
  border-radius 4px
  text-align center
  .title 
    padding 15px 0
    border-bottom 1px solid $color-border-default
  .content 
    padding 20px 15px
    font-size 14px
    line-height 1.5
    text-align left
  .footer
    padding 20px 15px 0
    button
      width 100%
      height auto
      min-height 40px
      padding 5px 20px
      line-height 1.3
      border-radius 20px
</style>