<template>
  <div class="screen-code">
    <div class="input-tips">
      <span> {{ $t('enterCode') }}</span>
      <i class="cubeic-close" @click="close"></i>
    </div>
    <div class="code-tips">{{ $t('enterCodeTip') }}</div>
    <form name="code" ref="form" class="input-group">
      <input v-for="(item, index) in [1, 2, 3, 4]" :key="index" type="text" pattern="\d*" @keyup="codeKeyup"
        @keydown="codeKeydown" :data-index="index" required min="0" max="9" minlength="1" maxlength="1"
        onpaste="return false" />
      <div v-if="codeWarning.show" class="warning">
        {{ codeWarning.text }}
      </div>
    </form>
    <UButton class="btn" :primary="true" @click="submit">{{ $t('confirm') }}</UButton>
  </div>
</template>
<script>
import { signInByCode } from '@/api'
import { Button } from "@/common/components";
export default {
  components: {
    UButton: Button
  },
  data() {
    return {
      codeWarning: {
        show: false,
        text: this.$t('codeWrong'),
      },
    }
  },
  mounted() {
    setTimeout(() => {
      this.$refs.form.querySelectorAll('input')[0].focus()
    }, 300)
  },
  methods: {
    codeKeyup(event) {
      if (event.keyCode !== 8) {
        if (event.target.dataset.index === '3') {
          // this.signin()
        } else {
          event.target.nextElementSibling.focus()
        }
      }
      this.codeWarning.show = false
      if (event.target.value.length > 0) {
        event.target.value = event.target.value.substring(
          event.target.value.length - 1
        )
      }
    },
    codeKeydown(event) {
      if (event.keyCode === 8) {
        // eslint-disable-next-line
        if (event.target.value == '') {
          event.target.previousElementSibling.value = ''
          event.target.previousElementSibling.focus()
        }
      }
    },
    signin() {
      let inputs = this.$refs.form.querySelectorAll('input')
      let code = ''
      Array.prototype.forEach.call(inputs, (input) => {
        code += input.value
      })
      let params = {
        verificationCode: code,
        examId: this.$route.params.examID
      }
      const toast = this.$createToast({
        time: 0,
        txt: this.$t('confirming'),
      })
      toast.show()
      signInByCode(params)
        .then(({ data }) => {
          toast.hide()
          if (data.code === 1) {
            this.close()
            this.closeException()
          } else {
            this.codeWarning.text = data.message || this.$t('codeWrong')
            this.codeWarning.show = true
            this.$saveLog(
              `屏幕监控验证码失败_${JSON.stringify(params)}_${JSON.stringify(
                data
              )}`
            )
          }
        })
        .catch((error) => {
          toast.hide()
          this.codeWarning.text = this.$t('codeWrong')
          this.codeWarning.show = true
          this.$saveLog(
            `屏幕监控验证码失败_${JSON.stringify(params)}_${JSON.stringify(
              error
            )}`
          )
        })
    },
    close() {
      // 清除inputs值
      let inputs = this.$refs.form.querySelectorAll('input')
      Array.prototype.forEach.call(inputs, (input) => {
        input.value = ''
      })
      this.$emit('closeScreenCode')
    },
    closeException() {
      this.$emit('closeException')
    },
    submit() {
      this.signin()
    }
  },
  destroyed() {
    // // 清除缓存中的签到信息
    // this.$store.commit('saveSignInParams', {})
  },
}
</script>
<style lang="stylus">
.screen-code {
  font-size: 14px;
  color: #444;
  background-color: #fff;
    height: 320px;
    padding: 16px 16px 32px;
    overflow: auto;
    text-align:left;
    .input-tips {
      color: #444;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      display: flex;
      align-item: center;
      .cubeic-close {
        margin-left: auto;
        font-size: 20px;
      }
    }
    .code-tips {
      margin-top: 16px;
      font-size: 15px;
      line-height: 20px;
      color: #444;
    }
    .input-group {
      width: 220px;
      margin: 36px auto 0;
      input {
        font-size: 32px;
        line-height: 40px;
        font-weight: bold;
        border-bottom: solid 2px #444;
        width: 35px;
        padding:12px 0;
        margin: 0 10px;
        outline: none;
        text-align: center;
        background-color: transparent;
        color: #444;
        border-radius: 0;
      }
    }
    .warning {
      position: absolute;
      padding: 0 10px;
      margin-top: 15px;
      color: #f60000;
      line-height: 20px;
      text-align: left;
    }
    .btn {
      margin-top: 60px;
    }
  }
</style>