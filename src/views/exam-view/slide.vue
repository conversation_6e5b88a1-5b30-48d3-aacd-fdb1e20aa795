<template>
  <div ref="wrapper" :class="$style.wrapper" @scroll="scroll">
    <div class="swiper-container">
      <div class="swiper-wrapper" :class="moveFlag ? '' : 'stop-swiping'">
        <!-- 必须在每个slide上设置left -->
        <!-- 勿直接将v-for的index做为key，会出现key重复的问题 -->
        <div
          class="swiper-slide"
          v-for="question in virtualData.slides"
          :key="`${question.paperpartid}||${question.questionid}`"
          :style="{
            left:
              $t('lang') === 'ar'
                ? `-${virtualData.offset}px`
                : `${virtualData.offset}px`,
          }"
          :data-order="question.order"
        >
          <Head
            v-if="question.dataType === 'part'"
            :data="question"
            :index="question.outerIndex"
          />
          <Type
            ref="questionType"
            v-if="question.dataType === 'question'"
            :item="question"
            :examInfo="examInfo"
            :waterMarkImg="waterMarkImg"
            :partname="question.partname"
            :reportType="reportType"
            :isParent="true"
            @scroll.native.passive="scroll"
            @onScroll="scroll"
            @syncStuAnswer="syncStuAnswer"
            @syncAudioCount="syncAudioCount"
            @canSwiperSlide="canSwiperSlide"
            @renderFormula="renderFormula"
          />
          <QuestionOrder
            v-if="question.dataType === 'answerCard'"
            ref="slideNum"
            :data="data"
            :class="$style.slideNum"
            :isPopup="false"
            @gotoPage="gotoPage"
            @manualSubmitStuAnswer="manualSubmitStuAnswer"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Type, { Head } from './question-type'
// import 'swiper/dist/css/swiper.css'
// import { swiper, swiperSlide } from 'vue-awesome-swiper'
import QuestionOrder from './question-order'
import Bus from '@/utils/bus'
// import load from '@/utils/load'
import { findLastIndex, isFunction, cloneDeep } from 'lodash'
import { mapState } from 'vuex'
import { getPlatform } from '@/common/utils'

import Swiper from 'swiper/dist/js/swiper.esm.bundle' // 导入swiper
import 'swiper/dist/css/swiper.min.css' // 记得导入样式

const platform = getPlatform()
// let subIndex
let flag = false
export default {
  components: {
    Head,
    Type,
    QuestionOrder,
    // Swiper: swiper,
    // SwiperSlide: swiperSlide
  },
  props: {
    data: {
      default() {
        return []
      },
    },
    paperLength: {
      type: Number,
      default: 0,
    },
    // records: {},
    reportType: {},
    examInfo: {},
    waterMarkImg: '',
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length != 0) {
          this.initData(newVal)
          this.$nextTick(() => {
            if (Object.keys(this.swiper).length == 0) {
              this.initSwiper()
              this.swiper.$el[0].style.minHeight = this.height
            }
          })
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      options: {
        initialSlide: 0,
        direction: 'horizontal',
        loop: false,
        autoplay: false,
        preventClicks: true,
        preventLinksPropagation: true,
        noSwipingClass: 'mejs__container',
        autoHeight: true,
        isMoveEnd: false,
        toastMsg: false,
      },
      staticData: [],
      // 只渲染的三条slide
      virtualData: {
        slides: [],
      },
      loading: true,
      // swiper实例对象
      swiper: {},
      // 当前部分题目的索引
      currentQuestionIndex: 0,
      height: '',
      // swiper能否滑动
      moveFlag: true,
    }
  },
  computed: {
    ...mapState(['map']),
  },
  mounted() {
    this.height = document.querySelector('.uContentWrap').clientHeight + 'px'
    // this.swiper.$el.style.minHeight = height
    // this.$refs.slideNum.$el.style.height = height
  },
  methods: {
    // 初始化部分、题目、答题卡数据
    initData(newVal) {
      this.staticData = []
      let order = 1
      for (let i = 0; i < newVal.length; i++) {
        this.staticData.push({
          ...newVal[i],
          outerIndex: i,
          dataType: 'part',
          order: order,
        })
        for (let j = 0; j < newVal[i].children.length; j++) {
          const data = newVal[i].children[j]
          this.staticData.push({
            ...data,
            dataType: 'question',
            order: order++,
            partname: newVal[i].partname,
          })
          if (data.questionlist) {
            order += data.questionlist.length - 1
          }
        }
      }
      this.staticData.push({ dataType: 'answerCard', data: newVal, order: 0 })
    },
    initSwiper() {
      let self = this
      self.swiper = new Swiper('.swiper-container', {
        initialSlide: 0,
        direction: 'horizontal',
        loop: false,
        autoplay: false,
        preventClicks: true,
        preventLinksPropagation: true,
        noSwipingClass: 'stop-swiping',
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // cache: true,
        on: {
          init() {
            self.loading = false
          },
          slideChange() {
            self.swiper = this
            self.handleSlideChange()
          },
          sliderMove: function () {
            self.sliderMove()
          },
        },
        virtual: {
          addSlidesBefore: 0,
          addSlidesAfter: 0,
          renderExternal: (data) => {
            self.virtualData = data
            this.renderFormula()
          },
          slides: self.staticData,
        },
      })
      window.swiper = self.swiper
    },
    // 手动更新swiperSlides
    updateVirtualSlide() {
      // 将原本的refRecord实例赋给新data
      this.swiper.virtual.slides.forEach((item, index) => {
       if (item.__recordRef) {
         this.staticData[index].__recordRef = item.__recordRef
       }
      })
      this.swiper.virtual.slides = this.staticData
      const { from, to } = this.virtualData
      this.virtualData = {
        ...this.virtualData,
        slides: this.staticData.slice(from, to + 1),
      }
    },
    // 获取参数item的element元素
    getElement(item) {
      if (!item) return
      let element = null
      const slideElements = document.querySelectorAll('.swiper-slide')
      slideElements.forEach((it) => {
        if (it.getAttribute('data-order') == item.order) {
          element = it
        }
      })
      return element
    },
    getItem(type) {
      let element = null
      const { activeIndex, previousIndex } = this.swiper
      switch (type) {
        case 'active':
          element = this.swiper.virtual.slides[activeIndex]
          break
        case 'previous':
          element = this.swiper.virtual.slides[previousIndex]
          break
        default:
          element = this.swiper.virtual.slides[activeIndex]
          break
      }
      return element
    },
    // 当卡片移动时的回调
    handleSlideChange() {
      const activeItem = this.getItem('active')
      this.currentQuestionIndex = activeItem.order
      const activeElement = this.getElement(activeItem)
      activeElement && activeElement.scrollTo(0, 0)
      this.isMoveEnd = true
      if (this.currentQuestionIndex > -1) {
        this.$emit('change', this.currentQuestionIndex)
      } else {
        // 微信公众号输入框失去焦点之后不自动滚到底部
        setTimeout(() => {
          window.scrollBy({
            top: 10,
            behavior: 'smooth',
          })
        }, 10)
      }
      // 填空题光标移除
      this._blur()
      // 暂停音视频播放
      this._stopMedia()
      this.slideChangeTransitionStart()
      // this.updateVirtualSlide()
    },
    /**
     * 滚动时的回调
     */
    scroll() {
      const _falg = flag
      flag = false
      const activeElement = this.getElement(this.getItem('active'))
      if (activeElement) {
        const order = parseInt(activeElement.getAttribute('data-order'))
        const subElements = activeElement.querySelectorAll('.composite-mark')
        const dragMark = activeElement.querySelector('.drag-mark')
        let height = activeElement.getBoundingClientRect().top
        if (dragMark) {
          height = dragMark.getBoundingClientRect().top
        }
        if (subElements && subElements[0]) {
          let subIndex = findLastIndex(subElements[0].children, (it) => {
            const top = it.getBoundingClientRect().top
            if (top <= height) {
              return true
            }
          })
          this.currentQuestionIndex = order + (subIndex < 0 ? 0 : subIndex)
          if (_falg === false) {
            this.$emit('change', this.currentQuestionIndex)
          }
        }
      }
      // 安卓部分机型切换滑块题目内容不显示，强制更新样式
      if (platform.isAndroid || platform.isHarmony) {
        activeElement.style.wordBreak = ''
        setTimeout(() => {
          activeElement.style.wordBreak = 'break-word'
        })
      }
    },
    // 手动切换卡片
    gotoPage(slideIndex, subIndex, question) {
      if (
        this._onSliderMove(
          () => {
            this._slideTo(slideIndex, subIndex, question)
          },
          slideIndex,
          subIndex,
          question
        )
      )
        return
      this._slideTo(slideIndex, subIndex, question)
    },
    // 跳转到卡片所点击的题
    _slideTo(slideIndex, subIndex, question) {
      this.swiper.slideTo(slideIndex)
      this.currentQuestionIndex = question.order + 1
      // 为了让slideTo结束后再滚动跳转到序号对应题目
      setTimeout(() => {
        const item = this.getItem('active')
        const activeElement = this.getElement(this.getItem('active'))
        if (activeElement) {
          const subElements = activeElement.querySelectorAll('.composite-mark')
          if (subElements && subElements.length != 0) {
            // flag 防止综述题选择题号时，触发scroll事件，导致题号错误问题
            flag = true
            const tempOrder =
              this.currentQuestionIndex -
              parseInt(activeElement.getAttribute('data-order'))
            let temp = subElements[0]
            if (temp && temp.children) {
              const sections = activeElement.querySelectorAll('section')
              if (item.type === 24) {
                // 是综述题
                let top = temp.children[tempOrder].offsetTop
                if (temp.parentElement.className) {
                  //综述题分屏跳转题目滚动
                  top = top - temp.offsetTop
                  temp.parentElement.scrollTo(0, top)
                } else if (sections) {
                  // 未分屏跳转题目滚动
                  sections[0].scrollTo({
                    top: top + 1,
                    preventScroll: true,
                  })
                }
              } else {
                // 下拉题文字较多时跳转滚动
                const top =
                  temp.querySelectorAll('.select-input')[subIndex].offsetTop
                sections[0].scrollTo({
                  top: top + 1,
                  preventScroll: true,
                })
              }
            }
            this.$emit('change', this.currentQuestionIndex)
          } else {
            flag = false
          }
        }
      })
    },
    /**
     * 保存用户作答至本地
     */
    syncStuAnswer(id, stuAnswer) {
      // nextTick -> setTimeout
      this.$emit('syncStuAnswer', id, stuAnswer)
      setTimeout(() => {
        this.updateVirtualSlide()
      })
    },
    // 保存音频次数
    syncAudioCount(id) {
      this.$emit('syncAudioCount', id)
      this.$nextTick(() => {
        this.updateVirtualSlide()
      })
    },
    // 排序题选择时，是否可以滑动
    canSwiperSlide(moveFlag) {
      this.moveFlag = moveFlag
    },
    /**
     * 更新slide高度
     */
    updateSlide() {
      this.swiper.update()
    },
    /**
     * 手动交卷
     */
    manualSubmitStuAnswer() {
      this.$emit('manualSubmitStuAnswer')
    },
    /**
     * 口语题录音前，需要弹窗确定
     */
    sliderMove(touchMove) {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this._onSliderMove()
      }, 0)
    },
    // 滑动切换开始时的处理函数
    slideChangeTransitionStart() {
      if (this.examInfo.sequentialAnswer && this.reportType != 2) {
        const element = this.getItem('active')
        if (
          element &&
          element.stat == 'blank' &&
          this.swiper.touches.diff < 0
        ) {
          this.swiper.allowSlideNext = false
        }
      }
    },
    /**
     * 滑动前的回调
     */
    _onSliderMove(callback, slideIndex, subIndex, question) {
      const swiper = this.swiper
      const recordRef = this._getRecordRef()
      const moveDirection = swiper.touches.diff
      const stat = this._getQuestat()
      let move = true
      if (this.isMoveEnd) {
        move = false
        this.isMoveEnd = false
      }
      if (recordRef && recordRef.isRecording) {
        swiper.allowSlideNext = false
        swiper.allowSlidePrev = false
        this.$createDialog({
          type: 'confirm',
          content: this.$t('recordTip1'),
          maskClosable: true,
          confirmBtn: { text: this.$t('confirm'), active: true },
          cancelBtn: { text: this.$t('cancel') },
          onConfirm: () => {
            recordRef.stopRecordFn(() => {
              swiper.allowSlideNext = true
              swiper.allowSlidePrev = true
              if (isFunction(callback)) {
                if (this.examInfo.sequentialAnswer) {
                  this._skipGotopage(question)
                } else {
                  callback()
                }
              } else {
                const diff = swiper.touches.diff
                if (diff >= 0) {
                  swiper.slidePrev()
                } else {
                  swiper.slideNext()
                }
              }
            })
          },
        }).show()
        return true
      } else if (
        stat &&
        stat == 'blank' &&
        this.examInfo.sequentialAnswer &&
        !question &&
        this.reportType != 2
      ) {
        // true为判断考试是否开启禁止跳题的条件this.examInfo
        if (move && moveDirection < 0) {
          swiper.allowSlideNext = false
          this._skipDialogShow()
        }
        return false
      } else if (
        question &&
        this.examInfo.sequentialAnswer &&
        this.reportType != 2
      ) {
        let tempItem = this.getItem('active')
        if (tempItem && tempItem.type != 24) {
          if ([17, 23, 25].includes(tempItem.type)) {
            if (tempItem.questionlist.indexOf(question) != -1) {
              return false
            }
          }
          return this._skipGotopage(question)
        } else if (tempItem && tempItem.type == 24) {
          if (tempItem.questionlist.indexOf(question) != -1) {
            return false
          } else {
            return this._skipGotopage(question)
          }
        } else if (!tempItem) {
          return this._skipGotopage(question)
        }
      } else {
        swiper.allowSlideNext = true
        swiper.allowSlidePrev = true
        return false
      }
    },
    /**
     * 获取录音实例
     */
    _getRecordRef() {
      const item = this.getItem('active')
      if (item) {
        return item.__recordRef || null
      }
      return null
    },
    _getQuestat() {
      const item = this.getItem('active')
      if (item && item.type == 24 && item.questionlist) {
        let isBlank = 'answer'
        item.questionlist.forEach((que) => {
          if (que.stat == 'blank') {
            isBlank = 'blank'
          }
        })
        return isBlank
      } else if (item && item.type != 24) {
        return item.stat || null
      }
      return null
    },
    _stopMedia() {
      const previousElement = this.getElement(this.getItem('previous'))
      if (previousElement) {
        const previous = previousElement.querySelectorAll('audio, video')
        Array.from(previous).forEach((it) => it.pause())
      }
    },
    _skipDialogShow() {
      if (this.toastMsg) {
        return
      }
      this.toastMsg = true
      const toast = this.$createToast({
        txt: this.$t('noSkippingMsg'),
        type: 'warn',
        time: 1000,
        onTimeout: () => {
          this.toastMsg = false
        },
      })
      toast.show()
    },
    _skipGotopage(question) {
      let tempItem = this.getItem('active')
      // true为判断考试是否开启禁止跳题的条件this.examInfo
      if (question.stat == 'answer' && this.examInfo.sequentialAnswer) {
        this.swiper.allowSlideNext = true
        this.swiper.allowSlidePrev = true
        return false
      } else if (
        question.stat == 'blank' &&
        this.examInfo.sequentialAnswer &&
        tempItem &&
        question.order != tempItem.order
      ) {
        this._skipDialogShow()
        return true
      } else if (
        question.stat == 'blank' &&
        this.examInfo.sequentialAnswer
      ) {
        this._skipDialogShow()
        return true
      }
    },
    /**
     * 输入框光标移除
     */
    _blur() {
      const previousElement = this.getElement(this.getItem('previous'))
      if (previousElement) {
        const inputElement = Array.from(
          previousElement.querySelectorAll('input, textarea')
        )
        inputElement.forEach((it) => {
          it.blur()
        })
        Bus.$emit('text-input-blur')
      }
    },
    renderFormula() {
      this.$emit('renderFormula')
    }
  },
  destroyed() {
    this.map.clear()
  },
}
</script>
<style lang="stylus" module>
.wrapper
  // width 100vw
  width 100%
  height 100%
  overflow-y auto

.wrapper :global(.swiper-container)
  width 100%
  height 100%
  // overflow-y auto

.wrapper :global(.swiper-slide)
  position: relative
  // overflow-y auto
  // -webkit-overflow-scrolling touch

.slideNum button
  color #fff
  background-color $color-main
  border-color $color-main

.slideNum > div
  flex 1 1
</style>
