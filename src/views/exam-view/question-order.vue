<script>
import { complexTypes } from '@/utils/Paper'
import { flatMap } from 'lodash'
import { mapState } from 'vuex'
const Order = {
  name: 'order',
  props: ['order', 'stat'],
  computed: {
    checkedStyle () {
      const { stat } = this
      const { $style } = this.$parent
      if (stat === 'answer') {
        return $style.isanswer
      } else {
        if (stat === 'right') return $style.checkRight
        else if (stat === 'error' || stat === 'forgot') return $style.checkError
      }
      return ''
    }
  },
  render (h) {
    const { order, checkedStyle } = this
    const { $style } = this.$parent
    return (
      <div class={$style.circle}>
        <span class={[$style.item, checkedStyle]} data-role="order">{order + 1}</span>
      </div>
    )
  }
}

export default {
  props: ['data'],
  data () {
    return { complexTypes }
  },
  computed: {
    ...mapState(['headerWarn']),
    name () {
      return this.$route.name
    }
  },
  watch: {
    headerWarn(val) {
      if (val) {
        this.$refs.editPadding.style.paddingBottom = '45px'
      } else {
        this.$refs.editPadding.style.paddingBottom = 0
      }
    }
  },
  methods: {
    // 手动切换卡片
    gotoPage (slideIndex, subIndex, question) {
      this.$emit('gotoPage', slideIndex, subIndex, question)
    },
    // 提交考试
    submit () {
      this.$emit('manualSubmitStuAnswer')
    }
  },
  render (h) {
    const { $style, data, name } = this
    let slideIndex = 0
    if(!data) return 
    return (
      <div class={$style.wrap} ref="editPadding">
        <div class={$style.container}>
          {
            data.map((it, partIndex) => {
              const { paperpartid, partname, children } = it
              return (
                <dl key={paperpartid} class={$style.dl}>
                  <dt class={$style.dt}>{partname}</dt>
                  <dd class={$style.dd}>
                    {
                      flatMap(children, it => {
                        slideIndex++
                        if (Array.isArray(it.questionlist)) {
                          return it.questionlist.map((it, subIndex) => {
                            const index = slideIndex + partIndex
                            return <Order order={it.order} key={it.questionid} stat={it.stat} nativeOnClick={() => { this.gotoPage(index, subIndex, it) } }/>
                          })
                        } else {
                          const index = slideIndex + partIndex
                          return <Order order={it.order} key={it.questionid} stat={it.stat} nativeOnClick={() => { this.gotoPage(index, null, it) }}/>
                        }
                      })
                    }
                  </dd>
                </dl>
              )
            })
          }
        </div>
        { name === 'exam' && <button onClick={this.submit} class={$style.submit} data-role="order">{this.$t('submitExam')}</button> }
      </div>
    )
  }
}
</script>

<style lang="stylus" module>
.wrap
  white-space normal
  display flex
  flex-direction column
  height 100%

  /.container
    overflow-y auto
    background-color #fff
    // flex 1 1
    // -webkit-overflow-scrolling touch

  /.dl
    padding 20px 20px 60px
    background-color #fff

  /.dt
    margin-bottom 20px

  /.dd
    margin-bottom -5px

    /.circle
      display inline-block
      width 20%
      text-align center
      margin-bottom 5px

    /.item
      display inline-block
      width 44px
      height 44px
      margin-right 10px
      line-height 44px
      text-align center
      font-size $font-size-medium
      border 1px solid $color-border-default
      border-radius 50%
      cursor pointer
      color $text-color-default

    /.itemActive
      border-color $color-main

      &:last-child
        margin-right 0

      /.isanswer
        border-color $color-main
        color $color-main

      /.checkError
        background-color #ffeeee
        color #ff0000
        border-color #ff0000

      /.checkRight
        background-color #efffe8
        color #41c80a
        border-color #41c80a

      /.checkRightSp
        border-color $color-main
        color $color-main
        background-color #fff

      /.checkErrorSp
        border 1px solid $color-border-default
        color $text-color-default
        background-color #fff

.submit
  flex 0 0 50px
  width 100%
  font-size 16px
  line-height 50px
  color $color-main
  border none
  border-top 1px solid #e3e3e9
  text-align center
  background-color #fff
  cursor pointer
  pointer-events auto
  outline none
</style>
