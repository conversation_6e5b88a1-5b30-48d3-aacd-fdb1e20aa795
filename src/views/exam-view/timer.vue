<script>
import { formatTimer } from '@/common/utils'
export default {
  props: {
    value: {
      validator (newVal) {
        return Number.isInteger(newVal)
      }
    },
    alertSecond: {
      validator (newVal) {
        return Number.isInteger(newVal)
      },
      default: Infinity
    },
    // 间隔
    interval: {
      validator (newVal) {
        return Number.isInteger(newVal)
      },
      default: 1
    },
    // 展示格式
    format: {
      type: String
    },
    // 结束时间
    endTime: {
      validator (newVal) {
        return Number.isInteger(newVal)
      },
      default: 0
    }
  },
  computed: {
    alert () {
      return this.alertSecond >= this.value
    }
  },
  methods: {
    formatTimer (seconds) {
      return formatTimer(seconds, this.format)
    }
  },
  mounted () {
    console.log(this.value + '-------')
    var oldTime = new Date()
    let timer = setInterval(() => {
      if (this.value <= this.endTime) {
        clearInterval(timer)
        return
      }
      var passTime = parseInt((new Date() - oldTime) / 1000)
      passTime = passTime < 1 ? 1 : passTime
      oldTime = new Date()
      this.$emit('input', this.value - passTime)
    }, this.interval * 1000)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer)
    })
  }
}
</script>
<template>
  <div :class="{[$style.timer]: true, [$style.alert]: alert}">
    <i class="iconfont icon-shichang1"></i>
    {{formatTimer(value)}}
  </div>
</template>
<style lang="stylus" module>
.timer
  height 30px
  padding 0 8px
  font-size $font-size-medium
  line-height 28px
  color $color-intro
  border 1px solid $color-border-default
  border-radius 20px

  i
    margin-right 5px
    font-size 17px

.alert
  color #f60000
  i
    color #f60000
</style>
