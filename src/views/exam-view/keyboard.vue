<template>
  <Keyboard :show="show" :canEnter="canEnter" @blur="show = false" @update="update" @delete="deleteInput" @positionBack="positionBack"></Keyboard>
</template>
<script>
import Keyboard from "@/components/Keyboard"
import Bus from '@/utils/bus'
import { checkUpdateAnswerLength } from '@/utils/utils'
export default {
  name: "KeyboardInput",
  components: {
    Keyboard
  },
  data() {
    return {
      show: false,
      canEnter: false,
      value: "",
      content: "",
      selectionStart: 0,
      selectionEnd: 0,
      selectionDirection: 'forward',
      currentDom: null,
      changeFun: null,
      // slideRef
      slideRef: {},
      // 当前题目
      questionItem: {},
      // 当前题目dom
      questionElement: null,
      // 当前需要滚动的section
      curSectionElement: null,
      // 是否分屏
      columnMode: false,
    };
  },
  watch: {
    value: {
      handler() {
        this.$nextTick(() => {
          this.showFocusCursor()
        })
        this.handleScroll()
        if (this.currentDom) {
          this.currentDom.value = this.value;
          this.changeFun && this.changeFun()
        }
      },
      immediate: true
    },
    show: {
      handler() {
        this.$nextTick(() => {
          if (this.currentDom && this.currentDom.nextElementSibling && this.currentDom.nextElementSibling.style) {
            this.currentDom.nextElementSibling.style.display = this.show ? 'block' : 'none'
          }
          if (!this.show) {
            this.currentDom && (this.currentDom.scrollLeft = 0)
            this.currentDom = null
            this.changeFun = null
            this.value = ""
            if (this.curSectionElement) {
              const wrapper = this.curSectionElement
              wrapper && (wrapper.style.paddingBottom = "0px")
            }
          }
        })
      },
      immediate: true
    }
  },
  created() {
    document.addEventListener('select', this.handleSelect)
    // document.addEventListener('selectionchange', this.handleSelect)
  },
  mounted() {
    Bus.$on('text-input-click', (data) => {
      this.slideRef = this.$parent.$parent.$refs.slide
      this.questionItem = this.slideRef.getItem('active')
      this.questionElement = this.slideRef.getElement(this.questionItem)
      // 需要滚动的section（根据是否分屏来决定滚哪一个）
      this.slideRef.$refs.questionType.forEach(res=>{
        if(res.item.questionid==this.questionItem.questionid){
          this.columnMode = res.columnMode
        }
      })
      this.curSectionElement = this.columnMode ? this.questionElement.querySelector('.composite-mark').parentElement : this.questionElement.querySelector('section')
      
      const e = data.e
      if (e.target !== this.currentDom) {
        this.show = false
        if (this.currentDom && this.currentDom.nextElementSibling && this.currentDom.nextElementSibling.style) {
          this.currentDom.nextElementSibling.style.display = 'none'
          this.currentDom.scrollLeft = 0
        }
      }
      this.$nextTick(() => {
        this.handleClick(e)
        this.changeFun = e.changeFun
        // 滚动到输入框位置
        try {
          const isNotView = e.target.getBoundingClientRect().top + 320 >= document.querySelector(".uContentWrap").clientHeight
          if (isNotView) {
            this.curSectionElement.style.paddingBottom = "320px"
            setTimeout(() => {
              const parentTop = e.target.tagName.toLowerCase() === 'input' ? data.parent.$el.offsetTop : data.parent.$refs.question.$el.offsetTop
              let top = parentTop + e.target.parentElement.offsetTop - 10
              const wrapper = this.curSectionElement
              if(this.columnMode){
                top -= this.questionElement.querySelector('.composite-mark').offsetTop
              }
              wrapper.scrollTo({
                top: top,
                behavior: 'smooth'
              })
            }, 100)
          }
          setTimeout(() => {
            e.target.blur()
          }, 200)
        } catch(e) {
          console.log(e)
        }
      })
    })
    Bus.$on('text-input-scroll', () => {
      this.handleScroll()
    })
    Bus.$on('text-input-blur', () => {
      this.show = false
    })
    Bus.$on('text-input-item-blur', (e) => {
      if (this.show && e.target === this.currentDom) {
        setTimeout(() => {
          this.inputScroll()
        })
      }
    })
  },
  methods: {
    update(value) {
      // 判断questionElement dom类型是不是textarea类型 是的的为简答题 要限制连续多个字符
      if (this.currentDom.tagName.toLowerCase() === 'textarea') {
        if(checkUpdateAnswerLength(value)) {
          value = ''
        }
      }
      this.value = this.value.slice(0, this.selectionStart) + value + this.value.slice(this.selectionEnd)
      this.selectionStart = this.selectionStart + value.length
      this.selectionEnd = this.selectionStart
    },
    deleteInput() {
      if (this.selectionEnd > 0) {
        const range = this.selectionEnd - this.selectionStart > 0 ? 0 : 1
        this.value = this.value.slice(0, this.selectionStart - range) + this.value.slice(this.selectionEnd)
        this.selectionStart = this.selectionStart - range
        this.selectionEnd = this.selectionStart
      }
    },
    handleClick(e) {
      this.getPosition(e)
      this.showFocusCursor()
    },
    handleSelect(e) {
      this.getPosition(e)
      this.selectionDirection = e.target.selectionDirection || 'forward'
    },
    getPosition(e) {
      const tagName = e.target.tagName ? e.target.tagName.toLowerCase() : ''
      if (['textarea', 'input'].indexOf(tagName) === -1) {
        return false
      }
      e && e.preventDefault();
      this.currentDom = e.target
      this.value = e.target.value || ""
      this.selectionStart = e.target.selectionStart
      this.selectionEnd = e.target.selectionEnd
      this.show = true
      this.canEnter = tagName === 'textarea'
    },
    handleScroll () {
      if (this.currentDom && this.currentDom.nextElementSibling) {
        this.$nextTick(() => {
          this.currentDom.nextElementSibling.scrollTop = this.currentDom.scrollTop
          this.currentDom.nextElementSibling.scrollLeft = this.currentDom.scrollLeft
        })
      }
    },
    showFocusCursor() {
      const handleInput = (input) => {
        return input.replace(/(\r|\n)/g, '<br>').replace(/\s/g, '&nbsp;')
      }
      const cursorTemplate = this.value ? '<span class="focus-cursor"></span>' : '<span class="focus-cursor">|</span>'
      const index = this.selectionDirection === 'backward' ? this.selectionStart : this.selectionEnd
      this.content = handleInput(this.value.slice(0, index)) + cursorTemplate + handleInput(this.value.slice(index))
      
      if (this.currentDom && this.currentDom.nextElementSibling) {
        this.currentDom.nextElementSibling.innerHTML = this.content
        this.inputScroll()
      }
    },
    inputScroll() {
      try {
        this.$nextTick(() => {
          if (this.currentDom && this.currentDom.nextElementSibling) {
            const tagName = this.currentDom.tagName ? this.currentDom.tagName.toLowerCase() : ''
            const left = this.currentDom.nextElementSibling.querySelector(".focus-cursor").offsetLeft
            if (tagName === 'input' && this.currentDom.clientWidth <= left) {
              this.currentDom.scrollLeft = left - 20
              this.currentDom.nextElementSibling.scrollLeft = this.currentDom.scrollLeft
            }
          }
        })
      } catch(e) {
        console.log(e)
      }
    },
    positionBack() {
      this.$nextTick(() => {
        this.selectionStart = this.selectionStart - 1
        this.selectionEnd = this.selectionStart
      })
    }
  },
  beforeDestroy() {
    Bus.$off('text-input-click')
    Bus.$off('text-input-scroll')
    Bus.$off('text-input-blur')
    Bus.$off('text-input-item-blur')
  },
};
</script>
<style lang="stylus">
.text-input-wrap {
  position: relative;
  width: 100%;
  height: 150px;
  .textarea {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 8px;
    line-height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: $text-color-default;
    font-size: 16px;
    font-family: $font-family;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
    border: none;
    outline: none;
    resize: none;
    border-radius: 4px;
    letter-spacing: 1px;
    overflow: auto;
    &::placeholder {
      color: #4567429;
    }
  }
  textarea.textarea {
    z-index: 2;
    background-color: transparent;
  }
  .textarea-follow {
    display: none;
    z-index: 1;
    color: transparent;
    background-color: #f1f3f7;
  }
  &.blank-input-wrap {
    display: inline-block;
    margin-left: 5px;
    width: 220px;
    height: 36px;
    line-height: 1.2;
    vertical-align: middle;
    .blank-input {
      position: absolute;
      width: 100%;
      font-family: $font-family;
      word-break: normal;
    }
    input.blank-input {
      z-index: 2;
      background-color: transparent;
    }
    .blank-input-follow {
      display: none;
      z-index: 1;
      color: transparent;
      background-color: #ffffff;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  // 只有火狐浏览器支持
  input, textarea {
    ime-mode: disabled !important;
  }
  @-webkit-keyframes cursor-flicker {  
    from { 
      opacity: 0; 
    }  
    50% {    
      opacity: 1;  
    }  
    100% {    
      opacity: 0;  
    }
  }
  @keyframes cursor-flicker {  
    from {    
      opacity: 0;  
    }  
    50% {    
      opacity: 1;  
    }  
    100% {    
      opacity: 0;  
    }
  }
  .focus-cursor {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 22px;
      background-color: #000000;
      -webkit-animation: 1s cursor-flicker infinite;
      animation: 1s cursor-flicker infinite;
    }
  }
}
</style>