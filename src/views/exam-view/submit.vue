<script>
import { Popup } from 'cube-ui'
import { Button } from '@/common/components'
export default {
  props: {
    canLeave: {
      type: Boolean,
      default: true
    }
  },
  components: {
    Popup,
    UButton: Button
  },
  methods: {
    /**
     * 取消交卷
     */
    cancel () {
      this.$refs.tipPopup.hide()
      this.$emit('cancel')
    },
    /**
     * 确定交卷
     */
    confirm () {
      this.$refs.tipPopup.hide()
      this.$emit('confirm')
    },
    /**
     * 弹窗
     */
    show () {
      this.$refs.tipPopup.show()
    }
  }
}
</script>
<template>
  <Popup type="extend-tip-popup"
    ref="tipPopup"
    :maskClosable="true"
    :positon="'center'">
    <div :class="$style.backtip">
      <div :class="$style.backtipBg"></div>
      <p :class="$style.backtipTitle">
        <span v-if="canLeave">{{ $t('exitTip1') }}</span>
        <i18n path="exitTip2" v-else>
          <span :class="$style.red" slot="screen">{{ $t('screenMonitor') }}</span>
        </i18n>
      </p>
      <UButton :primary="true"
        :radius="true"
        @click="cancel">{{ $t('continueExam') }}</UButton>
      <UButton :radius="true" v-if="canLeave"
        @click="confirm">{{ $t('saveAndExit') }}</UButton>
    </div>
  </Popup>
</template>
<style lang="stylus" module>
.backtip
  padding-bottom 40px
  background-color #fff
  border-radius 16px
  overflow hidden
  margin 0 30px
  /.backtipBg
    padding-top 50%
    width 100vw
    background url('@/assets/images/tc_practice_time.png') no-repeat 0 0/cover
  /.backtipTitle
    text-align left
    padding 20px 30px 40px
    line-height 1.4
  :global(.uButton)
    min-width 190px
    width auto 
    padding 0 10px
    margin 0 auto
    padding 13px 16px
    margin-bottom 19px
    &:last-child
      margin-bottom 0
  .red
    color: #ea5947
</style>
