<template>
  <Page :class="$style.examPage" :show="true" @back="back">
    <template v-if="status === 'success'">
      <div slot="left-attach" v-if="examInfo.enableSnapshotMonitor && false" :class="$style.photoMonitor">
        <div v-if="!isSnapTime" :class="$style.watching">
          <img src="@/assets/images/camera_watching.png" />
          <span>{{ $t('monitoring') }}</span><!-- 监控中 -->
        </div>
        <div v-if="isSnapTime" :class="$style.snapping">
          <img src="@/assets/images/camera_timer.gif" />
          <i18n tag="span" path="photoCountDown"><!-- {count}秒后开始拍照 -->
            <strong slot="n">{{ isSnapTime }}</strong>
          </i18n>
        </div>
      </div>
      <SwitchBtn slot="title" :total="paperLength" :order="order" v-model="visible" />
      <Timer slot="menu" v-if="status && name === 'exam'" v-model="examInfo.examTime" :alertSecond="60" :interval="1"
        :format="'mm:ss'" />
      <div :class="$style.snapPhoto" v-if="snapPhoto">
        <img :src="snapPhoto + '?imageView2/0/format/jpg'" alt="">
      </div>
      <Pose v-if="examInfo.enableSnapshotMonitor" :examUserId="examInfo.examUserID" :userId="user.userID"
        :examId="examID" @saveBehavior="savePoseBehavior" />
      <div v-show="visible" :class="$style.popup">
        <div :class="$style.popupMask" @click="hide"></div>
        <div :class="$style.popupContainer">
          <QuestionOrder :class="$style.snPanel" :data="paper.part" @gotoPage="gotoPage"
            @manualSubmitStuAnswer="manualSubmitStuAnswer" @click.native="delegateHide" />
        </div>
      </div>
      <div :class="$style.headerWarn" ref="headerWarn">
        <i class="iconfont icon-jinggao"></i>
        <span>{{ $t('netWorkErrorNew') }}</span>
      </div>
      <Slide :data="paper.part" :examInfo="examInfo" :reportType="reportType" :waterMarkImg="waterMarkImg"
        @syncStuAnswer="syncStuAnswer" @syncAudioCount="syncAudioCount" @manualSubmitStuAnswer="manualSubmitStuAnswer"
        @change="syncOrder" @renderFormula="renderFormula" ref="slide"></Slide>
      <Submit :canLeave="!(totalCount > 0 || leaveLimitTime > 0)" @confirm="manualSaveStuAnswer" @cancel="cancelSubmit"
        ref="submit" />
      <Hint v-if="name === 'exam' && (totalCount > 0 || leaveLimitTime > 0)" :totalCount="totalCount"
        :leaveLimitTime="leaveLimitTime" :realLeftTime="leftTime" :isTimeOver="isTimeOver()" v-bind="getLeftInfo()"
        @confirm="submitStuAnswerWithCount" @confirmForLimitTime="confirmForLimitTime" ref="hint" />
    </template>
    <loading v-else-if="status === 'loading'"></loading>
    <Error v-else></Error>
    <Popup type="my-popup" :mask="true" :mask-closable="false" ref="myPopup1">
      <div :class="$style.myPopup1">
        <div :class="$style.title">
          {{ $t('noSkippingTips') }}
        </div>
        <div :class="$style.content">
          {{ $t('netWorkErrorMsg') }}
        </div>
        <button type="button" class="button button-black-hollow" @click="closeNetworkError">{{ $t('continueExam')
          }}</button>
        <button type="button" class="button button-black-hollow" @click="quiteExam">{{ $t('quiteExam') }}</button>
      </div>
    </Popup>
    <Popup type="my-popup" :mask="true" :mask-closable="false" ref="myPopup2">
      <div :class="$style.myPopup2">
        <div :class="$style.title">
          {{ $t('noSkippingTips') }}
          <i v-if="!isSystemSubmit" class="cubeic-close" :class="$style.iconClose" @click="closeSubmitError"></i>
        </div>
        <div :class="$style.content" v-html="$t('submitPaperErrorMsg')">
        </div>
        <button type="button" class="button button-black-hollow" @click="reSubmitPaper">{{ $t('reSubmit') }}</button>
        <button type="button" class="button button-black-hollow" @click="quiteExam">{{ $t('quiteExam') }}</button>
      </div>
    </Popup>
    <PopupTip ref="teacherPopupTip" :title="$t('forceSubmit')" :btnText="$t('forceSubmitting')" :seconds="5"
      @end="submitStuAnswerWithCount">
      {{ $t('forceSubmitTip') }}
    </PopupTip>
    <PopupTip ref="screenShotSubmitPopupTip" :title="$t('forceSumitTitle')" :btnText="$t('submitting')" :seconds="3"
      @end="submitStuAnswerWithCount">
      {{ $t('forbitScreenShotSubmitTip') }}
    </PopupTip>
    <PopupTip ref="popupTip" :title="$t('forbitScreenShot')" :content="$t('forbitScreenShotTip')"
      :btnText="$t('continueExam')" :seconds="10" :allDisabled="false">
      <p>{{ $t('forbitScreenShotTip') }}</p>
      <i18n tag="p" path="forbitScreenShotTip1">
        <span :class="$style.red" slot="forbitScreenShotTi2">{{ $t('forbitScreenShotTi2') }}</span>
      </i18n>
    </PopupTip>
    <Keyboard v-if="examInfo.openSafeKeyboard" />
    <!-- 滑块验证码 -->
    <VerificationCode ref="verificationCode" @codeCallback="codeCallback" />
    <!-- 简答题上传文件类型提示 -->
    <Popup type="my-popup" :mask="true" :mask-closable="true" ref="viewUploadType" :position="'bottom'">
      <div :class="$style.myPopup3">
        <div :class="$style.title">
          {{ $t('canUploadType') }}
          <i v-if="!isSystemSubmit" class="cubeic-close" :class="$style.iconClose" @click="closeViewUploadType"></i>
        </div>
        <div :class="$style.content" v-html="$t('viewUploadTypeTip')">
        </div>
      </div>
    </Popup>
    <UtilBox v-if="status === 'success' && examInfo.enableCalculator" @canSwiperSlide="canSwiperSlide"
      @clickUtilBox="clickUtilBox" />
    <Popup type="my-popup" :mask="false" ref="calculatorPopup" :position="'bottom'">
      <div :class="$style.calculatorPopup">
        <div :class="$style.title">
          <div>{{ $t('calculator') }}</div>
          <div :class="$style.btns">
            <span class="iconfont icon-expand" @click="calculatorHide"></span>
            <span class="iconfont icon-iconfont01" @click="calculatorDestroy"></span>
          </div>
        </div>
        <div :class="$style.content">
          <Calculator ref="calculator" />
        </div>
      </div>
    </Popup>
    <!-- 屏幕监控异常提醒 -->
    <Popup type="my-popup" :mask="true" :mask-closable="false" ref="myPopup4">
      <div :class="$style.myPopup4">
        <div :class="$style.title">{{ $t('screenMonitorAbnormal') }}</div>
        <div :class="$style.content">
          <i18n tag="span" path="screenMonitorAbnormalTip1">
            <strong slot="n">{{ curScreenExceptionTimes >= examInfo.screenExceptionTimes ?
              examInfo.screenExceptionTimes :
              curScreenExceptionTimes }}</strong>
          </i18n>
          <br>
          <i18n tag="span" path="screenMonitorAbnormalTip2">
            <strong slot="n">{{ examInfo.screenExceptionTimes }}</strong>
          </i18n>
        </div>
        <div :class="$style.btn" @click="screenExceptionPopupHide1">{{ $t('close') }}</div>
      </div>
    </Popup>
    <!-- 屏幕监控异常强制交卷 -->
    <PopupTip ref="screenMonitorPopupTip" :title="$t('screenMonitorAbnormal')" :btnText="$t('submitExam')" :seconds="5"
      @end="submitByScreenExceptionTimes">
      <i18n tag="span" path="screenMonitorAbnormalTip1">
        <strong slot="n">{{ curScreenExceptionTimes >= examInfo.screenExceptionTimes ? examInfo.screenExceptionTimes :
          curScreenExceptionTimes }}</strong>
      </i18n>
      <div>{{ $t('screenMonitorAbnormalTip3') }}</div>
    </PopupTip>
    <!-- 屏幕监控异常验证码提醒 -->
    <Popup type="my-popup" :mask="true" :mask-closable="false" ref="myPopup5">
      <div :class="$style.myPopup4">
        <div :class="$style.title">{{ $t('screenMonitorAbnormal') }}</div>
        <div :class="$style.content">
          <i18n tag="span" path="screenMonitorAbnormalTip5">
            <strong @click="showScreenCode" slot="screenMonitorAbnormalTip4">{{ $t('screenMonitorAbnormalTip4')
              }}</strong>
          </i18n>
        </div>
        <div :class="$style.btnwrap">
          <div :class="$style.btnSubmit" @click="screenCodePopupConfirm">{{ $t('submitExam') }}</div>
          <div :class="$style.btnByCode" @click="showScreenCode">{{ $t('enterCode') }}</div>
        </div>

      </div>
    </Popup>
    <!-- ios专用 手动中断屏幕监控服务时弹出 -->
    <Popup type="my-popup" :mask="true" :mask-closable="false" ref="myPopup6">
      <div :class="$style.myPopup4">
        <div :class="$style.title">{{ $t('noSkippingTips') }}</div>
        <div :class="$style.content">
          {{ $t('toAuthorizeTip') }}
        </div>
        <div :class="$style.btn" @click="startScreenCaptureService">{{ $t('toAuthorize') }}</div>
      </div>
    </Popup>
    <Popup type="my-popup" :mask="false" :mask-closable="true" ref="screenMonitorCodePopup" :position="'bottom'">
      <ScreenCode @closeException="screenCodePopupHide" @closeScreenCode="closeScreenCode" />
    </Popup>
  </Page>
</template>
<script>
import { Toast, Popup } from 'cube-ui'
import { Page } from '@/common/components'
import { formatTimer, getPlatform } from '@/common/utils'
import Loading from './loading'
import Error from './error'
import Timer from './timer'
import SwitchBtn from './switch-btn'
import QuestionOrder from './question-order'
import Slide from './slide'
import Submit from './submit'
import Hint from './leave-hint'
import Pose from './pose'
import PopupTip from './popup-tip'
import Keyboard from './keyboard'
import VerificationCode from '@/components/VerificationCode/index.vue'
import { startExam, getEndTimeNew, getPaper, getLastStuAnswer, getReferAnswer, getStuAnswer, saveStuAnswer, submitStuAnswer, saveExamLimitTime, getExamLimitTime, saveExamLimitCount, getExamLimitCount, getExamLastLeaveTime, saveLocalCache, refreshSession, getVerificationCodeToken, getScreenExceptionsApi } from '@/api'
import Paper from '@/utils/Paper'
import faceApi from '@/api/face'
import Bus from '@/utils/bus'
import Uploader from '@/lib/cordovaPlugin/uploader'
import { getUrlParam, getUniqueValue } from '@/utils/utils'
import { getWaterMarkImg } from "@/utils/watermark.js"
import changeScreenRecordStatus from './screen-record/index.js'
import { forbidScreenRecord, getScreenRecordState, forbidBackWithGesture, multiWindowMode, takeACandidPhotograph, registerOnStop, registerOnStart, deregisterOnStop, deregisterOnStart, startScreenAnalysis, stopScreenCaptureService, startScreenCaptureService, analyze, setCanAnalyze } from '@/lib/cordovaPlugin/mobilePlugin'
import { mapMutations } from 'vuex'
// import { mapState, mapMutations, mapActions, mapGetters } from 'vuex'
// import { exam } from '@/api/config'
import UtilBox from '@/components/UtilBox'
import Calculator from '@/components/UtilBox/calculator.vue'
import ScreenCode from './screen-code.vue'
import {
  keysInit,
} from "@/lib/cordovaPlugin/mobilePlugin.js";

const platform = getPlatform()
export default {
  name: 'exam-view',
  components: {
    Page,
    Loading,
    Error,
    Toast,
    Popup,
    Timer,
    SwitchBtn,
    QuestionOrder,
    Slide,
    Submit,
    Hint,
    Pose,
    PopupTip,
    Keyboard,
    VerificationCode,
    UtilBox,
    Calculator,
    ScreenCode
  },
  data() {
    return {
      status: 'loading', // 网路状态
      examInfo: {
        paperID: undefined,
        autoSavedKey: undefined,
        examTime: undefined,
        examSettingTime: undefined, // 设置的考试时间
        addTime: undefined, // 批次的考试时间副本
        examAddTime: undefined, // 答题时间延长的时间
        examRelationId: undefined, // 批次对应考试的id
        costTime: 0, // 历史已用时间
        startTime: undefined, // 本次考试开始时间
        examUserID: undefined,
        endTime: undefined, // 考试批次结束时间
        status: 0
      },
      paper: {}, // 试卷信息
      paperLength: 0,
      order: 0, // 当前试题
      visible: false, // 题目编号面板是否显示
      stopAutoSubmit: false,
      totalCount: 0,
      leaveLimitTime: 0,
      isPageActive: true,
      hiddenProperty: 'hidden' in document ? 'hidden' : 'webkitHidden' in document ? 'webkitHidden' : 'mozHidden' in document ? 'mozHidden' : null,
      examID: this.$route.params.examID,
      isSnapTime: 0,  // 是否即将开始抓拍倒计时
      snapPhoto: '',
      notTip: true,
      isWxBack: false,
      leftTime: null,
      waterMarkImg: "",
      netWorkOptimization: 0, //网络状态(3为异常)
      forceSubmitType: 0,
      isSystemSubmit: false, // 是否系统交卷
      screenshotsCount: 0,
      leaveMaxTime: 5, // 离开异常行为记录
      behaviorList: [],
      // 验证码相关参数
      randStr: '',
      ticket: '',
      // 当前什么操作出现的验证码 1 开始考试， 2 交卷
      currentHandleVerify: 1,
      // 是否是ios选择文件
      iosSelectDocument: false,
      // 屏幕监控当前异常次数
      curScreenExceptionTimes: null,
      // 交卷时的id 等交卷分析完了再交卷
      submitAnalysisId: null,
      timer4: null,
      // 是否正在滑块验证码
      isSlideCodeChecking: false
    }
  },
  computed: {
    name() {
      return this.$route.name
    },
    query() {
      const name = this.name
      if (name === 'examReport') {
        const { examUserID } = this.$route.params
        return { examUserID }
      } else if (name === 'exam') {
        const { userID, examID } = this.$route.params
        const appVersion = window.appVersion
        return { userID, examID, appVersion }
      }
      return {}
    },
    reportType() {
      const type = this.$route.params.type
      if (type) {
        // 进入考试记录界面
        if (type.includes('3')) {
          return 2 // 显示答案
        } else {
          return 1
        }
      } else {
        // 进入考试界面
        return 0
      }
    },
    user() {
      return this.$store.state.user;
    },
    // 屏幕监控设置项开启
    openScreenException() {
      return (this.examInfo.screenExceptionVerify > 0 || this.examInfo.screenExceptionTimes > 0) && !getPlatform().isHarmony
    }
  },
  watch: {
    'examInfo.examTime': {
      immediate: true,
      handler(newVal) {
        if (this.status === 'success' && this.stopAutoSubmit === false) {
          // 一旦提交成功或提交失败，就自动停止交卷
          // 时间到自动交卷
          if (newVal <= 0) {
            this.isSystemSubmit = true
            this.forceSubmitType = 7
            this.submitStuAnswer(this.$t('endSubmit'), 0)
          }
        }
      }
    },
    curScreenExceptionTimes(val) {
      if (val >= this.examInfo.screenExceptionTimes) {
        // 屏幕监控次数提示
        this.screenExceptionPopupShow2()
      } else {
        // 屏幕监控次数完成强制交卷
        this.screenExceptionPopupShow1()
      }
    }
  },
  methods: {
    ...mapMutations(['changeheaderWarn']),
    // ceshi 
    createCode() {
      this.isSlideCodeChecking = true
      this.$refs.verificationCode.show()
      this.$nextTick(() => {
        this.$refs.verificationCode.createCode()
      })
    },
    // 滑块回调
    codeCallback(val) {
      const { ret, randstr, ticket } = val
      this.randStr = randstr || ''
      this.ticket = ticket || ''
      // 验证码校验成功之后手动校验token
      this.verificationCodeToken()
    },
    // 校验token
    async verificationCodeToken() {
      try {
        let verificationParams = {
          ticket: this.ticket,
          randStr: this.randStr,
          examId: this.$route.params.examID || this.examID || '',
          terminal: 1
        }
        const { data } = await getVerificationCodeToken(verificationParams)
        this.isSlideCodeChecking = false
        if (data && data.result && data.code == 1) {
          // 验证码校验成功后，重新调 1:开始考试,2:提交考试接口 3:超时自动提交考试 4 次数受限交卷 5 超过单次时长后台交卷 6 网络断开重新交卷
          switch (this.currentHandleVerify) {
            case 1:
              this.initData()
              break;
            case 2:
              this.submitStuAnswer(this.$t('submitting'))
              break;
            case 3:
              this.submitStuAnswer(this.$t('endSubmit'), 0)
              break;
            case 4:
              this.submitStuAnswerWithCount()
              break;
            case 5:
              this.submitStuAnswerWithTime()
              break;
            case 6:
              this.reSubmitPaper()
              break;
            default:
              break;
          }
        } else {
          // 校验token失败 重新弹出验证码
          this.createCode()
        }
      } catch (error) {
        console.log(error, 'errorerrorerrorerror');
        // 接口报错 也重新弹出验证码
        this.createCode()
      }
    },
    // 入口
    start() {
      // 延长token有效期
      window.Authorization && refreshSession(window.Authorization)
      if (this.name === 'exam') {
        // 进行考试界面
        return startExam(this.query).then(({ data }) => {
          if (data && data == 1001) {
            return 'needVerify'
          }
          const { status, isNew, paperID, autoSavedKey, examTime, addTime, examAddTime, examRelationID, examUserID, endTime, currentTime, leaveLimit, leaveLimitTime, enableSnapshotMonitor, enableUpload, commitPaperTime, questionOrderBreak, optionOrderBreak, waterMark, sequentialAnswer, screenshotMonitor, openSafeKeyboard, hideQuestionType, enableAppUpload, enableCalculator, screenExceptionTimes, screenExceptionVerify } = data

          let examLastTime = Number(examTime) * 60 | 0 // 设置的考试时间
          let examRemainingTime = parseInt((endTime - currentTime) / 1000) // 批次结束剩余时间
          if (!isNew) {
            examLastTime += (examAddTime || 0) * 60 //延长的答题时间
          }
          if (examRemainingTime < examLastTime) {
            if (examRemainingTime < 0) {
              examLastTime = 0
            } else {
              examLastTime = examRemainingTime
            }
          }
          this.examInfo = { paperID, autoSavedKey, examTime: examLastTime, examSettingTime: examTime, costTime: 0, addTime: addTime || 0, examAddTime: examAddTime || 0, examRemainingTime, examRelationId: examRelationID, examUserID, endTime, currentTime, status, enableSnapshotMonitor, enableUpload, commitPaperTime, questionOrderBreak, optionOrderBreak, waterMark, sequentialAnswer, screenshotMonitor, openSafeKeyboard, hideQuestionType, enableAppUpload, enableCalculator, screenExceptionTimes, screenExceptionVerify }
          this.examInfo.startTime = new Date()
          this.examInfo.examUserAddTime = 0

          this.isNew = isNew
          this.totalCount = leaveLimit
          this.leaveLimitTime = leaveLimitTime
          // if (enableSnapshotMonitor) {
          //   this.randomPhotoTime()
          // }
          if (waterMark) {
            console.log("this.user", this.user)
            this.waterMarkImg = getWaterMarkImg({
              name: this.user.name,
              loginName: this.user.userName
            })
          }
          window.traceId = examUserID || window.traceId
          if (status === 0) {
            // 参加考试
            if (isNew) {
              // 标记一次开始考试事件
              this.behavior('开始考试')
              return Promise.all([this.getPaperForSpeak(paperID), this.getExamLimitCount()]).then(([arg]) => {
                return arg
              })
            } else {
              // 获取答题记录
              return Promise.all([this.getPaperForSpeak(paperID), this.getLastStuAnswer(autoSavedKey, examUserID), this.getExamLimitCount(), this.getExamLimitLeftTime()]).then(([arg1, arg2]) => {
                return { ...arg1, ...arg2 }
              })
            }
          } else if (status === 1) {
            this._warnToast(this.$t('noExamTip1')).show()
          } else if (status === 2) {
            this._warnToast(this.$t('noExamTip3')).show()
          } else if (status === 3) {
            this._warnToast(this.$t('examEnd')).show()
          } else if (status === 4) {
            this._warnToast(this.$t('lateExam')).show()
          }
          return null
        })
      } else if (this.name === 'examReport') {
        // 查看考试记录页面
        window.traceId = this.query.examUserID || window.traceId
        return getStuAnswer(this.query).then(({ data }) => {
          const { paperID, hideQuestionType } = data
          this.examInfo.hideQuestionType = hideQuestionType
          return Promise.all([this.getPaper(paperID), this.getReferAnswer(paperID, this.$route.query.id)]).then(([arg1, arg2]) => {
            return { ...arg1, ...arg2, stuAnswer: data.answerJson, examStatus: data.status }
          })
        })
      }
    },
    // 获取试卷
    getPaper(paperID) {
      let params = { paperID, userID: this.query.userID, examID: this.examID }
      if (this.name === 'examReport') {
        params = { paperID, examuserId: this.query.examUserID, examID: this.$route.query.id }
      } else if (this.name === 'exam' && !this.isNew) {
        params.examuserId = this.examInfo.examUserID
      }
      return getPaper(params).then(({ data }) => {
        return { paper: data }
      })
    },
    // 口语题需要使用getReferAnswer
    getPaperForSpeak(paperID) {
      return this.getPaper(paperID).then(({ paper }) => {
        // const status = paper.part.some(it => {
        //   return Array.isArray(it.children) && it.children.some(it => (it.type === 16 || it.type === 19))
        // })
        // if (status) {
        //   return getReferAnswer({ paperID,userId:this.$route.params.userID }).then(({ data }) => {
        //     return { paper, referAnswer: data }
        //   })
        // } else {
        //   return { paper }
        // }
        return { paper }
      })
    },
    // 获取用户的最后一次答题记录
    getLastStuAnswer(autoSavedKey, examUserId) {
      return getLastStuAnswer({ autoSavedKey, examUserId }).then(({ data }) => {
        const { surplus, costTime, examUserID, examUserAddTime } = data
        let lastTime
        if (costTime) {
          this.examInfo.costTime = costTime
          lastTime = this.examInfo.examSettingTime * 60 - costTime
          if (!this.isNew) {
            lastTime += this.examInfo.examAddTime * 60
          }
          if (lastTime < 0) {
            lastTime = 0
          }
          if (lastTime < this.examInfo.examTime) {
            this.examInfo.examTime = lastTime
          }
        } else if (surplus) {
          const [minute, second] = surplus.split(':')
          lastTime = Number(minute) * 60 + Number(second)
          if (lastTime < this.examInfo.examTime) {
            this.examInfo.examTime = lastTime
          }

          if (examUserID !== undefined) {
            this.examInfo.examUserID = examUserID
          }
        }
        this.examInfo.initCostTime = costTime || 0
        this.examInfo.examUserAddTime = examUserAddTime || 0;
        this.examInfo.examTime = Math.min(this.examInfo.examRemainingTime, this.examInfo.examSettingTime * 60 + this.examInfo.examUserAddTime * 60 - this.examInfo.initCostTime)
        return { stuAnswer: (data.tabs || []) }
      })
    },
    // 获取试卷答案
    getReferAnswer(paperID, examID) {
      if (this.reportType === 2) {
        return getReferAnswer({ paperID, examID, userId: this.$route.params.userID }).then(({ data }) => {
          return { referAnswer: data }
        })
      } else {
        return Promise.resolve({})
      }
    },
    setScreenshotsCount(isInit) {
      this.$refs.popupTip.close()
      this.showScreenshotsTip(isInit)
      window.localStorage.setItem(this.examInfo.examUserID + 'screenshotsCount', this.screenshotsCount)
    },
    getScreenshotsCount() {
      const count = window.localStorage.getItem(this.examInfo.examUserID + 'screenshotsCount')
      if (count > 0) {
        this.screenshotsCount = Math.max(Number(count), this.screenshotsCount)
        this.setScreenshotsCount(true)
      }
    },
    getExamLimitCount() {
      const { examUserID } = this.examInfo
      return getExamLimitCount({ examUserID }).then(({ data }) => {
        const { count, screenshotsCount } = data
        this.saveLeftInfo(count)
        // const { count, lastLeftTime } = data
        // this.saveLeftInfo(count, lastLeftTime)
        screenshotsCount && (this.screenshotsCount = screenshotsCount)
        this.getScreenshotsCount()
      })
    },
    // 保存离开时间
    saveExamLimitTime() {
      const { examUserID } = this.examInfo
      return saveExamLimitTime({ examUserID }).then(({ data }) => {
        if (data === false) throw data
      })
    },
    // 获取上次离开时间
    getExamLimitTime(callback) {
      const { examUserID } = this.examInfo
      return getExamLimitTime({ examUserID }).then(({ data }) => {
        callback && callback(data)
      })
    },
    // 获取上次离开时间(限制离开时长专用)
    getExamLimitLeftTime() {
      let time = window.localStorage.getItem(this.examInfo.examUserID + 'leaveTime')
      if (time) {
        this.saveLeftInfoForLimit(new Date(time * 1))
      }
      const { examUserID } = this.examInfo
      return getExamLastLeaveTime({ examUserID }).then(({ data }) => {
        if (data.lastLeaveTime > 0) {
          this.saveLeftInfoForLimit(new Date(data.lastLeaveTime))
        }
        this.clearLimitLeftTime()
      }).catch(() => {
        this.clearLimitLeftTime()
      })
    },
    // 保存消耗次数
    saveExamLimitCount(leaveTime) {
      const { examUserID } = this.examInfo
      console.log(leaveTime)
      // 异常次数消耗的同时记一次异常log
      this.behavior('异常')
      return saveExamLimitCount({ examUserID, leaveTime }).then(({ data }) => {
        if (data === false) throw data
      })
    },
    // 处理获取的数据
    refresh() {
      this.status = 'loading'
      return this.start().then((data) => {
        if (data && data == 'needVerify') {
          return data
        }
        if (this.examInfo.status !== 0) return
        const instance = new Paper(data, this.reportType, this.isNew, this.examInfo)
        this.paperStore = instance
        this.paper = instance.paper
        this.paperLength = instance.length
        process.env.NODE_ENV === 'development' && console.log(this.paper)
        this.status = 'success'
      }).catch(e => {
        this.status = 'fail'
        throw e
      })
    },
    // 同步题目题号
    syncOrder(order) {
      order && (this.order = order)
    },
    // renderFormula 为了重新再渲染公式等
    renderFormula() {
      // 渲染公式
      setTimeout(() => {
        try {
          MathJax.Hub.Queue(['Typeset', MathJax.Hub, 'MathDiv'])
        } catch (e) { }
        // 富文本内图片添加预览
        this.imageBindEvent()
      })
    },
    // 手动指定slide的卡片
    gotoPage(slideIndex, order, question) {
      const slide = this.$refs.slide
      slide.gotoPage(slideIndex, order, question)
    },
    // 隐藏题目编号面板
    hide(event) {
      this.visible = false
    },
    delegateHide(event) {
      const dataset = event.target.dataset
      if (dataset.role === 'order') {
        this.hide()
      }
    },
    // 保存用户答题 保存在本地，不用于上传
    syncStuAnswer(ID, answer) {
      this.paperStore.saveStudentAnswer(ID, answer)
      this.$nextTick(() => {
        // 下拉题渲染公式
        this.renderFormula()
      })
    },
    // 同步音频次数(题目ID)
    syncAudioCount(ID) {
      const arr = this.paperStore.saveAudioCount(ID)
      if (arr.some(it => it > 0)) {
        this.saveStuAnswer().catch(error => {
          throw error
        })
      }
    },
    // 微信返回
    backByWx() {
      this.isWxBack = true
      if (this.notTip) {
        this.back()
      }
    },
    // 定制返回按钮
    back() {
      if (getUrlParam('mp') !== '1') {
        if (this.name === 'exam') {
          if (this.status === 'success') {
            this.$refs.submit.show()
            return false
          } else {
            return true
          }
        } else {
          return true
        }
      } else {
        let from = this.$route.query.from || 'info'
        if (this.name === 'exam') {
          if (this.status === 'success') {
            this.$refs.submit.show()
            return false
          } else {
            this.exitPage(this, { name: from, params: { userID: this.$route.params.userID, examID: this.$route.query.id } })
            return false
          }
        } else {
          goPage(this, { name: from, params: { userID: this.$route.params.userID, examID: this.$route.query.id } })
          return false
        }
      }
    },
    cancelSubmit() {
      if ((platform.isAndroid || platform.isHarmony) && getUrlParam('mp') === '1' && this.name === 'exam' && this.isWxBack) {
        this.isWxBack = false
        window.history.pushState(null, null, window.location.href);
      }
    },
    // 上传用户答题记录
    saveStuAnswer() {
      return saveStuAnswer(this.collectStuAnswer()).then((response) => {
        const data = response.data
        if (data === false) return data
        else return response
      })
    },
    // 自动上传答题记录
    autoSaveStuAnswer() {
      return this.saveStuAnswer().catch((error) => {
        this.$saveLog(`${this.examID}_${this.examInfo.examUserID}_答题记录保存失败_${JSON.stringify(error)}`)
        throw error
      })
    },
    // 手动上传答题记录
    manualSaveStuAnswer() {
      let toast = this._loadingToast(this.$t('savingAnswer'))
      const timer = setTimeout(() => {
        toast.show()
      }, 300)
      this.saveStuAnswer().then(() => {
        clearTimeout(timer)
        toast.hide()
        if (this.totalCount > 0 && !this.stopDetect()) {
          // 次数减一
          const { count } = this.getLeftInfo()
          // 保存在本地
          this.saveLeftInfo(count + 1, new Date())
          this.clearLeftTime()
          // if (this.totalCount > 0 && this.stopDetect()) {
          //   this.submitStuAnswer('正在交卷', undefined, true)
          // } else {
          //   this.exit()
          //   // 标记一次离开页面事件
          //   this.behavior('离开页面', 'draft')
          // }
          // 标记一次离开页面事件
          this.behavior('离开页面', 'draft')
          // 保存一次离开服务器
          this.saveExamLimitCount(this.formatTime(new Date()))
          this.exit()
        } else {
          // 标记一次离开页面事件
          this.behavior('离开页面', 'draft')
          this.exit()
        }
      }).catch((e) => {
        clearTimeout(timer)
        toast.hide()
        this._failToast(this.$t('autoSaveFailed')).show()
        throw e
      })
    },
    async submitStuAnswer(loadingToastTxt, examTime, isLimitSubmit) {
      if (this.stopSubmit === true || this.isSlideCodeChecking) {
        return
      }
      this.stopAutoSubmit = true // 防止多次自动提交
      this.stopSubmit = true // 防止多次提交
      const toast = this._loadingToast(loadingToastTxt)
      toast.show()
      const data = this.collectStuAnswer(examTime)
      data.examUserID = this.examInfo.examUserID
      try {
        if (this.openScreenException) {
          await this.openScreenAwait()
        }
      } finally {
        this.submitStuAnswerRequest(data, toast, isLimitSubmit, examTime)
      }
    },
    // 开启屏幕监控时，交卷需要等待
    async openScreenAwait() {
      // 要交卷时分析一次屏幕
      this.submitAnalysisId = new Date().getTime()
      analyze({ id: this.submitAnalysisId, timestamp: new Date().getTime(), ignoreCD: true })
      // 10s后强制置空 防止没有回调交不了卷
      setTimeout(() => {
        this.submitAnalysisId = null
      }, 10 * 1000)
      // 这里必须要等this.submitAnalysisId为空才能执行下面
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.submitAnalysisId) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 300)
      })
    },
    // 交卷
    submitStuAnswerRequest(data, toast, isLimitSubmit, examTime) {
      submitStuAnswer(data).then(({ data }) => {
        if (data && data == 1001) {
          toast.hide()
          this.stopSubmit = false // 防止多次提交
          // 根据submitStuAnswer方法的参数判断提交试卷类型 2 手动 3 超时自动 4 次数限制交卷 6 网络异常重新交卷
          if (examTime == 0) {
            this.currentHandleVerify = 3
          } else if (isLimitSubmit && examTime) {
            this.currentHandleVerify = 6
          } else if (isLimitSubmit && examTime == undefined) {
            this.currentHandleVerify = 4
          } else {
            this.currentHandleVerify = 2
          }
          return this.createCode()
        }
        if (data === false) throw data
        toast.hide()
        this.stopSubmit = true // 防止多次提交
        this._successToast(this.$t('submitSuccess')).show()
        // 交卷成功标记一次交卷事件
        this.behavior('交卷', isLimitSubmit)
        this.$refs.headerWarn.style.display = 'none'
        this.changeheaderWarn(false)
        this.sendLocalData(this.examInfo.examUserID);
        this.$saveLog(`${this.examID}_${this.examInfo.examUserID}_${this.forceSubmitType}_交卷响应成功_${JSON.stringify(data)}`)
      }).catch((error) => {
        toast.hide()
        this.stopSubmit = false
        this.$refs.headerWarn.style.display = 'flex'
        this.changeheaderWarn(true)
        this.$refs.myPopup2.show()
        this.handlerError(data)
        this.$saveLog(`${this.examID}_${this.examInfo.examUserID}_${this.forceSubmitType}_交卷响应失败_${JSON.stringify(data)}`)
        throw error
      })
    },
    // 手动交卷
    manualSubmitStuAnswer() {
      let costTime = Math.round((new Date() - this.examInfo.startTime) / 1000)
      if (costTime < 0) {
        costTime = 0
      }
      var totalCostTime = this.examInfo.costTime + costTime
      if (totalCostTime < this.examInfo.commitPaperTime * 60) {
        this.$createDialog({
          type: 'alert',
          content: this.$t('commitLimitTimeTips', { time: this.examInfo.commitPaperTime })
        }).show()
        return
      }
      const length = this.paperLength - this.getAnsweredLength()
      if (length > 0) {
        this.savePaperDialog(`<span class="subanstip">${this.$t('submitTip1', { num: length })}</span>`, this.$t('submitExam'), this.$t('continueExam'))
      } else {
        this.savePaperDialog('<span class="subanstip">' + this.$t('examSubmitConfirm') + '</span>', this.$t('confirm'), this.$t('cancel'))
      }
    },
    // 次数受限而交卷
    submitStuAnswerWithCount() {
      this.isSystemSubmit = true
      return this.submitStuAnswer(this.$t('submitting'), undefined, true)
    },
    // 超过单次时长后台交卷
    async submitStuAnswerWithTime() {
      if (this.stopSubmit === true) {
        return
      }
      this.isSystemSubmit = true
      this.stopAutoSubmit = true // 防止多次自动提交
      this.stopSubmit = true // 防止多次提交
      const data = this.collectStuAnswer()
      data.examUserID = this.examInfo.examUserID
      if (this.openScreenException) {
        await this.openScreenAwait()
      }
      return submitStuAnswer(data).then(({ data }) => {
        if (data && data == 1001) {
          this.stopSubmit = false
          // 提交试卷类型 5 超过单次时长后台交卷
          this.currentHandleVerify = 5
          return this.createCode()
        }
        if (data === false) throw data
        this.stopSubmit = true // 防止多次提交
        // 交卷成功标记一次交卷事件
        this.behavior('交卷', true)
        this.sendLocalData(this.examInfo.examUserID);
      }).catch((error) => {
        // this._failToast('交卷失败').show()
        this.stopSubmit = false
        if (!this.retryTimes || this.retryTimes < 3) {
          this.submitStuAnswerWithTime()
          this.retryTimes = (this.retryTimes || 0) + 1
        } else {
          this.handlerError(data)
        }
      })
    },
    // 屏幕监控次数超出设置自动交卷
    submitByScreenExceptionTimes() {
      this.isSystemSubmit = true
      this.forceSubmitType = 9  // 屏幕监控异常强制收卷
      this.submitStuAnswer(this.$t('submitting'), undefined, true)
    },
    submitStuAnswerWithCount() {
      this.isSystemSubmit = true
      return this.submitStuAnswer(this.$t('submitting'), undefined, true)
    },
    confirmForLimitTime() {
      this.exit()
    },
    // 手动交卷前使用弹窗确定
    savePaperDialog(content, confirmTxt, cancelTxt) {
      return this.$createDialog({
        type: 'confirm',
        content,
        confirmBtn: { text: confirmTxt, active: true },
        cancelBtn: { text: cancelTxt },
        onConfirm: () => {
          this.submitStuAnswer(this.$t('submitting'))
        }
      }).show()
    },
    // 退出页面
    exitPage(vm, options) {
      if (this.openScreenException) {
        stopScreenCaptureService()
      }
      if ((platform.isAndroid || platform.isHarmony) && getUrlParam('mp') === '1' && this.name === 'exam') {
        this.notTip = false
        if (window.history && window.history.length > 1) {
          window.history.go(-1)
          setTimeout(() => {
            goPage(vm, options)
          }, 100)
        }
      } else {
        goPage(vm, options)
      }
    },
    // 退出考试
    exit() {
      // this.$router.push({ name: 'examList', query: { userid: this.query.userID } })
      if (window.unitId) {
        window.broadcaster.fireNativeEvent('unitActivityRefresh', {})  //更新单元模块状态
        this.$router.back()
        if (this.openScreenException) {
          stopScreenCaptureService()
        }
        return
      }
      this.exitPage(this, { name: 'examList', query: { userid: this.query.userID } })
    },
    // 获取答题记录
    collectStuAnswer(endTime) {
      const { autoSavedKey, examTime } = this.examInfo
      let surplus
      if (Number.isFinite(endTime) && endTime >= 0) {
        surplus = formatTimer(endTime, 'mm:ss')
      } else {
        surplus = formatTimer(examTime, 'mm:ss')
      }
      let costTime = Math.round((new Date() - this.examInfo.startTime) / 1000)
      if (costTime < 0) {
        costTime = 0
      }
      this.examInfo.startTime = new Date()
      this.examInfo.costTime += costTime
      const tabs = this.paperStore.collectStudentAnswers()
      const data = { autoSavedKey, surplus, costTime: this.examInfo.costTime, tabs, examUserId: this.examInfo.examUserID }
      if (this.examInfo.optionOrderBreak) {
        data.choiceItems = this.paperStore.choiceItems || []
      }
      return data
    },
    // 获取用户已作答题目的数量
    getAnsweredLength(record) {
      return this.paperStore.getAnsweredLength()
    },
    // 保存离开时间和次数到store中
    saveLeftInfo(count, time) {
      if (time) {
        window.localStorage.setItem(this.examInfo.examUserID + 'time', time.getTime())
      }

      time = time || 0
      this.$store.commit('saveLeftInfo', {
        examUserID: this.examInfo.examUserID,
        time: time,
        count
      })
    },
    // 获取离开时间和次数
    getLeftInfo() {
      return this.$store.getters.getLeftInfo(this.examInfo.examUserID)
    },
    // 清除心跳时间
    clearLeftTime() {
      window.localStorage.removeItem(this.examInfo.examUserID + 'time')
      this.$store.commit('clearTime', {
        examUserID: this.examInfo.examUserID
      })
    },
    // 保存离开时间到store中(限制单次离开时长专用)
    saveLeftInfoForLimit(time) {
      if (time) {
        window.localStorage.setItem(this.examInfo.examUserID + 'leaveTime', new Date(time).getTime())
      }
      time = time || 0
      this.$store.commit('saveLeftTime', {
        examUserID: this.examInfo.examUserID,
        time: time
      })
      this.leftTime = time
    },
    // 清除单次时长心跳时间
    clearLimitLeftTime() {
      if (!this.isTimeOver()) {
        window.localStorage.removeItem(this.examInfo.examUserID + 'leaveTime')
        this.$store.commit('clearLeftTime', {
          examUserID: this.examInfo.examUserID
        })
      }
    },
    // 是否已超时
    isTimeOver() {
      console.log('监听时间' + this.leaveLimitTime, this.getLeftInfo().leftTime ? new Date() - this.getLeftInfo().leftTime : 0)
      return this.leaveLimitTime > 0 && this.getLeftInfo().leftTime > 0 && (new Date() - this.getLeftInfo().leftTime > this.leaveLimitTime * 1000 || this.getLeftInfo().leftTime - new Date() > 0)
    },
    // 是否停止检测
    stopDetect() {
      return this.totalCount <= this.getLeftInfo().count
    },
    showHint() {
      //2020-07 优化需求 不论是否开启切屏监控，学生中途离开后回到考试页面时，浮窗提示已离开次数
      const { examUserID } = this.examInfo
      const { count } = this.getLeftInfo(examUserID)
      if (this.isTimeOver()) {
        this.$refs.hint.show()
        this.submitStuAnswerWithTime()
      } else if (this.totalCount > 0) {
        this.$refs.hint.show()
      } else {
        this.$createToast({ type: 'warn', time: 2000, txt: this.$t('leaveExamTimesTips', { count }) }).show()
      }
    },
    judgeUnusualLeave() {
      if (this.isTimeOver()) {
        this.showHint()
      } else if (this.getLeftInfo().time && ((new Date() - this.getLeftInfo().time > this.leaveMaxTime * 1000) || (this.getLeftInfo().time - new Date() > 0))) {
        console.log(this.$t('unusualLeave'))
        this.saveLeftInfo(this.getLeftInfo().count + 1)
        this.saveExamLimitCount(this.formatTime(new Date(this.getLeftInfo().time)))
        this.clearLeftTime()
        this.showHint()
      }
      // this.getExamLimitTime((data) => {
      //   if (data.leaveTime && (new Date(data.currentTime) - new Date(data.leaveTime) > 5000)) {
      //     this.saveLeftInfo(this.getLeftInfo().count + 1, new Date(data.leaveTime))
      //     this.saveExamLimitCount(data.leaveTime)
      //     this.$refs.hint.show()
      //   }
      // })
    },

    _onPageChange(event) {
      if (this._pageHidden() === true) {
        // this._startTime = new Date()
        if (!this.stopDetect()) {
          // this._leaveTime = 0
          // if (this._leaveTimer) {
          //   clearInterval(this._leaveTimer)
          //   this._leaveTimer = null
          // }
          // this._leaveTimer = setInterval(() => {
          //   this._leaveTime++
          // }, 1000)

          this.saveExamLimitTime()
        }
      } else {
        if (!this.stopDetect()) {
          this.judgeUnusualLeave()
          // if (new Date() - this._startTime > 5000 || this._leaveTime > 5) {
          //   this.saveLeftInfo(this.getLeftInfo().count + 1, this._startTime)
          //   this.saveExamLimitCount()
          //   this.$refs.hint.show()
          // }
        }
      }
    },

    _onAndroidPageHide() {
      this.iOSCallState = true
      // if (!this.stopDetect()) {
      //   this.saveExamLimitTime()
      // }
      // this._androidPageHide = true
    },

    _onAndroidPageShow() {
      this.iOSCallState = false
      // if (this._androidPageHide === true) {
      //   this._androidPageHide = false
      //   if (!this.stopDetect()) {
      //     this.judgeUnusualLeave()
      //   }
      // }
    },

    _pageHidden() {
      const hidden = document.hidden
      if (hidden === undefined) {
        return document.webkitHidden
      } else {
        return hidden
      }
    },
    _onVisibilityChange() {
      if (platform.isAndroid || platform.isHarmony) {
        registerOnStop('visibilitychange', () => {
          this._onAndroidPageHide()
        })
        registerOnStart('visibilitychange', () => {
          this._onAndroidPageShow()
        })
      } else {
        // document.addEventListener('visibilitychange', this._onPageChange)
      }
    },
    _offVisibilityChange() {
      if (platform.isAndroid || platform.isHarmony) {
        deregisterOnStop('visibilitychange')
        deregisterOnStart('visibilitychange')
      } else {
        // document.removeEventListener('visibilitychange', this._onPageChange)
      }
    },
    _warnToast(txt) {
      return this.$createToast({ type: 'warn', time: 1000, $events: { timeout: 'exit' }, txt })
    },
    _loadingToast(txt) {
      return this.$createToast({ type: 'loading', mask: true, txt, time: 0 })
    },
    _successToast(txt) {
      return this.$createToast({ type: 'correct', mask: true, txt, time: 1000, $events: { timeout: 'exit' } })
    },
    _failToast(txt) {
      return this.$createToast({ type: 'error', mask: true, txt, time: 2000 })
    },
    formatTime(date) {
      let timeStr = ''
      if (!date) {
        return timeStr
      }

      timeStr += date.getFullYear() + '-'
      if (date.getMonth() < 9) {
        timeStr += '0' + (date.getMonth() + 1) + '-'
      } else {
        timeStr += (date.getMonth() + 1) + '-'
      }
      if (date.getDate() < 10) {
        timeStr += '0' + date.getDate() + ' '
      } else {
        timeStr += date.getDate() + ' '
      }
      if (date.getHours() < 10) {
        timeStr += '0' + date.getHours() + ':'
      } else {
        timeStr += date.getHours() + ':'
      }
      if (date.getMinutes() < 10) {
        timeStr += '0' + date.getMinutes() + ':'
      } else {
        timeStr += date.getMinutes() + ':'
      }
      if (date.getSeconds() < 10) {
        timeStr += '0' + date.getSeconds()
      } else {
        timeStr += date.getSeconds()
      }

      return timeStr
    },
    getDeviceVersion() {
      let ua = navigator.userAgent
      if (platform.isAndroid || platform.isHarmony) {
        let sss = ua.split(';')
        let index = -1
        for (let i in sss) {
          if (sss[i].indexOf('Build/') > 0) {
            index = i
            break
          }
        }
        if (index > -1) {
          return sss[index].substring(0, sss[index].indexOf('Build/'))
        }
      } else {
        return 'iPhone'
      }
    },
    postBehavior() {
      faceApi.saveBehavior()
    },
    savePoseBehavior(data) {
      const { type, createTime, url } = data
      this.behavior(type, false, createTime, url)
    },
    _behavior(type, draft, createTime, url) {
      console.log(this.hiddenProperty)
      if (this.reportType !== 0) {
        return
      }
      this.isSavingBehavior = true
      let behaviorMap = {
        '监控视频': 0,
        '开始考试': 1,
        '进入页面': 2,
        '离开页面': 3,
        '交卷': 4,
        '异常': 5,
        '截图': 10,
        '屏幕监控正常': 11,
        '屏幕监控异常': 12
      }
      let data = {
        userId: this.$route.params.userID,
        examId: this.examID,
        examUserId: this.examInfo.examUserID,
        behaviorType: behaviorMap[type],
        terminal: 2,
        isForceSubmit: 0,
        deviceInfo: this.getDeviceVersion(),
        extra: '',
        terminalId: window.deviceID
      }
      let extra = {
        isDraft: false,
        isH5: false,
        isAPP: false,
        isCall: false
      }
      if (type === '上传附件') {
        data.behaviorType = behaviorMap['离开页面']
        data.exception = this.$t('uploadFileType')
      }
      if (type === '离开页面') {
        if (draft) {
          extra.isDraft = true
        } else {
          if (platform.isAndroid || platform.isHarmony) {
            if (document[this.hiddenProperty]) {
              // console.log(type + '----h5')
              extra.isH5 = true
            }
            if (this.iOSCallState) {
              // console.log(type + '-----APP')
              extra.isAPP = true
            }
          } else {
            if (this.iOSCallState) {
              // console.log(type + '----电话呼入')
              extra.isCall = true
            }
            if (document[this.hiddenProperty]) {
              // console.log(type + '----h5')
              extra.isH5 = true
            }
          }
        }
        data.extra = JSON.stringify(extra)
        if (!this.isTimeOver()) {
          this.saveLeftInfoForLimit(new Date())
        }
      } else if (type === '交卷') {
        // data.extra = JSON.stringify({ isForce: draft === true })
        data.isForceSubmit = 0
        if (this.isTimeOver()) {
          data.isForceSubmit = 3
        } else if (this.totalCount > 0 && this.stopDetect()) {
          data.isForceSubmit = 1
        } else if (this.forceSubmitType > 0) {
          data.isForceSubmit = this.forceSubmitType
        }
        this.isSubmitted = true
      }
      if (type === '监控视频' || type === '屏幕监控正常' || type === '屏幕监控异常') {
        data.createTime = createTime
        data.url = url
      }
      console.log(data)
      // 后台根据时间排序用
      data.requestTime = new Date().getTime()
      console.log("行为轨迹--" + type + "--时间", data.requestTime)
      this.setLocalExamBehavior(data)
      this.$saveLog(`行为轨迹_${JSON.stringify(data)}`)
      if (!this.isTimeOver() || type === '交卷') {
        faceApi.saveBehavior(data).then(() => {
          this.isSavingBehavior = false
        }).catch((error) => {
          this.isSavingBehavior = false
          this.$saveLog(`行为轨迹保存失败_${JSON.stringify(data)}_${JSON.stringify(error)}`)
        })
      }
    },
    behavior(type, draft, createTime, url) {
      this.behaviorList.push(() => {
        this._behavior(type, draft, createTime, url)
      })
      const behaviorFun = this.behaviorList.shift()
      if (!this.isSavingBehavior) {
        behaviorFun && behaviorFun()
      } else {
        setTimeout(() => {
          behaviorFun && behaviorFun()
        }, 20)
      }
    },
    randomPhotoTime() {
      var self = this
      let time = Math.round(Math.random() * 120) + 180
      // let time = 20
      self.photoTimer = setTimeout(() => {
        self.isSnapTime = 3
        self.snapTimer = setInterval(() => {
          self.isSnapTime--
          if (self.isSnapTime === 0) {
            clearInterval(self.snapTimer)
            self.takeSnapping()
          }
        }, 1000)
        clearTimeout(self.photoTimer)
        self.randomPhotoTime()
      }, time * 1000)
    },
    takeSnapping() {
      var self = this
      takeACandidPhotograph((filePath) => {
        console.log('下面是抓拍的本地路径')
        console.log(filePath)
        // let instance = self.$createUpopup({
        //   content: self.$t('uploading') + '...'
        // })
        self.upload(filePath)
      }, (err) => {
        console.log(err)
      })
    },
    upload(filePath) {
      let self = this
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      let uploader = new Uploader()
      uploader.upload(filePath, {
        successCb: function (path, remoPath) {
          console.log('-----3-3--33-3--3-3-3-3-3-3-3-3-3-')
          console.log(path, remoPath)
          if (self.name !== 'exam') {
            return
          }
          if (path == filePath) {
            self.snapPhoto = remoPath
            faceApi.saveSnap({
              examUserId: self.examInfo.examUserID,
              url: remoPath
            })
            var photoShowTimer = setTimeout(() => {
              self.snapPhoto = ''
              clearTimeout(photoShowTimer)
            }, 2000)
          }
        }
      })

      // let str = filePath.replace('file://', '')
      // let timestamp = new Date().getTime()
      // let fileName = filePath.split('.').pop()
      // var random = Math.ceil(Math.random() * 10000)
      // var random2 = Math.ceil(Math.random() * 10000)
      // var remoteFile = 'resource' + '/web/' + random + '/' + random2 + '/' + timestamp + '.' + fileName
      // window.onUploadSuccess = function (path, remoPath) {
      //   if (self.name !== 'exam') {
      //     return
      //   }
      //   if (path == filePath) {
      //     self.snapPhoto = remoPath
      //       faceApi.saveSnap({
      //       examUserId: self.examInfo.examUserID,
      //       url: remoPath
      //     })
      //     var photoShowTimer = setTimeout(()=> {
      //       self.snapPhoto = ''
      //       clearTimeout(photoShowTimer)
      //     }, 2000)
      //   }
      // }
      // window.cordova.require('cordova/exec')(function (result) {
      //   console.log(result)
      // }, function (err) {
      //   console.log(err)
      // }, 'UUploader', 'upload', [str,remoteFile], null, null)
    },
    showImage(e) {
      var pre = this.$createImagePreview({
        imgs: [e.target.src]
      }, (h) => {
        return h('div', {
          slot: 'footer'
        }, '')
      })
      pre.show()
    },
    imageBindEvent() {
      const imgElements = document.querySelectorAll(".has-img img")
      imgElements.forEach((element) => {
        element.addEventListener('click', this.showImage)
      })
    },
    closeNetworkError() {
      this.$refs.myPopup1.hide()
      this.netWorkOptimization = 0
    },
    reSubmitPaper() {
      this.$refs.myPopup2.hide()
      this.submitStuAnswer(this.$t('submitting'), this.examInfo.examTime, true)
    },
    closeSubmitError() {
      this.$refs.myPopup2.hide()
      this.netWorkOptimization = 0
    },
    quiteExam() {
      setTimeout(() => {
        this.$createDialog({
          type: 'confirm',
          title: this.$t('noSkippingTips'),
          content: this.$t('quiteExamAgain'),
          confirmBtn: {
            text: this.$t('confirm'),
          },
          cancelBtn: {
            text: this.$t('cancel'),
          },
          onConfirm: () => {
            this.autoSaveStuAnswer()
            setTimeout(() => {
              this.exit()
            })
          }
        }).show()
      })
    },
    teacherForceSubmit() {
      //教师提前收卷
      this.isSystemSubmit = true
      this.forceSubmitType = 5
      this.$refs.teacherPopupTip.show()
    },
    showScreenshotsTip(isInit) {
      if (this.examInfo.screenshotMonitor && this.screenshotsCount > 0) {
        if (this.screenshotsCount > 1) {
          this.forceSubmitType = 6
          this.$refs.screenShotSubmitPopupTip.show()
        } else if (!isInit) {
          this.$refs.popupTip.show()
        }
      }
    },
    getCache(name) {
      const userId = this.$route.params.userID;
      const str = window.localStorage.getItem(name);
      let obj = str ? JSON.parse(str) : {};
      return { obj, userId }
    },
    setLocalExamRecord(data) {
      let { obj, userId } = this.getCache("record_cache")
      let arr = obj[userId] ? obj[userId] : [];
      const index = arr.findIndex((it) => it.examUserId == data.examUserId);
      if (index != -1) {
        arr[index] = data;
      } else {
        arr.unshift(data);
      }
      obj[userId] = arr;
      window.localStorage.setItem("record_cache", JSON.stringify(obj));
    },
    setLocalExamBehavior(data) {
      let { obj, userId } = this.getCache("behavior_cache")
      let arr = obj[userId] ? obj[userId] : {};
      if (arr[data.examUserId]) {
        arr[data.examUserId].push(data)
      } else {
        arr[data.examUserId] = [data]
      }
      obj[userId] = arr;
      window.localStorage.setItem("behavior_cache", JSON.stringify(obj));
    },
    sendLocalData(examUserId, isAll) {
      const getCacheData = (name) => {
        const { obj, userId } = this.getCache(name)
        let content = []
        if (name === 'behavior_cache') {
          const arr = obj[userId] ? obj[userId] : {};
          content = arr[examUserId] || []
        } else {
          const arr = obj[userId] ? obj[userId] : [];
          content = arr.filter(it => it.examUserId === examUserId)
        }
        return {
          examUserId: examUserId,
          cacheName: name,
          content: content
        }
      }
      let data = []
      data.push(getCacheData("behavior_cache"))
      if (isAll) {
        data.push(getCacheData("record_cache"))
      }
      saveLocalCache(data)
    },
    handlerError(data) {
      // 交卷失败答题记录也会存本地
      let catchData = data
      catchData.cacheTime = new Date().getTime();
      catchData.examTitle = this.paper.title;
      // 失败类型： 交卷失败
      catchData.type = 1;
      this.setLocalExamRecord(catchData);
      this.sendLocalData(catchData.examUserID, true);
    },
    // 初始化mounted校验
    initData() {
      this.refresh().then((data) => {
        // 弹出验证码 
        this.currentHandleVerify = 1
        if (data && data == 'needVerify') return this.createCode()
        if (this.examInfo.status !== 0) return
        // 渲染公式
        this.renderFormula()
        if (this.openScreenException) {
          startScreenAnalysis({ remainingTime: this.examInfo.examTime, examUserId: this.examInfo.examUserID })
          setTimeout(() => {
            analyze({ id: new Date().getTime(), timestamp: new Date().getTime(), isDelay: true, ignoreCD: true })
          }, 5 * 1000);
        }
        if (this.name === 'exam') {
          // 自动保存记录
          this.autoSaveStuAnswer()
          // 每300s 自动保存(现改为60s)
          const timer = setInterval(() => {
            this.autoSaveStuAnswer()
          }, 60000)
          // 首次进入界面，就弹框提示
          const { examUserID } = this.examInfo
          let timer2
          if (this.totalCount > 0 || 1) {
            const { count } = this.getLeftInfo(examUserID)
            // 从本地存储里获取上次离开时间
            let time = window.localStorage.getItem(this.examInfo.examUserID + 'time')
            if (time) {
              this.saveLeftInfo(count, new Date(time * 1))
              this.isPageActive = false
              this.judgeUnusualLeave()
            }
            // 弹出离开提示
            if (this.isNew === false && count > 0) {
              this.showHint()
            }
            // 没有开启考试离开时长监控并且不是新考试，标记一次进入页面
            // 时间延迟一下
            if (!this.isNew) {
              setTimeout(() => {
                this.behavior('进入页面')
              }, 100)
            }

            // 检测app考试进入后台
            let leaveTimer = () => {
              window.localStorage.setItem(this.examInfo.examUserID + 'leaveTime', new Date().getTime())
              if (!this.isPageActive) {
                this.judgeUnusualLeave()
              }
              if (!document[this.hiddenProperty] && !this.iOSCallState && this.iosIsActive && !this.iosSelectDocument) {
                this.saveLeftInfo(this.getLeftInfo().count, new Date())
                if (time) {
                  this.isPageActive = true
                  time = null
                }
                if (!this.isPageActive) {
                  this.isPageActive = true
                  this.clearLimitLeftTime()
                  this.leaveMaxTime = 5
                  if (this.openScreenException) {
                    setCanAnalyze(true)
                  }
                  this.behavior('进入页面')
                }
              } else {
                if (this.isPageActive) {
                  this.isPageActive = false
                  if (this.cameraOpen) {
                    if (this.openScreenException) {
                      setCanAnalyze(false)
                    }
                    this.behavior('上传附件')
                  } else {
                    if (this.openScreenException) {
                      analyze({ id: new Date().getTime(), timestamp: new Date().getTime() })
                    }
                    this.behavior('离开页面')
                  }
                }
              }
            }
            timer2 = setInterval(leaveTimer, 1000)
            this._onVisibilityChange()
          }
          let initExamAddTime = this.examInfo.examAddTime
          const timer3 = setInterval(() => {
            if (getPlatform().isAndroid || getPlatform().isHarmony) {
              // 多屏算一次离开
              multiWindowMode((res) => {
                if (res == 1) {
                  this.saveLeftInfo(this.getLeftInfo().count + 1)
                  this.saveExamLimitCount(this.formatTime(new Date(this.getLeftInfo().time)))
                  this.clearLeftTime()
                  this.showHint()
                }
              }, (error) => {
                console.error(error)
              })
            }
            let query = {
              examRelationId: this.examInfo.examRelationId,
              examUserId: this.examInfo.examUserID,
              costTime: this.examInfo.costTime + Math.round((new Date() - this.examInfo.startTime) / 1000),
              examUserAddTime: this.examInfo.examUserAddTime
            }
            getEndTimeNew(query).then(({ data }) => {
              // addTime 场次截止时间累计延长时间（分钟），场次截止是最新的，可以弃用
              // examAddTime 考试时长累计延长时间（分钟），未进入和已结束的考生不计入
              // examUserAddTime 单个考生可以延长的时间（为了减去初次进入考试之前延长的时间）
              // currentTime 服务器当前时间
              const { addTime, examAddTime, examRelationEndTime, examRelationId, currentTime } = data.result
              if (typeof addTime === 'number' && typeof examAddTime === 'number') {
                this.examInfo.examUserAddTime = this.examInfo.examUserAddTime + examAddTime - this.examInfo.examAddTime
                this.examInfo.examAddTime = examAddTime
                this.examInfo.endTime = examRelationEndTime
                let leftTime = Math.round((this.examInfo.endTime - this.examInfo.currentTime) / 1000);
                let currentCostTime = Math.round(((currentTime || new Date()) - this.examInfo.currentTime) / 1000)
                const availableDuration = Math.min(leftTime, this.examInfo.examSettingTime * 60 + this.examInfo.examUserAddTime * 60 - (this.examInfo.initCostTime || 0))
                this.examInfo.examTime = Math.max(availableDuration - currentCostTime, 0);
                if (examRelationId) {
                  this.examInfo.examRelationId = examRelationId
                }
              }
              this.netWorkOptimization = 0
              this.$refs.headerWarn.style.display = 'none'
              this.changeheaderWarn(false)
              if (data.result.forSubmitFlag) {
                // 教师提前收卷
                this.teacherForceSubmit()
              } else if (this.$refs.myPopup2.isVisible && this.isSystemSubmit) {
                // 网络恢复自动收卷
                this.reSubmitPaper()
              }
            }).catch((error) => {
              this.netWorkOptimization++
              this.$refs.headerWarn.style.display = 'flex'
              this.changeheaderWarn(true)
              if (this.netWorkOptimization == 3 && !this.$refs.myPopup2.isVisible && !this.isSystemSubmit) {
                this.$refs.myPopup1.show()
              }
            })
          }, 10 * 1000)
          this.$once('hook:beforeDestroy', () => {
            // 清理工作
            clearInterval(timer)
            clearInterval(timer2)
            clearInterval(timer3)
            clearTimeout(this.photoTimer)
            clearTimeout(this.snapTimer)
            this._offVisibilityChange()
            changeScreenRecordStatus(false)
            window.screenRecord = null
            window.screenShot = null
            forbidScreenRecord(0)
            forbidBackWithGesture(0)
          })
        } else {
          // 答题记录页面也禁止左右滑动
          forbidBackWithGesture(1)
          this.$once('hook:beforeDestroy', () => {
            forbidBackWithGesture(0)
          })
        }
      })
    },
    closeViewUploadType() {
      this.$refs.viewUploadType.hide()
    },
    // 计算器工具箱移动是否停止滑动
    canSwiperSlide(val) {
      this.$refs.slide.canSwiperSlide(!val)
    },
    clickUtilBox() {
      this.$refs.calculatorPopup.show()
    },
    calculatorHide() {
      this.$refs.calculatorPopup.hide()
    },
    calculatorDestroy() {
      this.$refs.calculator.clear()
      this.$refs.calculator.updateFontsize()
      this.$refs.calculatorPopup.hide()
    },
    // 屏幕监控服务相关回调
    handleCheckScreen(res) {
      // ● resultCode：录屏权限请求结果码
      //   ● 0：获取权限成功，启动录屏服务
      //   ● 1：获取权限失败
      //   ● 2：录屏权限被中断
      //   ● 3：分析成功
      //   ● 4：分析失败，设置了不能检测
      //   ● 5：分析失败，屏幕截图异常
      //   ● 6：分析失败，超过最大上传次数
      // ● resultId：客户端自增的 ID
      // ● id：analyze 方法中前端自定义的回调 ID（如果是自动检测则为 -1）
      // ● similarity：相似度结果（建议将检测结果记录入数据库，便于后续问题分析）
      // ● imageUrl：屏幕截图的图片地址
      // ● abnormal：true 表示异常，false 表示正常
      let tempRes = {}
      try {
        tempRes = JSON.parse(res)
      } catch (error) {
        console.log(error, 'error');
      }

      console.log(tempRes, 'tempRes.id');
      // ios自动分析回调的id包含了'ios' 这时候要替换一下
      if (tempRes.id == -1 || (typeof tempRes.id === 'string' && tempRes.id.indexOf("ios") != -1)) {
        tempRes.id = new Date().getTime()
      }
      // 交卷时的id校验回调，校验完成才能交卷
      if (tempRes.id == this.submitAnalysisId) {
        this.submitAnalysisId = null
      }
      switch (tempRes.resultCode) {
        case 0:
          // ios中断录屏权限的时候,授权成功的情况,关闭去授权弹窗
          if (this.$refs.myPopup6.isVisible) {
            this.$refs.myPopup6.hide()
          }
          startScreenAnalysis({ remainingTime: this.examInfo.examTime, examUserId: this.examInfo.examUserID })
          break;
        case 1:
        case 2:
          setTimeout(() => {
            if (this.name === 'exam') {
              // 安卓中断或者取消服务时重新开启服务，ios存在没有回调情况，出现授权弹窗
              if (getPlatform().isAndroid || getPlatform().isHarmony) {
                startScreenCaptureService()
              } else {
                this.$refs.myPopup6.show()
              }
            }
          }, 300);
          break
        case 3:
          if (tempRes.abnormal) {
            // 交卷之后不出现相关异常弹窗
            if (!this.stopSubmit) {
              // 屏幕异常出现验证码
              this.examInfo.screenExceptionVerify > 0 ? this.screenCodePopupShow(tempRes)
                // 屏幕异常出现提醒至强制交卷
                : this.getScreenExceptions(tempRes)
            } else {
              this.behavior("屏幕监控异常", false, tempRes.id, tempRes.imageUrl)
            }
          } else {
            this.behavior("屏幕监控正常", false, tempRes.id, tempRes.imageUrl)
          }
          break
        default:
          break;
      }
    },
    // 屏幕监控提醒popup
    screenExceptionPopupShow1() {
      this.$refs.myPopup4.show()
    },
    screenExceptionPopupHide1() {
      this.$refs.myPopup4.hide()
    },
    // 屏幕监控强制交卷popup
    screenExceptionPopupShow2() {
      this.$refs.screenMonitorPopupTip.show()
    },
    // 屏幕监控验证码提醒
    screenCodePopupShow(tempRes) {
      this.behavior("屏幕监控异常", false, tempRes.id, tempRes.imageUrl)
      this.$refs.myPopup5.show()
    },
    screenCodePopupHide() {
      this.$refs.myPopup5.hide()
    },
    screenCodePopupConfirm() {
      this.screenCodePopupHide()
      this.submitStuAnswer(this.$t('submitting'))
    },
    // 屏幕监控验证码
    showScreenCode() {
      this.$refs.screenMonitorCodePopup.show()
    },
    closeScreenCode() {
      this.$refs.screenMonitorCodePopup.hide()
    },
    // 拿屏幕已经异常的次数
    async getScreenExceptions(tempRes) {
      const { data } = await getScreenExceptionsApi({ examUserId: this.examInfo.examUserID })
      this.curScreenExceptionTimes = data.result + 1 || 0
      this.behavior("屏幕监控异常", false, tempRes.id, tempRes.imageUrl)
    },
    // 客户端开启服务方法
    startScreenCaptureService
  },
  mounted() {
    // 如果是在app环境 初始化安全键盘
    if (navigator.userAgent.includes("umoocApp")) {
      console.log('初始化安全键盘');
      keysInit()
    }


    window.exam = this
    // 录屏服务监听
    window.onScreenCaptureServiceResult = (res) => {
      this.handleCheckScreen(res)
    }
    this.iOSCallState = false
    window.iOSCallStateIncoming = () => {
      // 来电话了
      this.iOSCallState = true
    }
    window.iOSCallStateDisconnected = () => {
      // 挂断了
      this.iOSCallState = false
    }
    window.focusLose = (status) => {
      this.iOSCallState = !status
    }

    // 下拉系统通知栏或上拉工具栏
    this.iosIsActive = 1
    window.appChangeActive = (status) => {
      this.iosIsActive = status
    }

    Bus.$on('camera-change', (data) => {
      if (data.type == 'ios') {
        // ios选择文档单独判断
        this.cameraOpen = data.value
        this.iosSelectDocument = data.value
      } else {
        this.cameraOpen = data
      }
      if (data) {
        this.leaveMaxTime = 60
      }
    })

    // 微信公众号监听返回键
    if ((platform.isAndroid || platform.isHarmony) && getUrlParam('mp') === '1' && this.name === 'exam') {
      window.history.pushState(null, null, window.location.href);
      window.addEventListener("popstate", this.backByWx, false)
    }

    if (this.name === 'exam') {
      try {
        if (platform.platform === 'android') {
          forbidScreenRecord(1)
        } else if (platform.platform === 'ios') {
          getScreenRecordState((status) => {
            changeScreenRecordStatus(status === 1)
          })
          this.getScreenshotsCount()
          let screenshotTime = 0
          window.screenShot = () => {
            // 间隔2s才算一次
            const time = new Date().getTime()
            if (!(screenshotTime && time - screenshotTime <= 2000) && !this.isSubmitted) {
              screenshotTime = time
              this.screenshotsCount++
              this.behavior('截图')
              this.setScreenshotsCount()
            }
          }
        }
        forbidBackWithGesture(1)
      } catch (e) {
        console.log(e)
      }
      window.screenRecord = (status) => {
        changeScreenRecordStatus(status === 1)
      }
    }
    this.initData()
    Bus.$on('view-upload-type', () => {
      this.$refs.viewUploadType.show()
    })
    Bus.$on('text-input-click', () => {
      this.calculatorHide()
    })
    Bus.$on('video-record-open', (status) => {
      if (this.openScreenException) {
        setCanAnalyze(status)
      }
    })
  },
  destroyed() {
    if ((platform.isAndroid || platform.isHarmony) && getUrlParam('mp') === '1' && this.name === 'exam') {
      window.removeEventListener("popstate", this.backByWx, false)
    }
    const imgElements = document.querySelectorAll(".has-img img")
    imgElements.forEach((element) => {
      element.removeEventListener('click', this.showImage, false)
    })
    window.traceId = this.user.userID
    Bus.$off('view-upload-type')
    Bus.$off('text-input-click')
    Bus.$off('video-record-open')
    clearInterval(this.timer4)
  },
}
</script>

<style lang="stylus" module>
.photoMonitor
  font-size 14px
  img
    height 18px
    margin-right 4px
  .watching,.snapping
    display flex
    align-items center
    span
      color #5abc8c
  .snapping
    span
      color #444
.snapPhoto
  position absolute
  right 0
  padding 8px
  z-index 99999
  img
    height 74px
.popup
  position absolute
  width 100%
  // top 44px
  top 0
  margin-top 44px
  bottom 0
  z-index 10
strong
  color #f60000
:global(.wrapios) .popup
  // top 64px
  margin-top calc(constant(safe-area-inset-top) + 45px)
  margin-top calc(env(safe-area-inset-top) + 45px)

.popupMask
  position absolute
  width 100%
  height 100%
  background-color rgba(0, 0, 0, 0.4)
  z-index 8

.popupContainer
  position absolute
  width 100%
  height 100%
  z-index 9
  pointer-events none

.snPanel
  max-height 70%
  pointer-events auto
  height auto

:global(.subanstip)
  display block
  padding 10px 20px

.headerWarn
  display: none;
  align-items: center;
  height: 44px;
  font-size: 16px;
  color: #EE7B6A;
  background-color: #FFF7EA;
  padding-left: 20px;
  i
    color: #F7B13D;
    font-size: 22px;
    margin-right: 8px;

.myPopup1
  width: 74%;
  margin: 0 auto;
  border-radius: 5px;
  padding: 20px;
  background-color: white;
  .title
    text-align: center;
    margin-bottom: 15px;
  .content
    margin-bottom: 10px;
    line-height: 24px;
  button
    display: block;
    width: 90%;
    color: #EA604F;
    margin: 0 auto;
    margin-top: 15px;

.myPopup2
  width: 74%;
  margin: 0 auto;
  border-radius: 5px;
  padding: 20px;
  background-color: white;
  .title
    position: relative;
    text-align: center;
    margin-bottom: 15px;
    .iconClose
      position: absolute;
      right: 0;
      font-size: 20px;
      font-weight: 700;
  .content
    margin-bottom: 10px;
    line-height: 24px;
  button
    display: block;
    width: 90%;
    color: #EA604F;
    margin: 0 auto;
    margin-top: 15px;
.myPopup3
  width 100%;
  margin: 0 auto;
  padding-bottom: 20px;
  background-color: white;
  .title
    position: relative;
    text-align: left;
    padding: 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    .iconClose
      position: absolute;
      right: 20px;
      font-size: 20px;
      font-weight: 700;
  .content
    font-size: 15px;
    border-top: 1px #b9b9bf solid;
    padding: 16px 16px 0;
    margin-bottom: 10px;
    line-height: 22px;
.myPopup4 
  width: 74%;
  margin: 0 auto;
  border-radius: 5px;
  padding: 20px 0;
  background-color: white;
  .title
    font-size: 18px;
    font-weight 500
    text-align center
    margin-bottom: 10px;
  .content
    margin-bottom: 10px;
    line-height: 24px;
    padding: 0 20px;
  .btnwrap
    display: flex;
  .btnSubmit 
    flex 1
    text-align center
    font-size 16px
    color #444444
    font-weight 500
    margin-top 16px
    padding-top 16px
    border-top 1px solid #E3E3E9
  .btnByCode 
    flex 1
    text-align center
    font-size 16px
    color #EA604F
    font-weight 500
    margin-top 16px
    padding-top 16px
    border-top 1px solid #E3E3E9
.red
  color #f60000
.calculatorPopup
  background-color: #F1F3F7;
  .title
    display: flex;
    padding: 15px 16px 8px;
    justify-content: space-between;
    .btns 
      display: flex;
      align-items: center;
      >span
        display: inline-block;
        transform: rotate(180deg) !important;
      >span:last-child 
        margin: 0 8px 0 16px
      >span:first-child
        font-size 20px
  .content
    padding: 0 16px 10px;
</style>
