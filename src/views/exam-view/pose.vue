<template>
  <div :class="$style.cameraMonitorWrap">
    <div
      :class="[$style.cameraMonitor, status === 6 ? '' : $style.error]"
      @click="showCameraMonitor"
    >
      <i class="iconfont icon-zhuapaijiankong"></i>
      <span>{{ status === 6 ? $t('normal') : $t('exception')}}</span>
    </div>
    <Posenet @poseResult="getPoseResult"/>
  </div>
</template>
<script>
import Posenet from '@/components/posenet';
import { cameraStream, posenetResult, showCameraPreview, videoRecord, uploadPosenet } from '@/lib/cordovaPlugin/mobilePlugin';
import faceApi from '@/api/face';
const intervalTime = 60 * 1000
export default {
  components: {
    Posenet
  },
  props: ['examUserId', 'userId', 'examId'],
  data() {
    return {
      status: 6, //状态正常: 6, 离开座位: 7,  视线偏离: 8,   交头接耳: 9
      imageData: '',
      checkTime: 0, // 检测时间
      hasExp: false,
      departureStartTime: 0,
      leaveStartTime: 0
    }
  },
  watch: {
    status: {
      handler(newVal) {
        this.departureStartTime = 0
        this.leaveStartTime = 0
        switch(newVal) {
          case 7: 
            this.leaveStartTime = new Date().getTime(); 
            break;
          case 8: 
            this.departureStartTime = new Date().getTime(); 
            break;
          case 9: 
            this.hasExp = true; 
            this.saveImg();
            break;
          default:;
        }
      }
    }
  },
  created() {
    cameraStream({
      isOpenCamera: 2,
      intervalTime: 200
    },() => {
      this.initRecordTimer()
    })
  },
  mounted() {
    window.posenetVideo = (fileName, filePath) => {
      if (filePath) {
        // fileName为开始录制的时间，视频上传的时间则需加上定时时间60000
        const createTime = Number(fileName.substring(fileName.lastIndexOf("_") + 1, fileName.length)) + intervalTime
        this.$emit('saveBehavior', {
          type: '监控视频',
          createTime: createTime,
          url: filePath
        })
      }
    }
  },
  methods: {
    showCameraMonitor() {
      setTimeout(() => {
        showCameraPreview(1)
      }, 200)
    },
    getPoseResult(data) {
      const { status, result, imageData } = data
      this.imageData = imageData
      this.checkTime = new Date().getTime()
      this.status = status
      const checkIsUpload = (tiemNum = 2000) => {
        const timeName = this.status === 7 ? 'leaveStartTime' : 'departureStartTime'
        if (this[timeName] && new Date().getTime() - this[timeName] > tiemNum) {
          this.hasExp = true
          this[timeName] = 0
          this.saveImg()
        }
      }
      if (this.status === 7 || this.status === 8) {
        checkIsUpload()
      }
      posenetResult({
        result: result,
        status: status === 6
      })
    },
    initRecordTimer() {
      let fileName = `${this.userId}_${this.examId}_${new Date().getTime()}`
      let params = {
        isStart: 1,
        videoName: fileName,
        examId: this.examId
      }
      // 开启录制
      videoRecord(params)       
      this.hasExp = false
      const timer = setInterval(() => {
        // 结束录制
        this.saveImg()
        videoRecord({
          isStart: 0, 
          videoName: fileName,
          isUpload: this.hasExp || 0,
          examId: this.examId
        })
        setTimeout(() => {
          // 为了和异常照片的时间线对应，开始录制的时间减去延迟的时长
          fileName = `${this.userId}_${this.examId}_${new Date().getTime()-3000}`
          params.videoName = fileName
          // 开启录制
          videoRecord(params) 
          this.hasExp = false
        }, 3000)
      }, intervalTime)
      this.$once('hook:beforeDestroy', () => {
        // 清理工作
        clearInterval(timer)
        // 结束录制
        videoRecord({
          isStart: 0, 
          videoName: fileName,
          isUpload: this.hasExp || 0,
          examId: this.examId
        })
        // 销毁视频视图
        showCameraPreview(0)
        // 关闭摄像头
        cameraStream({
          isOpenCamera: 0
        })
        window.posenetImage = null
        window.posenetVideo = null
      })
    },
    saveImg() {
      const { status, imageData, checkTime } = this
      // 正常抓拍的照片异常需要上传异常视频
      if (!this.hasExp) {
        this.hasExp = status !== 6
      }
      if (imageData) {
        uploadPosenet({
          fileName: `${this.userId}_${this.examId}_${checkTime}`,
          filePath: imageData
        }, (res) => {
          if (res) {
            faceApi.saveSnap({
              alarmType: status,
              createTime: checkTime,
              examUserId: this.examUserId,
              url: res
            })
          }
        })
      }
    }
  },
}
</script>
<style lang="stylus" module>
.cameraMonitor {
  position: absolute;
  right: 16px;
  z-index: 8;
  margin-top: 7px;
  padding: 2px 14px;
  font-size: 14px;
  color: #32b593;
  border: 1px solid #32b593;
  border-radius: 20px;
  cursor: pointer;
  background-color: #ffffff;

  i {
    margin-right: 4px;
    color: #32b593;
  }

  &.error {
    color: #f59a23;
    border: 1px solid #f59a23;

    i {
      color: #f59a23;
    }
  }
}
</style>