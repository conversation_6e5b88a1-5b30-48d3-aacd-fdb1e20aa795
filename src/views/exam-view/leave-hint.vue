<script>
import { Popup } from 'cube-ui'
import { Button } from '@/common/components'
// import { formatTimer, formatDate } from '@/common/utils'
import { formatDate } from '@/common/utils'
export default {
  // el: 'body',
  components: {
    Popup,
    UButton: Button
  },
  props: {
    time: {},
    leftTime: {},
    realLeftTime: null,
    totalCount: Number,
    count: Number,
    leaveLimitTime: Number,
    isTimeOver: Boolean
  },
  data () {
    return {
      seconds: 10
    }
  },
  computed: {
    // 到达消耗次数限制，将为用户强制提交
    disabled () {
      const { totalCount, count, isTimeOver } = this
      if (totalCount > 0 && !isTimeOver) {
        return count >= totalCount
      }
      return false
    },
    computedStartTime () {
      const { realLeftTime } = this
      if (!realLeftTime) {
        return ''
      } else {
        return formatDate(realLeftTime, 'yyyy-MM-dd HH:mm:ss')
      }
    },
    // 离开考试提示
    leaveExamTip () {
      const { realLeftTime } = this
      if (!realLeftTime) {
        // 没有记录时间，适用于强杀APP
        return this.$t('hintTip1')
      } else {
        const { computedStartTime } = this
        return this.$t('hintTip2', { time: computedStartTime })
      }
    },
    // 消耗次数提示
    countTip () {
      const { totalCount, count, disabled, isTimeOver, leaveLimitTime } = this
      if (isTimeOver) {
        return this.$t('hintTip5', { s: leaveLimitTime })
      } else {
        if (disabled) {
          return this.$t('hintTip3', { totalCount: totalCount })
        } else {
          return this.$t('hintTip4', { totalCount: totalCount, count: totalCount - count })
        }
      }
    },
    // 确认按钮文案
    confirmBtn () {
      const { disabled, seconds, isTimeOver } = this
      if (isTimeOver) {
        return this.$t('getIt')
      } else {
        if (disabled) {
          return this.$t('submitSeconds', { seconds: seconds })
        } else {
          return this.$t('continueExam2')
        }
      }
    }
  },
  watch: {
    disabled: {
      immediate: true,
      handler (newVal) {
        if (newVal === true) {
          this.timer = setInterval(() => {
            this.seconds--
            if (this.seconds <= 0) {
              clearInterval(this.timer)
              this.$emit('confirm')
              this.hide()
            }
          }, 1000)
        }
      }
    }
  },
  methods: {
    confirm () {
      this.hide()
      clearInterval(this.timer)
      if (this.isTimeOver) {
        this.$emit('confirmForLimitTime')
      } else if (this.disabled) {
        this.$emit('confirm')
      }
    },
    /**
     * 弹窗
     */
    show () {
      this.$refs.tipPopup.show()
    },
    hide () {
      this.$refs.tipPopup.hide()
    }
  },
  destroyed () {
    clearInterval(this.timer)
  }
}
</script>
<template>
  <Popup type="extend-tip-popup" ref="tipPopup" :positon="'center'">
    <div :class="$style.backtip">
      <h2 :class="$style.title">{{ $t('exitExamTip') }}</h2>
      <p :class="$style.tipInfo" v-html="leaveExamTip"></p>
      <p :class="$style.tipInfo">
        <strong>{{countTip}}</strong>
      </p>
      <UButton radius @click="confirm" primary>{{confirmBtn}}</UButton>
    </div>
  </Popup>
</template>
<style lang="stylus" module>
.backtip
  background-color #fff
  padding 0 16px 30px
  border-radius 4px
  margin 0 30px

  strong
    color #f00
    font-style normal
    font-weight normal

  /.title
    height 50px
    margin 0
    font-size $font-size-medium-x
    line-height 50px
    color $text-color-default
    font-weight bold
    text-align center
    border-bottom 1px solid $color-border-default

  /.tipInfo
    text-align left
    padding-top 16px
    line-height 1.5

  :global(.uButton)
    margin 0 auto
    margin-top 30px
    margin-bottom 19px

    &:last-child
      margin-bottom 0
</style>
