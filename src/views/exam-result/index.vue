<script>
import { getExamResult, logout } from "@/api";
import { formatDate } from "@/common/utils";
export default {
  data() {
    return {
      model: {
        name: "",
        studentId: "",
        rankDetailList: [],
      },
    };
  },
  created() {
    this.fetch();
  },
  computed: {
    user() {
      return this.$store.state.user;
    },
  },
  methods: {
    fetch() {
      getExamResult().then(({ data }) => {
        if (data.code === 1) {
          this.model = data.result;
        }
      });
    },
    logout() {
      logout()
        .then((res) => {
          this.exit();
        })
        .catch(() => {
          this.exit();
        });
    },
    exit() {
      this.$router.replace({
        name: "checkScore",
      });
    },
  },
  filters: {
    formatTime(time) {
      if (!time) {
        return "--";
      }
      return formatDate(time, "yyyy-MM-dd HH:mm");
    },
  },
};
</script>

<template>
  <div class="result-page">
    <div class="page-head">
      <span class="text">福建开放大学党史知识竞赛</span>
      <span class="text">成绩查询</span>
    </div>
    <div class="page-content">
      <div class="info">
        <div class="user-info">
          <p class="name">{{ model.name || user.name }}</p>
          <p class="studentId">{{ model.studentId || user.studentID }}</p>
        </div>
        <div class="total-result">
          <div class="total-score">
            <div v-if="model.finalGrade || model.finalGrade === 0">
              <span class="score">{{ model.finalGrade }}</span>
              <span class="unit">分</span>
            </div>
            <span class="noData" v-else>--</span>
            <span class="text">最终成绩</span>
          </div>
          <div class="total-rank">
            <div v-if="model.finalRank">
              <span class="score">{{ model.finalRank }}</span>
              <span class="unit">名</span>
            </div>
            <span class="noData" v-else>--</span>
            <span class="text">最终排名</span>
          </div>
        </div>
      </div>
      <div
        class="result-list"
        v-if="model.rankDetailList && model.rankDetailList.length > 0"
      >
        <div class="table">
          <div class="th">
            <div class="td sort">场次</div>
            <div class="td time">时间</div>
            <div class="td score">成绩</div>
            <div class="td rank">排名</div>
          </div>
          <div
            class="tr"
            v-for="(item, index) in model.rankDetailList"
            :key="'item' + index"
          >
            <div class="td sort">{{ item.num }}</div>
            <div class="td time">
              <span class="start-time"
                >开始：{{ item.startTime | formatTime }}</span
              >
              <span class="end-time"
                >结束：{{ item.endTime | formatTime }}</span
              >
            </div>
            <div class="td score" v-if="item.status === 2">
              {{ item.grade || 0 }}
            </div>
            <div class="td rank" v-if="item.status === 2">
              {{ item.rank || "--" }}
            </div>
            <div class="td status living" v-if="item.status === 1">
              考试进行中
            </div>
            <div class="td status not-start" v-if="item.status === 0">
              考试未开始
            </div>
          </div>
        </div>
      </div>
      <button type="button" class="button" @click="logout">退出</button>
      <div class="desc">
        <p>特别提示：</p>
        <p>
          <span class="dian"></span>
          <span class="desc-detail"
            >单场考试成绩相同时，按答题时长排序，答题时间用时短的排名靠前</span
          >
        </p>
        <p>
          <span class="dian"></span>
          <span class="desc-detail"
            >最终成绩取三次考试最高分，成绩相同时，按答题时长排序，答题用时短的排名靠前</span
          >
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="stylus">
.result-page {
  background-color: #F5F5F5;

  .page-head {
    width: 100%;
    padding: 18px 10px 72px;
    background: #fc793b url('~@/assets/images/check_bj.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .text {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: #FFFFFF;
      text-align: center;
      line-height: 30px;
      text-shadow: 0 1px 2px rgba(172, 0, 0, 0.43);
    }
  }

  .page-content {
    position: relative;
    width: 100%;
    padding: 125px 16px 30px;

    .info {
      position: absolute;
      top: -54px;
      left: 16px;
      width: calc(100% - 32px);
      padding: 16px 20px;
      background: #FFFFFF;
      border-radius: 4px;

      .user-info {
        margin-bottom: 12px;
        padding-bottom: 12px;
        font-weight: bold;
        text-align: center;
        line-height: 24px;
        border-bottom: 1px dashed #E3E3E9;

        .name {
          margin-bottom: 4px;
          font-size: 22px;
          color: #444444;
        }

        .studentId {
          font-size: 16px;
          color: #888888;
        }
      }

      .total-result {
        display: flex;
        align-items: center;

        .total-score, .total-rank {
          flex: 1;
          font-weight: bold;
          text-align: center;
          color: #888888;

          .score, .unit {
            font-weight: bold;
            font-size: 28px;
            color: #FE5718;
            line-height: 1;
          }

          .unit {
            font-size: 12px;
          }

          .noData, .text {
            display: block;
          }

          .noData {
            font-size: 28px;
            line-height: 28px;
          }

          .text {
            font-size: 14px;
            line-height: 22px;
          }
        }
      }
    }

    .result-list {
      margin-bottom: 20px;

      .table {
        .th {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          background-color: #F0F0F3;

          .td {
            font-size: 14px;
            color: #444444;
            line-height: 20px;
          }
        }

        .td {
          padding-right: 12px;
          box-sizing: content-box;

          &:last-child {
            padding-right: 0;
          }

          &.sort {
            width: 36px;
          }

          &.time {
            width: 60%;
          }

          &.score {
            width: 15%;
          }

          &.rank {
            width: calc(25% - 28px);
          }
        }

        .tr {
          display: flex;
          align-items: center;
          padding: 10px 12px;
          background-color: #fff;
          border-bottom: 1px solid #E3E3E9;

          &:last-child {
            border-bottom: 0;
          }

          .td {
            font-size: 12px;
            color: #444444;
            line-height: 20px;

            .start-time, .end-time {
              display: block;
            }

            &.status {
              width: calc(30% + 12px);
              height: 28px;
              border-radius: 14px;
              font-size: 12px;
              text-align: center;
              line-height: 28px;

              &.living {
                color: #FE5718;
                background-color: rgba(254, 87, 24, 0.08);
              }

              &.not-start {
                color: #888888;
                background-color: #F5F5F5;
              }
            }
          }
        }
      }
    }

    .button {
      margin-top: 16px;
      width: 100%;
      height: 44px;
      font-size: 16px;
      background-color: #FE5718;
      border-radius: 6px;
    }

    .desc {
      margin-top: 24px;
      font-size: 14px;
      color: #969696;
      line-height: 26px;

      .dian {
        display: inline-block;
        margin: 12px 6px 12px 0;
        width: 4px;
        height: 4px;
        background-color: #969696;
        border-radius: 50%;
        vertical-align: top;
      }

      .desc-detail {
        display: inline-block;
        max-width: calc(100% - 10px);
      }
    }
  }
}
</style>
