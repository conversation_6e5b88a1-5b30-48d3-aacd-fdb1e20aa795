<script>
import { Scroll } from 'cube-ui'
import Item from './item'
// 获取试卷列表
import { getExamList } from '@/api'
import { Page } from '@/common/components'
import { exitApp, updateActiveList } from '@/lib/cordovaPlugin/mobilePlugin.js'
import Loading from '@/common/components/loading'
import { mapState } from 'vuex'
export default {
  components: {
    Page,
    Scroll,
    Item,
    Loading
  },
  data () {
    return {
      status: 'loading',
      data: [],
      initPage: 1,
      // 表示列表还没有获取完毕
      continueToFetch: true,
      // Scroll 配置项
      options: {
        pullDownRefresh: {
          threshold: 90,
          stop: 40,
          txt: this.$t('refreshSuccess')
        },
        pullUpLoad: {
          threshold: 0
        },
        scrollBar: false
      }
    }
  },
  computed: {
    ...mapState(['entry']),
    userID () {
      return this.$route.query.userid
    },
    ocId () {
      return this.$route.query.courseid
    },
    user () {
      window.$store = this.$store
      // return this.$store.getters.getUser('user')
      return this.$store.state.user
    }
  },
  methods: {
    fetch () {
      const { userID, initPage, ocId } = this
      // 后端拼写错误, 应该是initPage
      return getExamList({ userID, ocId, intPage: initPage }).then(({ data }) => {
        const examArr = data.examArr || []
        this.status = 'success'
        this.options.pullDownRefresh.txt = this.$t('refreshSuccess')
        if (examArr.length < 10) {
          this.continueToFetch = false
        }
        if (initPage === 1) {
          this.data = examArr
        } else {
          if (this.check(examArr)) {
            this.continueToFetch = false
          } else {
            this.data = this.data.concat(examArr)
          }
        }
        if (this.continueToFetch === false) {
          this.$refs.scroll.forceUpdate()
        }
        this.initPage++
      }).catch(e => {
        this.status = 'fail'
        this.options.pullDownRefresh.txt = this.$t('refreshFailed')
        this.$refs.scroll.forceUpdate()
        this.$createToast({
          type: 'error',
          mask: true,
          txt: this.$t('netWorkError')
        }).show()
        throw e
      })
    },
    check (examArr) {
      const dataLength = this.data.length - 1
      const examArrLength = examArr.length - 1
      if (this.data[dataLength].examID === examArr[examArrLength].examID) {
        return true
      }
      return false
    },
    refresh () {
      this.status = 'loading'
      this.initPage = 1
      this.fetch()
    },
    onPullingDown () {
      this.initPage = 1
      this.continueToFetch = true
      this.fetch()
    },
    onPullingUp () {
      if (!this.continueToFetch) {
        this.$refs.scroll.forceUpdate()
      } else {
        this.fetch()
      }
    },
    back () {
      if (this.entry === 1) {
        updateActiveList()
      }
      exitApp()
      return false
    }
  },
  mounted () {
    this.refresh()
    window.test = this
  }
}
</script>

<template>
  <Page :title="$t('mobileExam')" :class="$style.page_list"  @back="back">
    <template v-slot:menu v-if="user.faceExam ==1 && user.faceExamExpireTime > new Date()">
      <router-link :to="`/face/${userID}/0/authentication`"><span>{{ $t('faceAuth') }}</span></router-link>
    </template>
    <Scroll
      :class="$style.bg"
      :data="data"
      :options="options"
      ref="scroll"
      @pulling-down="onPullingDown"
      @pulling-up="onPullingUp"
    >
      <div :class="$style.container">
        <Loading v-if="status === 'loading'"></Loading>
        <div v-else-if="status === 'success' && data.length > 0">
          <ul :class="$style.list">
            <li v-for="item in data" :key="item.examID">
              <Item :item="item" :userID="userID"/>
            </li>
          </ul>
        </div>
        <p v-else :class="$style.fail">{{ $t('teaNoExam') }}</p>
      </div>
    </Scroll>
  </Page>
</template>

<style lang="stylus" module>
.page_list
  background url('@/assets/images/list_pic.png') no-repeat center bottom / contain, url('@/assets/images/list_bj.png') no-repeat 0 0 / cover
.container
  /.fail
    position absolute
    top 50%
    left 50%
    transform translate(-50%, -50%)
    font-size $font-size-medium
    color $text-color-default

.list
  padding 20px 20px 55px 20px

  &>li
    margin-bottom 8px

    &:last-child
      margin-bottom 0
</style>
