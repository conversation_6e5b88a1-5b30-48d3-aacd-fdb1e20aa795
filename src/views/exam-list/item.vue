<script>
import { repayExam } from '@/api'
import { formatDate, getPlatform } from '@/common/utils'
import faceApi from '@/api/face'
/**
 * 参与方式  1-电脑网页  2-手机APP 3-不限  4-电脑锁屏客户端  5 -(电脑网页和手机APP) 6-(电脑网页和锁屏客户端) 7-(手机APP和锁屏客户端)
 */
const examTypeMap = {
  1: [1],
  2: [2],
  3: [2, 1, 4],
  4: [4],
  5: [2, 1],
  6: [1, 4],
  7: [2, 4],
  8: [5],
  9: [2, 5],
  10: [1, 5],
  11: [4, 5],
  12: [1, 2, 5],
  13: [2, 4, 5],
  14: [1, 4, 5],
  15: [1, 2, 4, 5],
} 
export default {
  props: ['item', 'userID'],
  computed: {
    examTypeText() {
      if (examTypeMap[this.item.examType].length > 0) {
        return this.$t(`examType${this.item.examType}`)
      }
      return ''
    },
    examTypeTextTip() {
      let arr = examTypeMap[this.item.examType]
      // 5是 H5考试的类型 但是examType8才是h5的提醒
      arr = arr.map(item => item === 5 ? item = 8: item)
      return arr.map(it => this.$t(`examType${it}`)).join('/')
    }
  },
  methods: {
    formatStartDate (d) {
      return formatDate(d, 'yyyy-MM-dd HH:mm')
    },

    formatEndDate (d) {
      return formatDate(d, 'MM-dd HH:mm')
    },

    // 交卷
    submitAnswer () {
      return repayExam({ examUserID: this.item.examUserID }).then(({ data }) => {
        const { status } = data
        if (status === 1) {
          // 成功
          this.saveBehavior()
          // this.$emit('refresh')
          this.gotoExamReport()
          // return getExamStatus(this.getQuery()).then(({ data }) => {
          //   const { hasRecord, status } = data
          //   this.$emit('updateStatus', hasRecord, status)
          // }).catch(() => {
          //   // throw error
          // })
        } else if (status === 2) {
          // 没有答题记录
          this._errorToast(this.$t('noAnswerRecord')).show()
        } else if (status === 0) {
          // 评分失败
          this._errorToast(this.$t('scoreFailed')).show()
        }
      }).catch(error => {
        this._errorToast(this.$t('submitFailed') + error).show()
      })
    },
    saveBehavior () {
      let data = {
        examId: this.item.examID,
        examUserId: this.item.examUserID,
        behaviorType: 4,
        terminal: 2,
        isForceSubmit: 8,
        deviceInfo: getPlatform().deviceInfo,
        extra: ''
      }
      faceApi.saveBehavior(data)
    },
    // 根据考试情况，进行不同的处理
    handleExam () {
      const { examType, hasRecord, status } = this.item
      // 调用过startExam hasRecord 即为true
      if (!this.checkExamType(2)) {
        // 电脑考试
        this._alertDialog({ confirmBtn: { text: this.$t('confirm') },content: '<p class="exam-pc-tip">' + this.$t('examTypeTip', {examType: this.examTypeTextTip}) + '</p>' }).show()
      } else if (hasRecord) {
        // 考试中断 请提交
        if (status === 1 || status === 3 || status === 4) {
        // 考试未开始1 考试已结束3 未参加4
        // 当有考试记录，并且未开始时: 表示有多个批次, 上一个批次结束了,但当前的批次为未开始
        // 4: 表示考试已经结束,并且答题次数为0
          this._alertDialog({
            content: '<p class="exam-recordtip">' + this.$t('examDetailTip1') + '</p>',
            confirmBtn: { text: this.$t('submitExam') },
            onConfirm: () => {
              this.submitAnswer()
            }
          }).show()
        } else if (status === 2) {
        // 正在进行 处于考试时间, 而不是用户进入过考试
          this._alertDialog({
            content: '<p class="exam-recordtip">' + this.$t('enterExamTip') + '</p>',
            confirmBtn: { text: this.$t('enterExam') },
            onConfirm: () => {
              this.gotoExamInfo()
            }
          }).show()
        }
      } else {
        if (status === 3 || status === 5) {
          // 未有中断的考试，且已经结束或正在进行，进入考试结果页面
          // 5 正在进行 (已参加)
          this.gotoExamReport()
        } else {
          // 进入考试详情页面
          this.gotoExamInfo()
        }
      }
    },
    getQuery () {
      return { userID: this.userID, examID: this.item.examID }
    },
    gotoExamInfo () {
      // this.$router.push({ name: 'info', params: this.getQuery() })
      goPage(this, { name: 'info', params: this.getQuery() })
    },
    gotoExamReport () {
      // this.$router.push({ name: 'report', params: this.getQuery() })
      goPage(this, { name: 'report', params: this.getQuery() })
    },
    _alertDialog (options) {
      return this.$createDialog({
        type: 'alert',
        confirmBtn: { text: this.$t('confirm'), active: true },
        maskClosable: true,
        ...options
      })
    },
    _errorToast (txt) {
      return this.$createToast({ type: 'error', time: 1000, mask: true, txt })
    },
    checkExamType(checktype) {
      return examTypeMap[this.item.examType].indexOf(checktype) !== -1
    }
  }
}
</script>
<template>
  <div :class="$style.container" @click="handleExam">
    <div :class="$style.tags">
      <span :class="[$style.tag, checkExamType(2) ? $style.tagPhone : $style.tagPC]">
        <i class="iconfont exam-list-icon icon-diannaoshouji"></i>{{ examTypeText }}
      </span>
    </div>
    <div :class="$style.head">
      <mark v-if="item.status ===2 ||item.status ===5" :class="[$style.mark, $style.markProcess]">{{ $t('ing') }}</mark>
      <mark v-if="item.status === 1" :class="[$style.mark, $style.markNotStart]">{{ $t('notStart') }}</mark>
      <mark v-if="item.status === 3||item.status ===4" :class="[$style.mark, $style.markEnd]">{{ $t('end') }}</mark>
      <span :class="$style.title">{{ item.title }}</span>
    </div>
    <div :class="$style.info">
      <div :class="$style.time">
        <p v-for="(relation, index) in item.relationArr" :key="index">
          <i class="iconfont exam-list-icon icon-time" :class="$style.dateIcon"></i>
          {{formatStartDate(relation.startDate)}} - {{formatEndDate(relation.endDate)}}
        </p>
      </div>
      <div :class="$style.remark" v-if="item.showGrade === 1">
        {{item.grade}}
        <span v-if="item.status === 4" :class="[$style.status, $style.statusGoing]">{{ $t('notJoined2') }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="stylus" module>
.container
  position relative
  padding 20px 16px 10px
  background-color #fff
  border-radius 8px
  /.tags
    position absolute
    right 0
    top 0
    color #fff
    font-size 12px
    height 16px
    line-height 16px
    text-align center
    /.tag
      border-top-right-radius 4px
      border-bottom-left-radius 8px
      padding 0 5px
    /.tagPhone
      background-color #fe6d9f
    /.tagPC
      background-color #e4e4f4
    i
      color #fff

.head
  padding-bottom 10px
  border-bottom 2px dashed $border-line-color2
  line-height 1.375
  word-wrap break-word
  overflow hidden
  text-overflow ellipsis
  display -webkit-box
  -webkit-line-clamp 2
  -webkit-box-orient vertical
  /.mark
    display inline-block
    border-radius 2px
    background-color #fff
    height 18px
    line-height 18px
    font-size 12px
    padding 0 2px
    margin-right 8px
    vertical-align text-top
  /.markProcess
    background-color #ff0000
    color #fff
  /.markNotStart
    background-color #ffcc00
    color #fff
  /.markEnd
    border 1px solid $border-line-color2
    color $text-color-intro
  /.title
    vertical-align middle
  /.info
    display flex
    align-items center
    padding-top 10px
    line-height 1
    /.time
      flex 1
      p
        font-size $font-size-small
        color $text-color-intro
        margin-bottom 4px
        &:last-child
          margin-bottom 0
      .dateIcon
        margin-right 4px
        font-size 12px
        color #e4e4f4
    /.remark
      font-size $font-size-medium
      color $text-color-intro

:global(.exam-list-icon) {
  margin-right 6px
  font-size 12px!important
}
</style>
