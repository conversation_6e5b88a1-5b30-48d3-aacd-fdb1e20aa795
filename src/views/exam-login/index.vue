<script>
import { Validator } from "cube-ui";
import { login } from "@/api";
import appsApi from "@/api/apps";
export default {
  components: {
    Validator,
  },
  data() {
    return {
      loginName: {
        value: "",
        valid: "",
        rules: {
          required: true,
        },
        messages: {
          required: "请输入账号",
        },
      },
      password: {
        value: "",
        valid: "",
        rules: {
          required: true,
        },
        messages: {
          required: "请输入密码",
        },
      },
      eye: {
        open: false,
        reverse: false,
      },
      error: false,
      isCheck: this.$route.name === "checkScore",
      orgId: this.$route.query.orgId
    };
  },
  computed: {
    title() {
      let str = "";
      if (this.orgId > 0) {
        switch (parseInt(this.orgId)) {
          case 1:
            str = "福建开放大学党史知识竞赛";
            break;
          case 1947:
            str = "供销社党史党务知识竞赛";
            break;
          default:
        }
      }
      return str;
    },
  },
  created() {
    document.title = this.$route.meta.title
  },
  methods: {
    login() {
      if (this.loginName.value && this.password.value) {
        login({
          loginName: this.loginName.value.trim(),
          password: this.password.value,
        })
          .then((res) => {
            if (res.data && res.data.roleId === 9) {
              window.Authorization = res.data.authorization;
              appsApi.getUserInfo(res.data.userId).then(({ data }) => {
                data.userId = data.userID;
                data.roleId = data.role;
                this.$store.commit("setUser", data);
                if (!this.isCheck) {
                  this.$router.replace({
                    name: "examList",
                    query: { userid: data.userId },
                  });
                } else {
                  this.$router.replace({
                    name: "result",
                  });
                }
              });
            } else {
              this.$createToast({
                type: "error",
                time: 1000,
                txt: this.$t(
                  `只有学生账号可以${this.isCheck ? "查询" : "登录"}`
                ),
              }).show();
            }
          })
          .catch((error) => {
            if (error && error.response && error.response.status === 400) {
              if (error.response.data.code === 1201) {
                this.error = true;
              }
            }
          });
      }
    },
  },
};
</script>

<template>
  <div class="login-page">
    <div class="page-head">
      <span class="text" v-if="title">{{ title }}</span>
      <span class="text">{{ isCheck ? "成绩查询" : "参赛入口" }}</span>
    </div>
    <div class="page-content">
      <div class="login-form">
        <div class="form-item">
          <Validator
            v-model="loginName.valid"
            :model="loginName.value"
            :rules="loginName.rules"
            :messages="loginName.messages"
            @focus="error = false"
            @input="error = false"
            ><cube-input
              v-model="loginName.value"
              :placeholder="'请使用参赛帐号' + (isCheck ? '查询' : '登录')"
              :clearable="{ visible: true, blurHidden: false }"
            >
              <div class="pre" slot="prepend">帐号</div>
            </cube-input></Validator
          >
        </div>
        <div class="form-item">
          <Validator
            v-model="password.valid"
            :model="password.value"
            :rules="password.rules"
            :messages="password.messages"
            ><cube-input
              v-model="password.value"
              placeholder="请输入密码"
              :type="'password'"
              :eye="eye"
              @focus="error = false"
            >
              <div class="pre" slot="prepend">密码</div>
            </cube-input></Validator
          >
          <div class="error" v-if="error">帐号或密码错误，请重新输入</div>
        </div>
        <button type="button" class="button" @click="login">
          {{ isCheck ? "查询成绩" : "登录" }}
        </button>
      </div>
      <div class="desc" v-if="isCheck">
        <p>特别提示：</p>
        <p>
          <span class="dian"></span>
          <span class="desc-detail"
            >每场考试结束后，才能查询当场考试成绩与排名</span
          >
        </p>
        <p>
          <span class="dian"></span>
          <span class="desc-detail"
            >所有场次考试结束后，可查看最终成绩与排名</span
          >
        </p>
        <p>
          <span class="dian"></span>
          <span class="desc-detail">成绩查询与参赛使用同一帐号</span>
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="stylus">
.login-page {
  .page-head {
    width: 100%;
    padding: 14px 10px;
    background: #fc793b url('~@/assets/images/login_bj.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .text {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: #FFFFFF;
      text-align: center;
      line-height: 30px;
      text-shadow: 0 1px 2px rgba(172, 0, 0, 0.43);
    }
  }

  .page-content {
    width: 100%;
    padding: 0 20px;

    .login-form {
      margin-top: 30px;
      width: 100%;

      .form-item {
        position: relative;
        margin-bottom: 30px;

        .cube-input {
          font-size: 16px;
          line-height: 24px;
          color: #444444;

          .cube-input-prepend {
            color: #444444;
          }

          .cube-input-field {
            padding: 4px 20px;
          }

          .cube-input-eye, .cube-input-clear {
            padding: 4px 10px;
            color: #C5C5C5;
          }

          input::-webkit-input-placeholder {
            color: #C5C5C5;
          }

          input::-moz-input-placeholder {
            color: #C5C5C5;
          }

          input::-ms-input-placeholder {
            color: #C5C5C5;
          }

          &:after {
            border: 0;
            border-bottom: 1px solid #EFEFF7;
          }
        }

        .cube-validator-msg, .error {
          position: absolute;
          left: 0;
          line-height: 26px;
          font-size: 12px;
          color: #F60000;
        }
      }

      .button {
        width: 100%;
        height: 44px;
        font-size: 16px;
        background-color: #FE5718;
        border-radius: 6px;
      }
    }

    .desc {
      margin-top: 24px;
      font-size: 14px;
      color: #969696;
      line-height: 26px;

      .dian {
        display: inline-block;
        margin: 12px 6px 12px 0;
        width: 4px;
        height: 4px;
        background-color: #969696;
        border-radius: 50%;
        vertical-align: top;
      }

      .desc-detail {
        display: inline-block;
        max-width: calc(100% - 10px);
      }
    }
  }
}
</style>
