import Vue from 'vue'
// eslint-disable-next-line
import {
  Style,
  Toast,
  Dialog,
  ActionSheet,
  ImagePreview,
  createAPI
} from 'cube-ui'
import App from './App'
import router from './router'
import store from './store'
import appsApi from '@/api/apps'
import faceApi from '@/api/face'
import '@/common/styles/common.styl'
import i18n from './i18n'
import { getPlatform } from '@/common/utils'
import { QINIU_RESOURCE_URL } from '@/common/config'

import {
  getUser,
  getConfigInfo,
  injectThesaurus,
  saveLog
} from '@/lib/cordovaPlugin/mobilePlugin'

import Popup from '@/common/components/popup'
import '@/lib/css/mathquill.css'
import { getUrlParam, loadScript, loadStaticScript } from '@/utils/utils'
import { decrypt } from '@/utils/crypto.js'

import vueTouch from 'vue-touch'
Vue.use(vueTouch, {name: 'v-touch'})

createAPI(Vue, Popup, ['click'], true)


function getHashParam(name) {
  var reg = new RegExp('(^|\\?|&)' + name + '=([^(&|\/)]*)(&|\/|$)')
  var r = window.location.hash.substr(1).match(reg)
  if (r != null) return decodeURI(r[2])
  return null
}

Vue.use(Toast)
Vue.use(Dialog)
Vue.use(ImagePreview)
Vue.use(ActionSheet)
Vue.config.productionTip = false
Vue.prototype.$saveLog = saveLog
// store.commit('setUser', {})
const initVue = () => {
  new Vue({
    router,
    store,
    i18n,
    render: h => h(App),
    created() {
      console.log(this)
      var user = {}
      appsApi.getUserInfo(getHashParam('userid') || getHashParam('userID') || window.traceId)
        .then(({
          data
        }) => {
          data.userId = data.userID
          data.roleId = data.role
          user = data
          return faceApi.getExamConfig(data.orgId)
        })
        .then(({
          data
        }) => {
          user.faceExam = data.faceExam
          user.faceExamExpireTime = data.faceExamExpireTime
          store.commit('setUser', user)
        })
    }
  }).$mount('#app')
  loadStaticScript('3rdlib/mathjax-2.7.5/MathJax.js?config=TeX-MML-AM_SVG', () => {
    loadStaticScript('3rdlib/mathjax-2.7.5/extensions/TeX/extpfeil.js')
    loadStaticScript('3rdlib/mathjax-2.7.5/extensions/TeX/mediawiki-texvc.js')
    loadStaticScript('3rdlib/mathjax-2.7.5/extensions/TeX/autoload-all.js')
  })
}

function loadOnlinePys() {
  const onlineUrl = `${QINIU_RESOURCE_URL}/thesaurus/py2.min.js`
  loadScript(onlineUrl, null, () => {
    saveLog("在线词库加载失败")
  })
}

window.pluginReady = () => {
    getUser((user) => {
      window.Authorization = user.token
      window.deviceID = user.deviceID
      window.traceId = user.userID
      initVue()
    })
    getConfigInfo((config) => {
      let appVersion = decrypt(config.hashCode, config.hashCodeSrt) || ''
      if (getPlatform().platform === 'ios' || getPlatform().isHarmony) {
        appVersion = config.appVersion
      }
      window.appVersion = appVersion.split('-')[0] || ''
      console.log("config.thesaurus", config.thesaurus)
      window.isOldApp = config.thesaurus === undefined && getPlatform().platform !== 'ios'
      injectThesaurus((content) => {
        try {
          // slice前五十个
          saveLog(content.slice(0, 50))
        } catch (error) {
          console.log(error,'error')
        }
        saveLog("词库注入成功")
        if (getPlatform().platform !== 'ios') {
          saveLog("安卓加载词库")
          const pyScript = document.createElement('script')
          pyScript.innerHTML = content
          document.body.append(pyScript)
        }
      }, () => {
        saveLog("词库注入失败")
        // 安卓没有injectThesaurus方法时还是先加载本地
        if (config.thesaurus && getPlatform().platform !== 'ios') {
          saveLog("安卓加载本地词库")
          loadScript(`cdvfile://umooc_cordova${config.thesaurus}`, null , () => {
            loadOnlinePys()
          })
        } else {
          saveLog("ios加载本地词库")
          loadOnlinePys()
        }
      })
    })

    window.onReloadUserInfo = function () {
      getUser((user) => {
        window.Authorization = user.token
        window.deviceID = user.deviceID
        window.traceId = user.userID
        window.isReLoging = false
      })
    }
}

if (navigator.userAgent.indexOf('umoocApp') !== -1) {
  // app内部先调用插件获取用户信息，在回调中初始化Vue
  // var s = document.createElement('script')
  // s.src = 'static/3rdlib/cordova/CordovaPlugin.js'
  // document.body.appendChild(s)

  // var initTimer = setInterval(function () {
  //   if (window.appPluginReady) {
  //     clearInterval(initTimer)
  //     window.ULplugin.User.getUser(function (jsonString) {
  //       var userData = jsonString
  //       try {
  //         let result = JSON.parse(jsonString)
  //         userData = result
  //       } catch (e) {}
  //       userData.userId = userData.userID
  //       userData.roleId = userData.role
  //       store.commit('setUser', userData)
  //       console.log(userData)
  //       // onDeviceReady()
  //       // cookies.set('AUTHORIZATION', userData.token)

  //       // init(userData, userData.token, 'app')
  //     })
  //   }
  // }, 10)
} else {
  window.traceId = getHashParam('userid') || getHashParam('userID')
  initVue()
  loadOnlinePys()
}

router.beforeEach((to, from, next) => {
  if (from.query.courseid > 0) {
    to.query.courseid = from.query.courseid
  }
  document.title = to.meta.changeTitle ? to.meta.title : '手机考试'
  next()
})

window.goPage = (vm, options) => {
  if(getUrlParam('mp') !== '1') {
    vm.$router.push(options)
  } else {
    vm.$router.replace(options)
  }
}
if(getUrlParam('mp') === '1') {
  let data = {
    url: location.href.split("#")[0],
  }
  faceApi.getWxConfig(data).then(res => {
    const result = res.data
    wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: result.appId, // 必填，公众号的唯一标识
      timestamp: result.timestamp, // 必填，生成签名的时间戳
      nonceStr: result.nonceStr, // 必填，生成签名的随机串
      signature: result.signature, // 必填，签名
      jsApiList: [
        "hideOptionMenu",
        "hideAllNonBaseMenuItem"
      ] // 必填，需要使用的JS接口列表
    });
    wx.ready(function () {
      wx.checkJsApi({
        jsApiList: [
          "hideOptionMenu",
          "hideAllNonBaseMenuItem"
        ], // 需要检测的JS接口列表，所有JS接口列表见附录2,
        success: function (res) {
          
        },
      });
      wx.hideOptionMenu();
      wx.hideAllNonBaseMenuItem();
    });
  })
}

// 姿态检测地址改成cdn地址
window.monitorBasePath = QINIU_RESOURCE_URL.indexOf('ulearning.cn') !== -1 ? QINIU_RESOURCE_URL : './static'
loadScript(`${window.monitorBasePath}/monitor/tfjs.js`, () => {
  loadScript(`${window.monitorBasePath}/monitor/posenet.js`, null, () => {
    saveLog('姿态检测posenet.js加载失败')
  })
}, () => {
  saveLog('姿态检测tfjs.js加载失败')
})
