import Vue from 'vue'
import Vuex from 'vuex'
import createLogger from 'vuex/dist/logger'

Vue.use(Vuex)
const debug = process.env.NODE_ENV !== 'production'
export default new Vuex.Store({
  state: {
    entry: 0, // 0 表示从小应用进入的, 1 表示从app 课程活动入口进入的,
    map: new Map(),
    // user: new Map(),
    user: {},
    timeMap: new Map(),
    headerWarn: false,
    locationInfo: {},
    signInParams: {} 
  },
  getters: {
    getLeftInfo (state) {
      return (examUserID) => {
        let data = state.map.get(examUserID) || { count: 0, time: 0 }
        data.leftTime = state.timeMap.get(examUserID) ? state.timeMap.get(examUserID).time : 0
        return data
      }
    }
    // getUser (state) {
    //   return user => state.user.get(user)
    // }
  },
  mutations: {
    saveLeftInfo (state, { examUserID, count: newCount, time: newTime }) {
      let oldCount = 0
      let oldTime = 0
      const oldInfo = state.map.get(examUserID)
      if (oldInfo) {
        oldCount = oldInfo.count
        oldTime = oldInfo.time
      }
      const count = Math.max(newCount, oldCount)
      const time = Math.max(newTime, oldTime)
      state.map.set(examUserID, { count, time })
    },
    clearTime (state, { examUserID }) {
      let oldCount = 0
      const oldInfo = state.map.get(examUserID)
      if (oldInfo) {
        oldCount = oldInfo.count
      }
      state.map.set(examUserID, { count: oldCount, time: null })
    },
    saveLeftTime (state, { examUserID, time: newTime }) {
      let oldTime = 0
      const oldInfo = state.map.get(examUserID)
      if (oldInfo) {
        oldTime = oldInfo.time
      }
      const time = Math.max(newTime, oldTime)
      state.timeMap.set(examUserID, { time })
    },
    clearLeftTime (state, { examUserID }) {
      state.timeMap.set(examUserID, { time: null })
    },
    changeEntry (state, entry) {
      state.entry = entry
    },
    setUser (state, user) {
      // state.user.set('user', user)
      state.user = user
    },
    // setNewUser (state, newValue) {
    //   state.newUser = newValue
    // }
    changeheaderWarn (state, value) {
      state.headerWarn = value
    },
    changeLocationInfo(state, value) {
      state.locationInfo = value
    },
    saveSignInParams(state, value) {
      state.signInParams = value
    }
  },
  actions: {},
  strict: debug,
  // plugins: debug ? [createLogger()] : []
})
