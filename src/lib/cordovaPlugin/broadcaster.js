window.cordova.define('Cordova/Broadcaster', function (cordovaRequire, cordovaExport, cordovaModule) {
  var exec = cordovaRequire('cordova/exec')
  var channel = cordovaRequire('cordova/channel')
  function Broadcaster () {
    this._channels = {}
    this.channelExists = function (c) {
      return this._channels.hasOwnProperty(c)
    }

    this.channelCreate = function (c) {
      this._channels[c] = channel.create(c)
    }
    this.channelSubscribe = function (c, f) {
      var channel = this._channels[c]
      channel.subscribe(f)
      return channel.numHandlers
    }
    this.channelUnsubscribe = function (c, f) {
      var channel = this._channels[c]
      channel.unsubscribe(f)
      return channel.numHandlers
    }
    this.channelFire = function (event) {
      this._channels[event.type].fire(event)
    }
    this.channelDelete = function (c) {
      delete this._channels[c]
    }
  }

  Broadcaster.prototype.fireNativeEvent = function (eventname, data, success, error) {
    exec(success, error, 'broadcaster', 'fireNativeEvent', [eventname, data])
  }
  Broadcaster.prototype.fireEvent = function (type, data) {
    if (!this.channelExists(type)) return
    var event = document.createEvent('Event')
    event.initEvent(type, false, false)
    if (data) {
      for (var i in data) {
        if (data.hasOwnProperty(i)) {
          event[i] = data[i]
        }
      }
    }
    this.channelFire(event)
  }
  Broadcaster.prototype.addEventListener = function (eventname, f) {
    if (!this.channelExists(eventname)) {
      this.channelCreate(eventname)
      var me = this
      exec(function () {
        me.channelSubscribe(eventname, f)
      }, function (err) {
        console.log('ERROR addEventListener: ', err)
      }, 'broadcaster', 'addEventListener', [eventname])
    } else {
      this.channelSubscribe(eventname, f)
    }
  }
  Broadcaster.prototype.removeEventListener = function (eventname, f) {
    if (this.channelExists(eventname)) {
      if (this.channelUnsubscribe(eventname, f) === 0) {
        var me = this
        exec(function () {
          me.channelDelete(eventname)
        }, function (err) {
          console.log('ERROR removeEventListener: ', err)
        }, 'broadcaster', 'removeEventListener', [eventname])
      }
    }
  }
  cordovaModule.exports = new Broadcaster()
})
