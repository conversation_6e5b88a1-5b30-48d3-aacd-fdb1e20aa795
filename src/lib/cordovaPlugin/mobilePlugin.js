// import Record from  'common/js/UBaseRecord'
/**
 * 从手机获得照片
 * @param album true 代表相册获取 false 从相机获取 默认false
 * @param success 选取照片成功后回调，参数 照片地址
 * @param failure 选取照片失败后回调， 参数 错误原因
 */
import { formatLimit } from "@/utils/fileUtils";
import { LoaderEnsure } from "@/lib/cordovaPlugin/MobilePluginLoader";
// import * as Convert from 'common/components/uAttach/uVideoAttach/convertExit.js'
// import * as ULoading from 'common/js/UDialog'
import Vue from "vue";
import i18n from "@/i18n.js";
/*
 * 获取用户信息
 * */
export function getUser(success) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.User.getUser(function (user) {
      console.log(user);
      success && success(user);
    });
  });
}
/*
 * APP版本配置信息
 * */
export function getConfigInfo(success) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UApp.getConfigInfo(function (config) {
      console.log(config);
      success && success(config);
    });
  });
}
/*
 * APP注入词库
 * */
export function injectThesaurus(success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UApp.injectThesaurus(
      function (data) {
        success && success(data);
      },
      function () {
        fail && fail();
      }
    );
  });
}
/*
 * 写入日志
 * */
export function saveLog(content, success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UApp.saveLog(
      content,
      function () {
        success && success();
      },
      function () {
        fail && fail();
      }
    );
  });
}
/*
 * 异地登录下线
 * */
export function loggedOnOtherDevices(success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.User.loggedOnOtherDevices(
      function () {
        success && success(user);
      },
      function () {
        fail && fail();
      }
    );
  });
}
/*
 * 重新登录
 * */
export function reLogin(success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.User.reLogin(
      function () {
        success && success(user);
      },
      function () {
        fail && fail();
      }
    );
  });
}
/*
 * 分享功能
 * */
export function share(params, success, failure) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    // console.log(plugin)
    plugin.Ushare.share(params, success, failure);
  });
}

const REFRESHACTIVELIST = "kLENotificationRefreshActivieList";

const REFCOURSEACTIVITIESCHANGE = "UNotificationNameCourseActivitiesChange";

/*
 *
 * 从手机端获取图片
 * mediaType = 2  支持视频 图片
 * mediaType = 1  支持视频
   mediaType = 0  支持图片
 * */
export function getPictureFromMobile(
  album = false,
  success,
  failure,
  videoType = true,
  cancelCallback,
  limit = 1,
  correctOrientation = true
) {
  let sourceType = {};
  if (album) {
    sourceType.sourceType = 1;
  } else {
    if (videoType) {
      sourceType.mediaType = 2;
    }
  }
  if (cancelCallback) {
    sourceType.cancelCallback = cancelCallback;
  }
  sourceType.macAccount = limit;
  sourceType.correctOrientation = correctOrientation;
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.getPicture(
      function (result) {
        let arr = [];
        if (result && result.length > 0) {
          let fileArr = [result];
          fileArr.forEach((file) => {
            if (file && !/^file/.test(file)) {
              file = "file://" + file;
            }
            let fileName = file.split("/").pop().toString();
            arr.push({
              filePath: file,
              fileName: fileName,
            });
          });
        }
        success && success(arr);
      },
      function (err) {
        failure && failure(err);
      },
      sourceType
    );
  });
}

export function getFileSize(result, success, failure) {
  LoaderEnsure("Cordova/CustomPlugin", function (plugin) {
    plugin.UFile.fileSize(result, function (fileSize) {
      if (fileSize) {
        success && success(fileSize);
      }
    });
  });
}

export function selectFileFromAndroid(success, params) {
  // var fileType = '*.doc;*.mp3;*.mp4;*.rar;'
  params = params || {};
  const wordType = "*.doc;*.docx;*.xls;*.xlsx;*.pptx;*.ppt;*.pdf;*.txt;";
  const videoType = "*.mp4;*.avi;*.mov;*.flv;*.rmvb;*.mpg;";
  const audioType = "*.mp3;*.mov;";
  const picType = ".jpg;*.jpeg;*.png;*.gif;";
  const fileType = wordType + videoType + audioType + picType;
  const fileSize = 200; //总的文件大小
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UFile.selectFile(fileType, fileSize);
    plugin.UFile.onSelectedFiles = function (fileCount, fileSize, filePath) {
      let fileModel = JSON.parse(filePath)[0];
      let Obj = {};
      if (fileModel.filepath && !/^file/.test(fileModel.filepath)) {
        Obj.filePath = "file://" + fileModel.filepath;
        Obj.fileType = formatLimit(fileModel.filepath);
        Obj.fileSize = fileModel.length;
        Obj.fileName = fileModel.filename;
      }
      success && success(Obj);
    };
  });
}
export function selectFileFromAndroid2(success, params) {
  // var fileType = '*.doc;*.mp3;*.mp4;*.rar;'
  params = params || {};
  const wordType = "*.doc;*.docx;*.xls;*.xlsx;*.pptx;*.ppt;*.pdf;*.txt;";
  const videoType = "*.mp4;*.avi;*.mov;*.flv;*.rmvb;*.mpg;";
  const audioType = "*.mp3;*.mov;";
  const picType = "*.jpg;*.jpeg;*.png;*.gif;";
  const fileType = params.type || wordType + videoType + audioType + picType;
  const fileSize = params.maxTotalSize || 200; //总的文件大小
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UFile.selectFile(
      fileType,
      fileSize,
      params.maxNum,
      params.maxSize,
      params.isHideBottomBtn
    );
    plugin.UFile.onSelectedFiles = function (fileCount, fileSize, files) {
      files = JSON.parse(files);
      let arr = [];
      for (let i = 0; i < files.length; i++) {
        const fileModel = files[i];
        if (fileModel.filepath && !/^file/.test(fileModel.filepath)) {
          let Obj = {};
          Obj.filePath = "file://" + fileModel.filepath;
          Obj.fileType = formatLimit(fileModel.filepath);
          Obj.fileSize = fileModel.length;
          Obj.fileName = fileModel.filename;
          arr.push(Obj);
        }
      }
      success && success(arr);
    };
  });
}

/*
 *
 * 从IOS获取文档
 * */
export function selectFileFromiOS(
  album = false,
  success,
  failure,
  videoType = true,
  cancelCallback
) {
  var options = {};
  if (cancelCallback) {
    options.cancelCallback = cancelCallback;
  }
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.selectFileFromiOS(
      function (result) {
        let fileArr = result ? JSON.parse(result) : [];
        let arr = [];
        fileArr.forEach((file) => {
          if (!/^file/.test(file.filepath)) {
            file.filepath = "file://" + file.filepath;
          }
          arr.push({
            path: file.filepath,
            name: file.filename,
            size: file.length,
          });
        });
        success && success(arr);
      },
      function (err) {
        if (err.toLowerCase() !== "cancel select") {
          alert(err);
        }
        failure && failure(err);
      },
      options
    );
  });
}

//姿态检测相关

//开启视频流摄像头
export function cameraStream(options, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.cameraStream(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      },
      options
    );
  });
}
//姿态检测结果
export function posenetResult(options, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.posenetResult(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      },
      options
    );
  });
}
//显示姿态检测预览
export function showCameraPreview(value, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.showCameraPreview(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      },
      value
    );
  });
}
//开始视频流录制
export function videoRecord(options, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.videoRecord(
      function (res) {
        success && success(res);
      },
      function (err) {
        failure && failure(err);
      },
      options
    );
  });
}
// 上传异常照片
export function uploadPosenet(options, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.uploadPosenet(
      function (res) {
        success && success(res);
      },
      function (err) {
        failure && failure(err);
      },
      options
    );
  });
}
// 等待状态
const ULRECORDREADY = 0;

// 检查权限中
const ULRECORDCHECK = 1;
// 录音中
const ULRECORDING = 2;
// 录音结束
const ULRECORDEND = 3;
// 检查权限拒绝
const ULRECORDDENY = 4;

class Record {
  constructor() {
    // 录音的状态
    this.status = ULRECORDREADY;
    // 文件路径
    this.filePath = "";
    // 当前声音时长
    this.currentDuration = 0;
    // 录音最大时长
    this.durationLimit = 180;
    // 定时器
    this._timer = {};

    this.onBeforeStartRecord = function () {};
    this.startRecord = function (success, failure, durationChange) {
      if (this.filePath) {
        this.filePath = "";
        this.status = ULRECORDREADY;
      }
      // if (this.status !== ULRECORDREADY) {
      //   // 正在录音中，稍后重试
      //   failure && failure('正在录音请稍后重试')
      //   return
      // }
      // 状态切换为检查中
      this.status = ULRECORDCHECK;
      this.onDurationChange = durationChange;
      let _this = this;
      LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
        plugin.URecorder.onStartRecord = function () {
          console.log("我是开始录音 了");
          _this.onBeforeStartRecord && _this.onBeforeStartRecord();
          _this.status = ULRECORDING;
          _this.currentDuration = 0;
          let timer = setInterval(() => {
            console.log(timer);
          }, 1000);

          // _this._timer = setInterval(function () {
          //   if (_this.currentDuration >= _this.durationLimit) {
          //     plugin.URecorder.stopRecord()
          //     return
          //   }
          //   console.log(_this._timer)
          //   console.log(_this.status)
          //   //  不录音时，或者时间超过限制，停止计时，停止录音
          //   if (_this.status === ULRECORDREADY || _this.status === ULRECORDEND || _this.currentDuration >= _this.durationLimit) {
          //     _this.stopRecord()
          //     console.log('我是清除定时器了')
          //     clearInterval(_this._timer)
          //     _this._timer = null
          //     return
          //   }
          //   _this.currentDuration++
          //   _this.onDurationChange && _this.onDurationChange(_this.currentDuration)
          // }, 1000)
        };
        plugin.URecorder.onRecordComplete = function (filePath) {
          _this.status = ULRECORDEND;
          _this.filePath = filePath;
          if (_this._timer) {
            clearInterval(this._timer);
          }
          _this.onRecordCompleteSuccess &&
            _this.onRecordCompleteSuccess(filePath, _this.currentDuration);
          _this.currentDuration = 0;
        };
        plugin.URecorder.checkPermission(
          () => {
            //  权限允许
            // success && success()
            plugin.URecorder.startRecord();
          },
          (msg) => {
            _this.status = ULRECORDDENY;
            failure && failure(i18n.t("mobilePermissionTip2")); // '请打开手机权限'
            //  权限拒绝
          }
        );
      });
    };
    this.stopRecord = function () {
      // this.status = ULRECORDREADY
      LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
        plugin.URecorder.stopRecord();
      });
    };
    this.clearRecord = function () {
      this.status = ULRECORDREADY;
      this.filePath = "";
      this.currentDuration = 0;
      this.stopRecord();
    };
    // 录音完成的回调
    this.onRecordCompleteSuccess = function () {};

    this.onDurationChange = function () {};
  }
}

class Media {
  constructor() {
    // 图片查看
    this.imgPreview = function (imgpath) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.imgPreview(imgpath);
      });
    };
    // 打开文档
    this.openFileInOtherApp = function (path) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.openFileInOtherApp(path);
      });
    };
    // 打开视频
    this.openVideo = function (path) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UMedia.playVideo(path);
      });
    };
  }
}

export function creatMedia() {
  return new Media();
}

/***
 *录音对象
 */
export function creatRecord() {
  return new Record();
}

/*
 * 退出App
 * */
export function exitApp() {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UApp.finish();
  });
}

/*
 * 安卓禁止屏幕录制
 * */
export function forbidScreenRecord(isForbid) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UApp.forbidScreenRecord(isForbid);
  });
}

/*
 * ios主动获取屏幕录制状态
 * */
export function getScreenRecordState(success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UApp.getScreenRecordState(
      function (res) {
        success && success(res);
      },
      function (err) {
        fail && fail(err);
      }
    );
  });
}

/*
 * ios禁止侧滑
 * */
export function forbidBackWithGesture(isForbid) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UApp.forbidBackWithGesture(isForbid);
  });
}

/*
 * 扫描二维码
 * */
export function scanQRCode(isForbid, successFn, failureFn) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    if (!window.qr) {
      window.qr = ULplugin.UApp;
    }
    window.qr.scanQRCode(isForbid, successFn, failureFn);
  });
}

// 打开外部连接带导航栏
export function openUrl(url) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UApp.openUrl(url, true);
  });
}

// 更新活动列表通知
export function updateActiveList() {
  LoaderEnsure("Cordova/Broadcaster", (broadcaster) => {
    broadcaster.fireNativeEvent(REFRESHACTIVELIST, {});
    broadcaster.fireNativeEvent(REFCOURSEACTIVITIESCHANGE, {});
  });
}

/*
 * 给原生发送通知
 * */
export function fireNativeEvent(name, str, parentId, callBack) {
  LoaderEnsure("Cordova/Broadcaster", (broadcaster) => {
    broadcaster.fireNativeEvent(
      name,
      {
        notify: str,
        parentId: parentId,
      },
      callBack
    );
  });
}

/*
 * 添加监听从原生发送的通知
 * */
export function addNativeEvent(name, callback) {
  LoaderEnsure("Cordova/Broadcaster", (broadcaster) => {
    if (!window.broadcaster) {
      window.broadcaster = broadcaster;
    }
    window.broadcaster.addEventListener(name, callback);
  });
}

class MobileUploder {
  constructor() {
    this.manger = {};
    let self = this;
    this.uploadFile = function (
      filePath,
      progress,
      success,
      failure,
      isFullPath = false
    ) {
      if (this.manger[filePath]) return;
      let model = {};
      model.progress = progress;
      model.success = success;
      model.failure = failure;
      this.manger[filePath] = model;
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UUploader.onUploadProgress = function (filePath, progress) {
          let model = self.manger[filePath];
          if (model) {
            model.progress && model.progress(progress);
          }
        };
        ULplugin.UUploader.onUploadCancel = function (filePath) {
          let model = self.manger[filePath];
          if (model) {
            model.cancelSuccess && model.cancelSuccess();
          }
        };
        ULplugin.UUploader.onUploadSuccess = function (filePath, remotePath) {
          let model = self.manger[filePath];
          if (model) {
            if (!isFullPath) {
              remotePath = remotePath.replace(
                "https://leicloud.ulearning.cn/",
                ""
              );
            }
            // let fileType = formatLimit(remotePath)
            // let richFileArr = ['doc', 'pdf', 'ppt', 'xls']
            // if (richFileArr.indexOf(fileType) > 0) {
            //   Convert.getConvertFile(remotePath)
            // } else if (fileType === 'video') {
            //   console.log(remotePath)
            //   Convert.getConvertVideo(remotePath)
            // }
            model.success && model.success(filePath, remotePath);
            delete self.manger[filePath];
          }
        };
        ULplugin.UUploader.onUploadFail = function (filePath) {
          let model = self.manger[filePath];
          if (model) {
            model.failure && model.failure();
            if (self.manger[filePath]) {
              delete self.manger[filePath];
            }
          }
        };
        ULplugin.UUploader.upload(filePath);
      });
    };
    this.cancelUpload = function (filePath, success) {
      if (!this.manger[filePath]) return;
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UUploader.cancel(filePath);
        success && success();
        if (this.manger[filePath]) {
          delete this.manger[filePath];
        }
      });
      // let model = this.manger[filePath]
      // model.cancelSuccess = success
    };
    // 上传
    this.upload = function (filePath, success, fail, progress) {
      // 上传进度
      window.onUploadProgress = (file, progress) => {
        typeof this.progress === "function" && this.progress(file, progress);
      };
      // 取消成功回调
      window.onUploadCancel = (file) => {
        typeof this.cancel === "function" && this.cancel(file);
      };
      // 上传成功回调
      window.onUploadSuccess = (file, remotePath) => {
        typeof this.success === "function" && this.success(file, remotePath);
      };
      // 上传失败的回调
      window.onUploadFail = (file) => {
        typeof this.fail === "function" && this.fail(file);
      };

      const str = filePath.replace("file://", "");
      const timestamp = new Date().getTime();
      const fileName = filePath.split(".").pop();
      const random = Math.random(1000).toFixed(3) * 1000;
      const random2 = Math.random(1000).toFixed(3) * 1000;
      const remoteFile =
        "homework" +
        "/web/" +
        random +
        "/" +
        random2 +
        "/" +
        timestamp +
        "." +
        fileName;
      this.success = success;
      this.progress = progress;
      this.fail = fail;
      LoaderEnsure("", (ULplugin) => {
        ULplugin.UUploader.upload(
          filePath,
          function (file, remotePath) {
            success && success(file, remotePath);
          },
          function (err) {
            fail && fail(err);
          },
          function (file, progressNum) {
            progress && progress(file, progressNum);
          }
        );
      });
    };
    // 取消上传
    this.cancel = function (filePath, cancel) {
      const str = filePath.replace("file://", "");
      this.cancel = cancel;
      LoaderEnsure("", (ULplugin) => {
        ULplugin.UUploader.cancel(filePath);
      });
    };
  }
}

export function createUploade() {
  return new MobileUploder();
}

class MobileDownloader {
  constructor() {
    this.downloadObj = {};
    // 检查远程文件的本地是否下载成功
    this.checkFileLocalPath = function (path, success, failure) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.isExist(path, success, failure);
      });
      // window.ULplugin.UFile.isExist(path, success, failure)
    };
    this.openFileInOtherApp = function (path) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.openFileInOtherApp(path);
      });
      // window.ULplugin.UFile.openFileInOtherApp(path)
    };
    this.openVideo = function (path) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UMedia.playVideo(path);
      });
      // window.ULplugin.UMedia.playVideo(path)
    };
    // 检查网络状态
    this.checkNetworkStatus = function (success) {
      // 1、是wifi 2、是移动网 3、无网络
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UApp.networkState((status) => {
          success(status);
        });
      });
    };
    // 开始下载
    this.download = function (path, progress, complete, failure, fileName) {
      this.addEventListener(path, progress, complete, failure);
      path = this.formateDownLoadPath(
        path,
        fileName,
        progress,
        complete,
        failure
      );
      console.log("我是开始下载了");
      console.log(path);
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.download(path);
      });
    };
    this.cancel = function (path) {
      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.cancelDownload(path);
      });
    };
    // 格式化下载的路径fil
    this.formateDownLoadPath = function (
      path,
      fileName,
      progress,
      complete,
      failure
    ) {
      if (!fileName) {
        let suffixName = path.split("/").pop();
        fileName = suffixName.split(".")[0];
      }
      return `${path}?name=${fileName}`;
    };
    // 添加事件监听，关闭页面，后台正在下载，注册监听方法，可以正确更新文件产状态
    this.addEventListener = function (path, progress, complete, failure) {
      let self = this;
      let Obj = self.downloadObj[path];
      if (Obj) {
        Obj.progressFns.push(progress);
        Obj.completeFns.push(complete);
        Obj.failureFns.push(failure);
      } else {
        let suffixName = path.split("/").pop();
        this.downloadObj[path] = {
          path: path,
          name: suffixName,
          progressFns: [progress],
          completeFns: [complete],
          failureFns: [failure],
        };
      }

      LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
        ULplugin.UFile.onDownloadProgress = function (url, progress) {
          console.log("我是进度更新了");
          if (progress) {
            console.log(progress);
          }
          // console.log('我是进度更新了', url, progress)
          let Obj = self.downloadObj[url];
          if (Obj && Obj.progressFns && Obj.progressFns.length !== 0) {
            Obj.progressFns.forEach((item) => {
              if (progress) {
                item && item(progress);
              }
            });
          }
        };
        ULplugin.UFile.onDownloadError = function (url, errmsg) {
          console.log("我是下载错误了", url, errmsg);
          let Obj = self.downloadObj[url];
          if (Obj && Obj.failureFns && Obj.failureFns.length !== 0) {
            Obj.failureFns.forEach((item) => {
              item && item(errmsg);
            });
          }
        };
        ULplugin.UFile.onDownloadSuccess = function (url, filePath) {
          console.log("我是下载完成了", url, filePath);
          console.log(self.downloadObj);
          let Obj = self.downloadObj[url];
          if (Obj && Obj.completeFns && Obj.completeFns.length !== 0) {
            Obj.completeFns.forEach((item) => {
              item && item(filePath);
            });
          }
        };
      });
    };
  }
}

export function creatDownloader() {
  return new MobileDownloader();
}

export function coureModify() {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UCourse.modify();
  });
}

export function getCourseInfo(success) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UCourse.courseInfo(success);
  });
}

/* 保存图片到本地手机  */
export function savePictureToNative(imgStr, success) {
  LoaderEnsure("Cordova/CustomPlugin", (ULplugin) => {
    ULplugin.UFile.imageWithBase64String(imgStr, success);
  });
}

/* 友盟统计事件  */
export function saveMobClickEvent(name) {
  console.log("loadeeee");
  LoaderEnsure("Umeng/Analytics", (plugin) => {
    console.log(plugin);
    plugin.onEvent(name);
  });
}

/* 打开定位设置，安卓使用 */
export function openLoactionInfo() {
  LoaderEnsure("Cordova/BduLocation", (plugin) => {
    plugin.openLoactionInfo();
  });
}

/* 获取定位 */
export function getLocation(success, failure) {
  LoaderEnsure("Cordova/BduLocation", (plugin) => {
    plugin.getCurrentPosition(
      function (position) {
        function _toDecimal6(x) {
          let f = parseFloat(x);
          if (isNaN(f)) {
            return false;
          }
          f = Math.round(x * 1000000) / 1000000;
          let s = f.toString();
          let rs = s.indexOf(".");
          if (rs < 0) {
            rs = s.length;
            s += ".";
          }
          while (s.length <= rs + 6) {
            s += "0";
          }
          return s;
        }

        // 经度
        let longitude = position.longitude;

        longitude = _toDecimal6(longitude);
        // 纬度
        let latitude = position.latitude;
        latitude = _toDecimal6(latitude);

        // 地理名称
        const addr = `${position.province || ""}${position.city || ""}${
          position.district || ""
        }${position.street || ""}`;
        // 附近位置
        let poiList = [];
        if (position.poiList && position.poiList.length > 0) {
          if (Array.isArray(position.poiList)) {
            // 鸿蒙反的是数组 转换成字符串
            position.poiList = `[${position.poiList.join()}]`;
          }
          poiList = position.poiList
            .substring(1, position.poiList.length - 1)
            .replace(/"/g, "")
            .split(",");
        }
        success({
          longitude,
          latitude,
          addr,
          poiList,
        });
      },
      function (code) {
        function getErrText(code) {
          if (code === 3) {
            return {
              code: 3,
              message: i18n.t("mobilePluginTips1"), // '请允许优学院使用定位服务，允许后重试'
            };
          } else if (code === 5 || code === 6) {
            return {
              code: 5,
              message: i18n.t("mobilePluginTips2"), // '请在手机系统的设置中开启定位服务，并允许优学院使用定位服务'
            };
          } else {
            return {
              code: code,
              message: i18n.t("mobilePluginTips3"), // '定位服务不稳定，无法获取你的位置信息，你可以稍后重试'
            };
          }
        }

        let msg = getErrText(code);
        if (msg.code === 3) {
          new Vue()
            .$createDialog({
              type: "alert",
              content: msg.message,
            })
            .show();
        } else if (msg.code === 5) {
          new Vue()
            .$createDialog({
              type: "confirm",
              content: msg.message,
              confirmBtn: { text: i18n.t("confirm"), active: true },
              cancelBtn: { text: i18n.t("cancel") },
              onConfirm: () => {
                openLoactionInfo();
              },
            })
            .show();
        } else if (msg.code === 6) {
          success({
            longitude: -1,
            latitude: -1,
            addr: "",
            poiList: [],
          });
          return;
        } else {
          new Vue()
            .$createToast({
              type: type,
              time: 1500,
              txt: msg.message,
            })
            .show();
        }
        failure && failure(msg);
      }
    );
  });
}

/*
 * 检测多窗口离开
 * */
export function multiWindowMode(success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UApp.multiWindowMode(
      function (data) {
        success && success(data);
      },
      function () {
        fail && fail();
      }
    );
  });
}
/*
 * 人脸识别
 * */
export function faceRecognition(isFromExamDetail, success, fail) {
  LoaderEnsure("Cordova/CustomPlugin", (plugin) => {
    plugin.UApp.faceRecognition(
      isFromExamDetail,
      function (data) {
        success && success(data);
      },
      function (err) {
        fail && fail(err);
      }
    );
  });
}

// 拿到抓拍本地路径并上传
export function takeACandidPhotograph(success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.takeACandidPhotograph(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}

// 检查相机权限
export function checkPermission(success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.checkPermission(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}
// 人脸识别处getClientFile照片
export function takePicture(options, success, failure) {
  LoaderEnsure("Cordova/Camera", function (camera) {
    camera.takePicture(
      options,
      function (filePath) {
        success && success(filePath);
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}
// NativeLifeCycle 多窗口切屏
export function registerOnStop(handle, success) {
  LoaderEnsure("Cordova/NativeLifeCycle", function (ULplugin) {
    ULplugin.NativeLifeCycle.registerOnStop(handle, function () {
      success && success();
    });
  });
}
export function deregisterOnStop(handle) {
  LoaderEnsure("Cordova/NativeLifeCycle", function (ULplugin) {
    ULplugin.NativeLifeCycle.deregisterOnStop(handle);
  });
}
export function registerOnStart(handle, success) {
  LoaderEnsure("Cordova/NativeLifeCycle", function (ULplugin) {
    ULplugin.NativeLifeCycle.registerOnStart(handle, function () {
      success && success();
    });
  });
}
export function deregisterOnStart(handle) {
  LoaderEnsure("Cordova/NativeLifeCycle", function (ULplugin) {
    ULplugin.NativeLifeCycle.deregisterOnStart(handle);
  });
}
export function deregisterAll() {
  LoaderEnsure("Cordova/NativeLifeCycle", function (ULplugin) {
    ULplugin.NativeLifeCycle.deregisterOnStart();
  });
}

// 检查录音机权限
export function checkRecorderPermission(success, failure) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.URecorder.checkPermission(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}
// 开始录音机录制
export function startRecord(success, failure) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.URecorder.startRecord(
      function (data) {
        success && success(data);
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}
// 停止录音机录制
export function stopRecord(success, failure, progress) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.URecorder.stopRecord(
      function () {
        success && success();
      },
      function (err) {
        failure && failure(err);
      }
    );
  });
}
// 申请权限并启动录屏服务
export function startScreenCaptureService() {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UScreenAnalysis.startScreenCaptureService();
  });
}
// 启动屏幕分析
export function startScreenAnalysis(options) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UScreenAnalysis.startScreenAnalysis(options);
  });
}
// 结束录屏服务并停止屏幕分析
export function stopScreenCaptureService() {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UScreenAnalysis.stopScreenCaptureService();
  });
}
// 分析一次屏幕
export function analyze(id) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UScreenAnalysis.analyze(id);
  });
}
// 设置是否可以分析
export function setCanAnalyze(canAnalyze) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UScreenAnalysis.setCanAnalyze(canAnalyze);
  });
}

// 移动端键盘初始化
export function keysInit() {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UKeyboard.KeyboardInit(
      () => {
        console.log("键盘初始化成功");
      },
      (err) => {
        console.log("键盘初始化失败", err);
      }
    );
  });
}
// 获取键盘输入的搜索结果
export function getKeysResult(data, success, error) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UKeyboard.getKeyboardResult(data, success, error);
  });
}

export function keysReset(success, error) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UKeyboard.keyboardReset(success, error);
  });
}

export function switchInputMode(data, success, error) {
  LoaderEnsure("", function (ULplugin) {
    ULplugin.UKeyboard.switchInputMode(data, success, error);
  });
}
