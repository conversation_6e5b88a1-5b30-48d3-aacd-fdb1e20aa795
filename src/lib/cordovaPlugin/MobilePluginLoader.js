/*
* 确保插件成功
* */
export function LoaderEnsure (message, success) {
  // console.log(window.cordova)
  if (window.ULplugin) {
    if (message == 'Cordova/Camera') {
      success(window.ULplugin.Camera)
    } else if (message == 'Cordova/BduLocation') {
      success(window.ULplugin.BaiduLocation)
    } else if (message == 'Cordova/Broadcaster') {
      success(window.broadcaster)
    } else {
      success(window.ULplugin)
    }
  } else {
    document.addEventListener('deviceready', () => {
      setTimeout(() => {
        LoaderEnsure(message, success)
      })
    }, false)
  }
}
