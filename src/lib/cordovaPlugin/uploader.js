window.uploadCbs = {}
class Uploader {
  
  constructor () {
    this.pluginName = 'UUploader'
    this.execNativeMethod = function (methodName, args, success, fail) {
      let exec = window.cordova.require('cordova/exec')
      exec(success, fail, this.pluginName, methodName, args)
    }

     // 上传进度
    window.onUploadProgress = function (filePath, progress) {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      for (var i in window.uploadCbs) {
        if (i == filePath) {
          var progressCb = window.uploadCbs[i].progressCb
          progressCb && progressCb(filePath, progress);
          return;
        }
      }
    }
    // 取消成功回调
    window.onUploadCancel = function (filePath) {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      for (var i in window.uploadCbs) {
        if (i == filePath) {
          var cancelCb = window.uploadCbs[i].cancelCb
          cancelCb && cancelCb(filePath);
          return;
        }
      }
    }
    // 上传成功回调
    window.onUploadSuccess = function (filePath, remotePath) {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      for (var i in window.uploadCbs) {
        if (i == filePath) {
          var successCb = window.uploadCbs[i].successCb
          successCb(filePath, remotePath);
          return;
        }
      }
    }
    // 上传失败的回调
    window.onUploadFail = function (filePath) {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      for (var i in window.uploadCbs) {
        if (i == filePath) {
          var failCb = window.uploadCbs[i].failCb
          failCb(filePath);
          return;
        }
      }
    }
  }
  upload (filePath, callbacks) {
    let self = this
    let str = filePath.replace('file://', '')
    let timestamp = new Date().getTime()
    let fileName = filePath.split('.').pop()
    let random = Math.ceil(Math.random() * 10000)
    let random2 = Math.ceil(Math.random() * 10000)
    let remoteFile = 'resource' + '/web/' + random + '/' + random2 + '/' + timestamp + '.' + fileName

    window.uploadCbs[filePath] = callbacks

    this.execNativeMethod('upload', [str, remoteFile], null, null)
  }

  cancel(filePath) {
    var str = filePath.replace('file://', '')
    this.execNativeMethod('cancel', [str], null, null)
  }
}

export default Uploader