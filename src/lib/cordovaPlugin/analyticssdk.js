
window.cordova.define("Umeng/Analytics", function(cordovaRequire, cordovaExport, cordovaModule) {
  /*
   * Javascript interface of Cordova plugin for Umeng Analytics SDK
   */
  var exec = cordovaRequire('cordova/exec')
  var channel = cordovaRequire('cordova/channel')

  var MobclickAgent = {

    /**
     * 自定义事件数量统计
     *
     * @param eventId
     *            String类型.事件ID，注意需要先在友盟网站注册此ID
     */
    onEvent : function(eventId) {
      exec(null, null, "UMPlugin","onEvent", [ eventId ]);

    },
    /**
     * 自定义事件数量统计
     *
     * @param eventId
     *            String类型.事件ID， 注意需要先在友盟网站注册此ID
     * @param eventLabel
     *            String类型.事件标签，事件的一个属性说明
     */
    onEventWithLabel : function(eventId, eventLabel) {
      exec(null, null, "UMPlugin","onEventWithLabel", [ eventId, eventLabel ]);

    },
    /**
     * 自定义事件数量统计
     *
     * @param eventId
     *            String类型.事件ID， 注意需要先在友盟网站注册此ID
     * @param eventData
     *            Dictionary类型.当前事件的属性集合，最多支持10个K-V值
     */
    onEventWithParameters : function(eventId, eventData) {
     exec(null, null, "UMPlugin","onEventWithParameters", [ eventId, eventData ]);

    },
    /**
     * 自定义事件数值型统计
     *
     * @param eventId
     *            String类型.事件ID，注意要先在友盟网站上注册此事件ID
     * @param eventData
     *            Dictionary类型.事件的属性集合，最多支持10个K-V值
     * @param eventNum
     *            int 类型.事件持续时长，单位毫秒，您需要手动计算并传入时长，作为事件的时长参数
     *
     */
    onEventWithCounter : function(eventId, eventData, eventNum) {
      exec(null, null, "UMPlugin","onEventWithCounter", [ eventId, eventData, eventNum ]);

    },
    /**
     * 页面统计开始时调用
     *
     * @param pageName
     *            String类型.页面名称
     */
    onPageBegin : function(pageName) {
      exec(null, null, "UMPlugin","onPageBegin", [ pageName ]);

    },
    /**
     * 页面统计结束时调用
     *
     * @param pageName
     *            String类型.页面名称
     */
    onPageEnd : function(pageName) {
     exec(null, null, "UMPlugin","onPageEnd", [ pageName ]);

    },

  };

  cordovaModule.exports =MobclickAgent;

});
