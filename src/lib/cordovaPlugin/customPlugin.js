window.cordova.define('Cordova/CustomPlugin', function (cordovaRequire, cordovaExport, cordovaModule) {
  let exec = window.cordova.require('cordova/exec')

  class BasePlugin {
    constructor(pluginName) {
      this.pluginName = pluginName
      this.execNativeMethod = function (methodName, args, success, fail) {
        exec(success, fail, this.pluginName, methodName, args)
      }
    }
  }

  class Share extends BasePlugin {
    constructor() {
      super('UShare')

      this.share = function (shareContent, success, fail) {
        this.execNativeMethod('share', [shareContent], success, fail)
      }
    }
  }

  class UApp extends BasePlugin {
    constructor() {
      super('UApp')
    }

    finish() {
      this.execNativeMethod('finish', [], null, null)
    }

    back() {
      this.execNativeMethod('back', [], null, null)
    }

    openNewWindow(url) {
      this.execNativeMethod('openNewWindow', [{
        'url': url
      }], null, null)
    }

    openUrl(url) {
      this.execNativeMethod('openUrl', [url, true], null, null)
    }

    // 检查网络状态  -1未知网络链接    0无网络  1 wifi   2 移动网络
    networkState(success) {
      this.execNativeMethod('networkState', [], success, null)
    }
    // 扫一下二维码  返回在成功回调中  
    scanQRCode(isForbid, success, failure) {
      this.execNativeMethod('scanQRCode', [isForbid], null, null)
      window.onQRSuccess = function () {
        typeof success === 'function' && success(...arguments)
      }
      window.onQRFailure = function () {
        typeof failure === 'function' && failure(...arguments)
      }
    }
    getConfigInfo(success, fail) {
      this.execNativeMethod('getConfigInfo', [], success, fail)
    }
    // 禁止屏幕录制
    forbidScreenRecord(isForbid, success, fail) {
      this.execNativeMethod('forbidScreenRecord', [isForbid], success, fail)
    }
    // 主动获取屏幕录制状态
    getScreenRecordState(success, fail) {
      this.execNativeMethod('getScreenRecordState', [], success, fail)
    }
    // ios禁止侧滑返回
    forbidBackWithGesture(isForbid, success, fail) {
      this.execNativeMethod('forbidBackWithGesture', [isForbid], success, fail)
    }
    // 注入词库
    injectThesaurus(success, fail) {
      this.execNativeMethod('injectThesaurus', [], success, fail)
    }
    // 写入日志
    saveLog(content, success, fail) {
      this.execNativeMethod('saveLog', [content], success, fail)
    }
    // 检测多窗口离开
    multiWindowMode(success, fail) {
      this.execNativeMethod('multiWindowMode', [], success, fail)
    }
    // 人脸识别
    faceRecognition(isFromExamDetail, success, fail) {
      this.execNativeMethod('faceRecognition', [isFromExamDetail], success, fail)
    }

  }

  class Umessage extends BasePlugin {
    constructor() {
      super('UMessage')
    }

    goToChat(userName, name) {
      this.execNativeMethod('goToChat', [{
        'userName': userName,
        'name': name
      }], null, null)
    }
  }

  class UMedia extends BasePlugin {
    constructor() {
      super('UMedia')
    }

    playVideo(url) {
      this.execNativeMethod('playVideo', [url], null, null)
    }
  }

  class User extends BasePlugin {
    constructor() {
      super('User')
    }

    getUser(success) {
      this.execNativeMethod('getUser', [], success, null)
    }

    loggedOnOtherDevices(success, fail) {
      this.execNativeMethod('loggedOnOtherDevices', [], success, fail)
    }

    reLogin(success, fail) {
      this.execNativeMethod('reLogin', [], success, fail)
    }
  }

  class UUploader extends BasePlugin {
    constructor() {
      super('UUploader')
    }

    // 上传
    upload(filePath) {
      let str = filePath.replace('file://', '')
      let timestamp = new Date().getTime()
      let fileName = filePath.split('.').pop()
      var random = Math.ceil(Math.random() * 10000)
      var random2 = Math.ceil(Math.random() * 10000)
      var remoteFile = 'resource' + '/web/' + random + '/' + random2 + '/' + timestamp + '.' + fileName

      let self = this
      // 上传进度
      window.onUploadProgress = function (file, progress) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadProgress && self.onUploadProgress(file, progress)
      }
      // 取消成功回调
      window.onUploadCancel = function (file) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadCancel && self.onUploadCancel(file)
      }
      // 上传成功回调
      window.onUploadSuccess = function (file, remoPath) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadSuccess && self.onUploadSuccess(file, remoPath)
      }
      // 上传失败的回调
      window.onUploadFail = function (file) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadFail && self.onUploadFail(file)
      }

      this.execNativeMethod('upload', [str, remoteFile], null, null)
    }

    // 取消上传
    cancel(filePath) {
      var str = filePath.replace('file://', '')
      this.execNativeMethod('cancel', [str], null, null)
    }
  }

  class URecorder extends BasePlugin {
    constructor() {
      super('URecorder')
      var self = this
      // 开始录音
      window.startRecord = function () {
        self.onStartRecord && self.onStartRecord()
      }
      window.onVolumeChanged = function (vau) {}
      window.stopRecord = function () {}
      // 录音完成
      window.recordFile = function (filePath) {
        if (!/^file/.test(filePath)) {
          filePath = 'file://' + filePath
        }
        self.onRecordComplete && self.onRecordComplete(filePath)
      }
    }

    checkPermission(success, failure) {
      this.execNativeMethod('checkPermission', [], success, failure)
    }

    // 开始录音
    startRecord() {
      this.execNativeMethod('startRecord', [], null, null)
    }

    // 结束录音
    stopRecord() {
      this.execNativeMethod('stopRecord', [], null, null)
    }

    onStartRecord() {}

    onRecordComplete() {}
  }

  class UFile extends BasePlugin {
    constructor() {
      super('UFile')
      var self = this
      // 当选择文件结束后回调
      window.onSelectedFiles = function (fileCount, fileSize, fileArr) {

        self.onSelectedFiles && self.onSelectedFiles(fileCount, fileSize, fileArr)
      }
      // 开始下载
      window.download = function (url, progress) {

        self.onDownloadProgress && self.onDownloadProgress(url, progress)
      }
      window.onDownloadError = function (url, errmsg) {
        self.onDownloadError && self.onDownloadError(url, errmsg)
      }
      window.onDownloadSuccessed = function (url, filePath) {
        console.log(url, filePath)
        self.onDownloadSuccess && self.onDownloadSuccess(url, filePath)
      }
      window.onDownloadAllSuccessed = function () {
        console.log(arguments)
      }
    }

    selectFile(fileType, fizeSize) {
      let arg = [].slice.call(arguments, 0)
      this.execNativeMethod('selectFile', arg)
    }

    //  上传文件
    upload() {
      this.execNativeMethod('upload', [])
    }

    //   删除文件
    uDelete(filepath, successFunction, errorFunction) {
      this.execNativeMethod('uDelete', [filepath], successFunction, errorFunction)
      // exec(successFunction, errorFunction, "UFile", "delete", [filepath]);
    }

    //   获取文件详情
    fileDetail(filepath, successFunction) {
      this.execNativeMethod('fileDetail', filepath, successFunction)
    }

    stopUpload(successFunction, errorFunction) {
      this.execNativeMethod('selectFile', [], successFunction, errorFunction)
    }

    download(fileArr, successFtn) {
      this.execNativeMethod('download', [fileArr], successFtn, null)
    }

    imageWithBase64String(str, successFn) {
      this.execNativeMethod('imageWithBase64String', [str], successFn, null)
    }

    cancelDownload() {
      this.execNativeMethod('cancelDownload', [], null, null)
      // exec(null, null, "UFile", "cancelDownload", []);
    }

    //   判断文件是否已缓存     successFunction传回缓存后的本地文件路径
    isExist(filepath, successFunction, errorFunction) {
      this.execNativeMethod('isExist', [filepath], successFunction, errorFunction)
      // exec(successFunction,errorFunction, "UFile", "isExist", [filepath]);
    }

    //  图片预览
    imgPreview(imgpath) {
      this.execNativeMethod('imgPreview', [imgpath])
    }

    //   使用其他应用打开文件
    openFileInOtherApp(filepath) {
      this.execNativeMethod('openFileInOtherApp', [filepath])
    }

    //  删除缓存文件
    deleteFile(filepath, successFunction, errorFunction) {
      this.execNativeMethod('deleteFile', [filepath], successFunction, errorFunction)
      // exec(successFunction, errorFunction, "UFile", "deleteFile", [filepath]);
    }

    // 获取文件大小
    fileSize(filePath, success) {
      this.execNativeMethod('fileSize', [filePath], success, null)
    }

    onDownloadProgress() {

    }

    onDownloadError() {

    }

    onDownloadSuccess() {

    }

    onSelectedFiles() {

    }
  }

  class UCourse extends BasePlugin {
    constructor() {
      super('UCourse')
    }

    modify() {
      this.execNativeMethod('modify', [], null, null)
    }

    // 获取当前课程信息
    courseInfo(success) {
      this.execNativeMethod('courseInfo', [], success, null)
    }
  }

  let ULPlugin = {
    Ushare: new Share({}),
    UApp: new UApp({}),
    Umessage: new Umessage({}),
    User: new User({}),
    URecorder: new URecorder({}),
    UMedia: new UMedia({}),
    UUploader: new UUploader({}),
    UFile: new UFile({}),
    UCourse: new UCourse({})
  }
  cordovaModule.exports = ULPlugin
})