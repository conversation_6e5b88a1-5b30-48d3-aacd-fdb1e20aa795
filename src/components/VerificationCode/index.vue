<template>
  <Popup type="my-popup" :mask="true" :mask-closable="false" ref="verificationCodePopup">
    <div id="verificationCodeDom">
    </div>
  </Popup>
</template>

<script>
import { Popup } from 'cube-ui'
export default {
  components: {
    Popup
  },
  data () {
    return {
      // 验证码应用id
      CaptchaAppId: '190249314'
    };
  },
  methods: {
    show(){
      this.$refs.verificationCodePopup.show()
    },
    hide(){
      this.$refs.verificationCodePopup.hide()
    },
    //生成验证码test
    createCode(){
      try {
        // 生成一个验证码对象
        // CaptchaAppId：登录验证码控制台，从【验证管理】页面进行查看。如果未创建过验证，请先新建验证。注意：不可使用客户端类型为小程序CaptchaAppId， 会 导致数据统计错误。
        //callback：定义的回调函数
        var captcha = new TencentCaptcha(document.getElementById('verificationCodeDom'), this.CaptchaAppId, (res) => {
          this.$emit('codeCallback', res)
          // 不管成功还是失败都关闭popup
          this.hide()
        }, {
          type: 'embed',
          needFeedBack: false,
          uerLanguage: 'zh-cn'
        });
        // 调用方法，显示验证码
        captcha.show(); 
      } catch (error) {
        // 加载异常，调用验证码js加载错误处理函数 手动生成票据返给后台校验token
        let ticket = 'trerror_1001_' + this.CaptchaAppId + '_' + Math.floor(new Date().getTime() / 1000); 
        let res = {
          ret: 0,
          randstr: '@'+ Math.random().toString(36).substr(2),
          ticket: ticket,
          errorCode: 1001,
          errorMessage: 'jsload_error'
        }
        this.$emit('codeCallback', res)
        // 不管成功还是失败都关闭popup
        this.hide()
      }
    }
  }
}

</script>