<template>
  <div :class="$style['component-face']">
    <canvas ref="camera" width="250" height="350"></canvas>
  </div>
</template>
<script>
import faceApi from '@/api/face'
import axios from 'axios'
import { BASE64_SAVEURL, QINIU_RESOURCE_URL } from '@/config/config'
export default {
  data () {
    return {

    }
  },
  mounted () {
    console.log('created')
    console.log(process.env.VUE_APP_API)
    let self = this
    var ctx = this.$refs.camera.getContext('2d')
    var config = {
      width: 250,
      height: 350
    }
    function rgbaToGrayscale (rgba, nrows, ncols) {
      var gray = new Uint8Array(nrows * ncols)
      for (var r = 0; r < nrows; ++r) {
        for (var c = 0; c < ncols; ++c) { // gray = 0.2*red + 0.7*green + 0.1*blue
          gray[r * ncols + c] = (2 * rgba[r * 4 * ncols + 4 * c + 0] + 7 * rgba[r * 4 * ncols + 4 * c + 1] + 1 * rgba[r * 4 * ncols + 4 * c + 2]) / 10
        }
      }
      return gray
    }
    var facefinderClassifyRegion = function (r, c, s, pixels, ldim) { return -1.0 }
    var updateMemory = window.pico.instantiate_detection_memory(5) // we will use the detecions of the last 5 frames
    function circleInFrame (circleX, circleY, circleR) {
      var $camera = self.$refs.camera
      var xMin = $camera.offsetLeft
      var xRange = $camera.offsetWidth
      var yMin = $camera.offsetTop
      var yRange = $camera.offsetHeight
      var total = 100000
      var numInCircle = 0
      var num = 0
      for (var i = 0; i < total; i++) {
        // 投点
        var x = Math.round(Math.random() * $camera.offsetWidth)
        var y = Math.round(Math.random() * $camera.offsetHeight)
        var inCircle = Math.pow(circleX - x, 2) + Math.pow(circleY - y, 2) - Math.pow(circleR, 2) < 0
        var inFrame = x > xMin && x < xMin + xRange && y > yMin && y < yMin + yRange
        if (inCircle) {
          numInCircle++
          if (inFrame) {
            num++
          }
        }
      }
      console.log(num, numInCircle)
      if (num / numInCircle > 0.9) {
        return true
      }
      return false
    }
    
    var processfn = function (video, dt) {
      // render the video frame to the canvas element and extract RGBA pixel data
      // if(self.myCanvas.video.paused) {
      //   return
      // }
      ctx.drawImage(video, 0, 0)
      var rgba = ctx.getImageData(0, 0, config.width, config.height).data
      // prepare input to `run_cascade`
      var image = {
        'pixels': rgbaToGrayscale(rgba, config.height, config.width),
        'nrows': config.height,
        'ncols': config.width,
        'ldim': config.width
      }
      var params = {
        'shiftfactor': 0.1, // move the detection window by 10% of its size
        'minsize': 100, // minimum size of a face
        'maxsize': 1000, // maximum size of a face
        'scalefactor': 1.5 // for multiscale processing: resize the detection window by 10% when moving to the higher scale
      }
      var dets = window.pico.run_cascade(image, facefinderClassifyRegion, params)
      dets = updateMemory(dets)
      dets = window.pico.cluster_detections(dets, 0.2) // set IoU threshold to 0.2
      for (var i = 0; i < dets.length; ++i) {
        if (dets[i][3] > 50.0) { // 检测到人脸出现
          if (circleInFrame(dets[i][1], dets[i][0], dets[i][2] / 2)) {
            try {
              self.getImage()
              self.myCanvas.video.pause()
            } catch (e) {
              console.error(e)
              self.myCanvas.video.play()
            }
          }
        }
      }
    }
    var cascadeurl = './static/face/facefinder'
    fetch(cascadeurl).then(function (response) {
      response.arrayBuffer().then(function (buffer) {
        var bytes = new Int8Array(buffer)
        facefinderClassifyRegion = window.pico.unpack_cascade(bytes)
        console.log('* cascade loaded')
      })
    })
    this.myCanvas = new this.Canvas(ctx, processfn)
  },
  methods: {
    getImage () {
      var dataUrl = this.$refs.camera.toDataURL('image/jpeg')
      // var $img = document.getElementById('faceImg')
      // $img.src = dataUrl
      var base64 = dataUrl.substr(dataUrl.indexOf('base64') + 7)
      faceApi.getUpToken({ fileName: 'base64.jpg' })
        .then(({ data }) => {
          axios({
            baseURL: BASE64_SAVEURL,
            headers: { 'Authorization': 'UpToken ' + data.uptoken, 'Content-Type': 'application/octet-stream' },
            method: 'post',
            data: base64
          }).then(response => {
            let url = QINIU_RESOURCE_URL + response.data.key
            this.compareFace(url)
          })
        })
      // uploadBase64(base64, 'base64.jpg', function (url) {
      //   // $img.src = url
      //   self.user.testFace(self.user.face().url, url, function (response) {
      //     if (response.samePerson) {
      //       self.step('faceSuccess')
      //       setTimeout(function () {
      //         self.step('photoUploaded')
      //       }, 3000)
      //     } else {
      //       self.step('faceFail')
      //     }
      //     self.destoryCamera()
      //   })
      // })
    },
    compareFace (url) {
      let data = {
        'url': url,
        'status': '0'
      }
      faceApi.compareFace({ examId: this.$route.params.examID }, data)
        .then(({ response }) => {
          console.log(response)
        })
    },
    Canvas (ctx, callback) {
      var self = this
      this.ctx = ctx
      this.callback = callback

      // We can't `new Video()` yet, so we'll resort to the vintage
      // "hidden div" hack for dynamic loading.
      var streamContainer = document.createElement('div')
      this.video = document.createElement('video')

      // If we don't do this, the stream will not be played.
      // By the way, the play and pause controls work as usual
      // for streamed videos.
      this.video.setAttribute('autoplay', '1')
      this.video.setAttribute('playsinline', '1')

      // The video should fill out all of the canvas
      this.video.setAttribute('width', 1)
      this.video.setAttribute('height', 1)

      streamContainer.appendChild(this.video)
      document.body.appendChild(streamContainer)

      // The callback happens when we are starting to stream the video.
      navigator.mediaDevices.getUserMedia({ video: { width: 250, height: 350 }, audio: false }).then(function (stream) {
        // Yay, now our webcam input is treated as a normal video and
        // we can start having fun
        self.video.srcObject = stream
        // Let's start drawing the canvas!
        self.update()
      }, function (err) {
        throw err
      })

      // As soon as we can draw a new frame on the canvas, we call the `draw` function
      // we passed as a parameter.
      this.update = function () {
        var self = this
        var last = Date.now()
        var loop = function () {
          // For some effects, you might want to know how much time is passed
          // since the last frame; that's why we pass along a Delta time `dt`
          // variable (expressed in milliseconds)
          var dt = Date.now - last
          if (!self.video.paused) {
            self.callback(self.video, dt)
          }
          last = Date.now()
          window.requestAnimationFrame(loop)
        }
        window.requestAnimationFrame(loop)
      }
    }
  }
}
</script>
<style lang="stylus" module>
.component-face {
  position relative
  font-size: 14px;
}
</style>
