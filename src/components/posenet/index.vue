<template>
  <img
    id="posenetImg"
    width="480"
    height="640"
    crossOrigin='anonymous'
    :src="src"
    v-show="false"
    v-if="src"
  />
</template>
<script>
import PosenetModel from "@/utils/PosenetModel.js";
import Bus from '@/utils/bus'
export default {
  data() {
    return {
      src: "",
      posenetModel: {},
      isCameraOpen: false
    }
  },
  created() {
    this.posenetModel = new PosenetModel()
  },
  mounted() {
    const _this = this
    window.posenetImage = (imageData) => {
      // 选择文件暂停检测
      if (_this.isCameraOpen) {
        return 
      }
      _this.src = `data:image/jpg;base64,${imageData}`
      _this.$nextTick(() => {
        const imageElement = document.getElementById('posenetImg');
        imageElement.onload = () => {
          _this.posenetModel.getPose(imageElement, (status, result) => {
            console.log("status, result", { status, result, imageData })
            _this.$emit("poseResult", { status, result, imageData })
          })
        }
      })
      console.log("window.posenet", posenet)
    }
    Bus.$on('camera-change', (data) => {
      this.isCameraOpen = data
    })
  }
}
</script>