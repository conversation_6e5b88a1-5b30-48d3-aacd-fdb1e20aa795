<template>
	<div :class="$style.utilBoxContainer" @mousedown="down" @touchstart="down" @mousemove="move" @touchmove="move"
		@mouseup="end" @touchend="end" ref="utilBox" @click="clickUtilBox">
		<img class="icon" :src="require('@/assets/images/svg/calculator.svg')" alt="grid" />
	</div>
</template>

<script>
export default {
	data() {
		return {
			flag: false,//控制使用
			position: {
				x: 0,
				y: 0
			},
			nx: '',
			ny: '',
			dx: '',
			dy: '',
			xPum: '',
			yPum: '',
		};
	},

	components: {
	},
	watch: {
		flag(val) {
			this.$emit('canSwiperSlide', val)
		}
	},
	mounted() { },

	methods: {
		clickUtilBox() {
			this.$emit('clickUtilBox')
		},
		down() {
			this.flag = true;
			var touch;
			if (event.touches) {
				touch = event.touches[0];
			} else {
				touch = event;
			}
			this.position.x = touch.clientX;
			this.position.y = touch.clientY;
			this.dx = this.$refs.utilBox.offsetLeft;
			this.dy = this.$refs.utilBox.offsetTop;
		},
		move() {
			if (this.flag) {
				var touch;
				if (event.touches) {
					touch = event.touches[0];
				} else {
					touch = event;
				}
				this.nx = touch.clientX - this.position.x;
				this.ny = touch.clientY - this.position.y;
				this.xPum = this.dx + this.nx;
				this.yPum = this.dy + this.ny;
				let width = window.innerWidth - this.$refs.utilBox.offsetWidth//屏幕宽度减去自身控件宽度
				let height = window.innerHeight - this.$refs.utilBox.offsetHeight//屏幕高度减去自身控件高度
				this.xPum < 0 && (this.xPum = 0)
				this.yPum < 0 && (this.yPum = 0)
				this.xPum > width && (this.xPum = width)
				this.yPum > height && (this.yPum = height)
				// if (this.xPum >= 0 && this.yPum >= 0 && this.xPum<= width &&this.yPum<= height) {
				this.$refs.utilBox.style.left = this.xPum + 'px';
				this.$refs.utilBox.style.top = this.yPum + 'px';
				// }
			}
			// top.getCurrentPages()[0].$refs.utilBox.GoToIM
		},
		//鼠标释放时候的函数
		end() {
			// 靠左自动吸附
			// this.$refs.utilBox.style.left= '0px';
			this.flag = false;
		},
	}
}

</script>
<style lang="stylus" module>
.utilBoxContainer {
  position fixed;
  bottom: 10px;
  right: 10px;
  width: 80px;
  height: 80px;
  z-index: 99;
  cursor: pointer;
}
</style>