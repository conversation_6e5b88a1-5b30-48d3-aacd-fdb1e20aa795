<template>
  <div class="calculator-container">
    <div class="input-wrap-container" @touchmove.stop.passive="() => {}">
      <div class="input-wrap" ref="inputWrap">
        <div class="display" :class="hasResult ? 'has-res' : 'no-res' " ref="displayRef">
          <span v-html="display"></span>
          <span v-if="hasResult">=</span>
        </div>
        <div v-if="hasResult" class="result" v-html="result" ref="resultRef"></div>
      </div>
    </div>
    <div class="calculator">
      <div class="keys">
        <div v-for="(item, i) in keys" :key="i" @click="clickBtn(item)" @touchstart="touchStart(item, i)"
          @touchend="touchEnd(item, i)" :class="getHoverStatus(item)">
          <i :class="`iconfont ${item.icon}`"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import math from 'mathjs'
export default {
  data() {
    return {
      display: 0,
      result: '',
      // 当前计算完成状态
      finish: true,
      keys: [
        // type 类型： Formula 计算公式; Fn 特殊函数（清空回退之类）; Number 纯数字键;
        // value 公式计算的值用来拼接或者函数用来区分
        // icon 图标
        {
          type: 'Fn',
          value: 'AC',
          icon: 'icon-AC'
        },
        {
          type: 'Fn',
          value: 'back',
          icon: 'icon-delete-back'
        },
        {
          type: 'Formula',
          value: '(',
          icon: 'icon-left-paren'
        },
        {
          type: 'Formula',
          value: ')',
          icon: 'icon-right-paren'
        },
        {
          type: 'Formula',
          value: '/',
          icon: 'icon-divide'
        },
        {
          type: 'Formula',
          value: 'ln(',
          icon: 'icon-ln'
        },
        // {
        //   type: 'Formula',
        //   value: '^(-1)',
        // },
        {
          type: 'Number',
          value: '7',
          icon: 'icon-seven'
        },
        {
          type: 'Number',
          value: '8',
          icon: 'icon-eight'
        },
        {
          type: 'Number',
          value: '9',
          icon: 'icon-nine'
        },
        {
          type: 'Formula',
          value: '*',
          icon: 'icon-multiply'
        },
        {
          type: 'Formula',
          value: 'lg(',
          icon: 'icon-log'
        },
        {
          type: 'Number',
          value: '4',
          icon: 'icon-four'
        },
        {
          type: 'Number',
          value: '5',
          icon: 'icon-five'
        },
        {
          type: 'Number',
          value: '6',
          icon: 'icon-six'
        },
        {
          type: 'Formula',
          value: '-',
          icon: 'icon-subtract'
        },
        {
          type: 'Formula',
          value: '^',
          icon: 'icon-Xy'
        },
        {
          type: 'Number',
          value: '1',
          icon: 'icon-one'
        },
        {
          type: 'Number',
          value: '2',
          icon: 'icon-two'
        },
        {
          type: 'Number',
          value: '3',
          icon: 'icon-three'
        },
        {
          type: 'Formula',
          value: '+',
          icon: 'icon-add1'
        },
        {
          type: 'Formula',
          value: '^2',
          icon: 'icon-square'
        },
        {
          type: 'Formula',
          value: '√(',
          icon: 'icon-square-root'
        },
        {
          type: 'Number',
          value: '0',
          icon: 'icon-zero'
        },
        {
          type: 'Formula',
          value: '.',
          icon: 'icon-point'
        },
        {
          type: 'Fn',
          value: 'equal',
          icon: 'icon-equal'
        },

      ],
      touchTimeout: null,
      // display是添加还是减少来放大缩小字号
      action: null
    };
  },
  computed: {
    hasResult() {
      return this.result || this.result === 0
    }
  },
  methods: {
    // 全部笼统的点击事件
    clickBtn(item) {
      switch (item.type) {
        // 数字和公式都是拼接好计算
        case "Formula":
          if(this.finish == true) {
            this.display = this.result 
            this.result = ''
          }
          this.finish = false
          this.action = 'plus'
          this.getFormula(item)
          this.updateFontsize()
          break;
        case "Number":
          if(this.finish == true) {
            this.clear()
          }
          this.finish = false
          this.action = 'plus'
          this.getFormula(item)
          this.updateFontsize()
          break;
        case "Fn":
          // 各种点击事件（清空、退格、等等）
          this.getClickFn(item)
          this.updateFontsize()
          break;
        default:
          break;
      }
    },
    // 公式相关按钮点击
    getFormula(item) {
      // 若是Error或者Infinity 置为
      if (this.display === 'Error' || this.display === Infinity) {
        
        this.display = ''
      }
      // 若是log或者ln函数或为数字，且display为0，需要先去除0
      if (item.icon == 'icon-log' || item.icon == 'icon-ln' || item.icon == 'icon-square-root' || item.icon == 'icon-left-paren' || item.type == 'Number') {
        if (this.display === 0) {
          this.display = ''
        }
      }
      this.display += item.value
      // this.display += `<span class="iconfont ${item.icon}" style="font-size: 36px"></span>`
      this.multipleMatch()
    },
    // 事件按钮点击
    getClickFn(item) {
      if (item.value === 'AC') {
        this.clear()
      } else if (item.value === 'back') {
        this.backspace()
      } else if (item.value === 'equal') {
        this.outputCheck()
      }
      this.action = 'minus'
    },
    // 清空按钮
    clear() {
      this.display = 0;
      this.result = ''
    },
    backspace() {
      // 回退按钮
      if (this.display === 0) return
      if (this.display.length == 1 || this.finish) {
        return this.clear()
      }
      this.display = this.display.toString().slice(0, -1)
    },
    outputCheck() {
      this.calculate()
      this.finish = true
    },
    // 滚动到最底部 最右边
    scrollRightAndBottom() {
      this.$nextTick(() => {
        this.$refs.inputWrap.scrollTop = this.$refs.inputWrap.scrollHeight;
        this.$refs.inputWrap.scrollLeft = this.$refs.inputWrap.scrollWidth;
      })
    },
    // 计算结果前处理校验
    calculate() {
      let tempCal = this.display.toString()
      // // 将ln、lg正则匹配替换成log，例子:将ln(6 + lg(33))替换成log(6+log(33,10),e)
      let regexLn = /ln\(((?:[^()]*|\((?:[^()]*|\([^()]*\))*\))*)\)/g
      let regexLg = /lg\(((?:[^()]*|\((?:[^()]*|\([^()]*\))*\))*)\)/g
      // 将√正则匹配替换成sqrt，例子:将√(9)替换成sqrt(9)
      let regexSqrt = /√\(((?:[^()]*|\((?:[^()]*|\([^()]*\))*\))*)\)/g
      tempCal = this.replaceStr(
        this.replaceStr(
          this.replaceStr(tempCal, regexLg, 'log(x, 10)'), regexLn, 'log(x, e)')
        , regexSqrt, 'sqrt(x)')
      try {
        if (!tempCal) return '0'
        // 如果是NaN或者Infinity则直接反错误
        if (isNaN(math.eval(tempCal))
          //  || math.eval(tempCal).toString().indexOf('Infinity') !== -1
        ) {
          this.display = 'Error'
          this.result = 'Error'
          return
        }
        this.result = parseFloat(math.format(math.eval(tempCal), {
          precision: 14
        })) || 0
        // this.result = math.eval(tempCal) || 0
      } catch (error) {
        console.log(error, 'errors');
        this.result = 'Error'
      }
    },
    // 递归校验多个log ln sqrt等多个括号叠在一起的情况
    replaceStr(tempCal, regex, logOrLn) {
      // 使用replace方法和回调函数进行替换
      return tempCal.replace(regex, (match, p1) => {
        // 对于每个匹配的 'lg或者ln' 函数调用，递归地替换内部的 'lg,ln'，然后转换为 'log' 函数调用
        let replaced = this.replaceStr(p1, regex, logOrLn);
        // 找到logOrLn中字符串x的位置插入替换成replaced变量
        return logOrLn.replace('x', replaced);
      });
    },
    // 多个连续符号匹配正则
    multipleMatch() {
      this.display = this.display.toString()
        .replace(/-{2,}/g, '-')
        .replace(/[+]{2,}/g, '+')
        .replace(/[/]{2,}/g, '/')
        .replace(/[*]{2,}/g, '*')
        .replace(/\.{2,}/g, '.')
        .replace(/\^{2,}/g, '^')
    },
    touchStart(item, i) {
      clearTimeout(this.touchTimeout);
      this.$set(this.keys[i], 'isHovered', true)
    },
    touchEnd(item, i) {
      this.touchTimeout = setTimeout(() => {
        this.$set(this.keys[i], 'isHovered', false)
      }); // 100ms后移除hover样式，以模拟hover出现时效果
    },
    getHoverStatus(item) {
      return item.isHovered ? item.icon != 'icon-equal' ? 'hovered' : 'hovered-end' : ''
    },
    // 计算过长更新字号
    updateFontsize() {
      this.$nextTick(() => {
        let tempRef = this.$refs.inputWrap
        let displayRef = this.$refs.displayRef
        let resultRef = this.$refs.resultRef
        displayRef.style.fontSize = '36px'
        displayRef.style.marginTop = '0'
        if(resultRef) {
          resultRef.style.fontSize = '36px'
          resultRef.style.marginTop = '10px'
        }
        // 正在输入
        if(!this.hasResult) {
          // 当display的宽度超过父元素时给字号缩小
          if(tempRef.scrollWidth > tempRef.clientWidth && this.action == 'plus') {
            displayRef.style.fontSize = '30px'
            displayRef.style.marginTop = '5px'
          } else if(tempRef.scrollWidth < tempRef.clientWidth && this.action == 'minus'){
            displayRef.style.fontSize = '36px'
            displayRef.style.marginTop = '0'
          }
        } else {
          displayRef.style.fontSize = '14px'
          displayRef.style.marginTop = '10px'
          if(resultRef) {
            if(tempRef.scrollWidth - 24 > tempRef.clientWidth) {
              resultRef.style.fontSize = '30px'
              resultRef.style.marginTop = '5px'
            } else {
              resultRef.style.fontSize = '36px'
              resultRef.style.marginTop = '0'
            }
          }
        }
        this.scrollRightAndBottom()
      })
    }
  },
}

</script>
<style lang="stylus" scoped>
.calculator-container {
  background-color: #F1F3F7;
  .input-wrap-container {
    margin: 12px 0;
    font-size: 36px;
    padding: 0 12px;
    text-align: right;
    background-color: #fff;
    font-weight: 600;
    border-radius: 4px;
    // 滚动条隐藏
    .input-wrap {
      height: 78px;
      min-width: 100%;
      display: inline-block;
      width: 100%;
      overflow-y: auto;
      -ms-overflow-style: none;  /* IE 和 Edge */
      scrollbar-width: none;  /* Firefox */

      >div {
        line-height: 1;
        word-break: break-all;
        white-space: nowrap;
        letter-spacing: 1px;
      }
      .has-res {
        font-size: 14px;
        line-height: 28px;
        color: #B1B1B1;
      }
      .no-res {
        min-height: 64px;
        padding-top: 38px;
      }
    }
    .input-wrap::-webkit-scrollbar {
      display: none;
    }
    .result, .display {
      white-space: nowrap;
      line-height: 38px;
    }
  }
  .calculator {
    text-align: center;
    .keys {
      // 使用网格布局四列六行布局
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-template-rows: repeat(5, 1fr);
      grid-gap: 7px;
      >div {
        height: 46px;
        line-height 46px;
        font-size: 24px;
        border: none;
        background-color: #E3E3E9;
        border-radius: 5px;
        position: relative;
        .iconfont {
          font-size: 24px;
          color: #000;
        }
        // &:hover {
        //   background-color: #fff;
        // }
        // &:active {
        //   background-color: #fff;
        // }
        // &:focus {
        //   background-color: #fff;
        // }
      }
      // 最后一个元素变蓝色
      >div:last-child { 
        background-color: #529FFF;
        .iconfont {
          color: #fff;
        }
      }
    }
  }
}
.hovered {
  background-color: #CCCCD2 !important; /* 鼠标悬停时的背景色 */
}
.hovered-end {
  background-color: #75b2ff !important; /* 鼠标悬停时的背景色 */
}

</style>