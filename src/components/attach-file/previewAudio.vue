<template>
  <div>
    <audio :id="id" class="custom-audio" width="100%" type="audio/mpeg" :src="src" @canplay="errorText = ''" @error="errorFun"></audio>
    <span class="error" v-if="errorText">{{ errorText }}</span>
  </div>
</template>
<script>
export default {
  props: ['src', 'id'],
  data() {
    return {
      errorText: ''
    }
  },
  methods: {
    errorFun(e) {
      this.errorText = this.$t('netWorkError')
      if (e.target.error && e.target.error.code === 4 && !e.target.error.message) {
        this.errorText = this.$t('filePlayTip')
      } 
      this.$saveLog(`${this.src}加载失败_${e.target.error ? JSON.stringify(e.target.error) : '网络错误'}`)
    }
  }
}
</script>
<style scoped>
.error {
  font-size: 14px;
  color: #F60000;
}
</style>
