<template>
  <component :is="types[type]" :src="sourceUrl" :id="id" :markedImage="markedImage" :angle="angle"></component>
</template>
<script>
import 'mediaelement'
import 'mediaelement/build/mediaelementplayer.min.css'
import PreviewAudio from './previewAudio'
import PreviewImage from './previewImage'
import PreviewVideo from './previewVideo'
// import { getMediaType } from './file.js'

import { mapState } from 'vuex'
export default {
  components: {
    PreviewAudio,
    PreviewImage,
    PreviewVideo
  },
  props: {
    src: String,
    id: String,
    limitCount: Number,
    type: String,
    markedImage: String,
    angle: Number,
    curTime: Number
  },
  data () {
    return {
      types: {
        'audio': 'PreviewAudio',
        'video': 'PreviewVideo',
        'image': 'PreviewImage'
      }
    }
  },
  computed: {
    ...mapState(['map']),
    sourceUrl () {
      if (this.type == 'video') {
        return this.src.substring(0, this.src.lastIndexOf('.')) + '.mp4'
      } else {
        return this.src
      }
    }
  },
  mounted () {
    if (this.id && this.type !== 'image') {
      /* eslint-disable-next-line */
      this.player = new MediaElementPlayer(this.id, {
        success: (mediaElement) => {
          this.media = mediaElement
          // 加载完成才能设置当前时间
          if(this.curTime){
            this.media.load()
          }
          this.media.setCurrentTime(this.curTime || mediaElement.getCurrentTime())
          this.map.set(this.media, {
            src: this.sourceUrl,
            currentTime: this.curTime || mediaElement.getCurrentTime()
          })
        },
        error: (event) => {
          console.log(event.detail)
          setTimeout(()=> {
             event.detail.target.parentElement.nextElementSibling.getElementsByClassName('mejs__cannotplay')[0].getElementsByTagName('a')[0].setAttribute('href','javascript:;')
          },300)
         
        }
        // stretching: 'fill'
      })
      // this.player.media.addEv
      this.$once('hook:beforeDestroy', () => {
        const { player } = this
        if (!player.paused) {
          player.pause()
        }
        // player.remove()
      })
    }
  }
}
</script>

<style lang="stylus">
.custom-audio
  /* 自定义音频 */
  position relative
  // width 240px !important
  border-radius 4px
  overflow hidden
  background transparent !important

.custom-audio .mejs__controls
  background #dee5ef !important

.custom-audio .mejs__button>button
  margin 12px 8px
  width 16px
  height 16px
  background transparent url('~@/assets/images/audio_images.png')
  background-repeat no-repeat
  background-size 100%

.custom-audio .mejs__play>button
  background-position 0px -49px

.custom-audio .mejs__pause>button
  background-position 0px -69px

.custom-audio .mejs__replay>button
  background-position 0px -25px

.custom-audio .mejs__loading>button
  background-position 0px -25px

.custom-audio .mejs__error>button
  background-position 0px 0

.custom-audio .mejs__currenttime-container
  display none

.custom-audio .mejs__time-rail
  padding-top 0
  width 115px

.custom-audio .mejs__time-total
  margin 18px 0 0
  height 4px
  border-radius 2px
  background #cdd3e2 !important
  outline none

.custom-audio .mejs__time-total>*
  height 100%

.custom-audio .mejs__time-buffering
  display none

.custom-audio .mejs__time-loaded
  background transparent !important

.custom-audio .mejs__time-current
  background #95a5bd

.custom-audio .mejs__time-handle
  top -4px

.custom-audio .mejs__time-handle-content
  width 12px
  height 12px
  border 1px solid #d3d9e7
  -webkit-transform scale(1)
  -ms-transform scale(1)
  transform scale(1)

.custom-audio .mejs__time-float
  display none !important

.custom-audio .mejs__duration-container::before
  content '- '

.custom-audio .mejs__duration-container
  font-weight normal
  color #314767

.custom-audio .mejs__volume-button, .custom-audio .mejs__horizontal-volume-slider
  display none

.custom-video
  width 100%
  height 100%
  max-width 100%
  max-height 100%

.custom-video .mejs__poster
  max-width 100%
</style>
