// 根据文件的扩展名或文件名 给出文件的类型
export const getMediaType = (suffix) => { 
  suffix = suffix.slice(suffix.lastIndexOf('.') + 1).toLowerCase()
  const types = {
    video: {
      format: 'mp4, mpg, mpeg, avi, rmvb, rm, wmv, mov, video, flv, mkv',
      size: 1024,
      transcoding: '自动转码(H.264编码的mp4格式)，切片(流媒体播放)，压缩(128~1024kbps)'
    },
    audio: {
      format: 'mp3, aac, wav, wma, ogg, m4a, audio',
      size: 50,
      transcoding: '自动转码(AAC编码的mp3格式)，切片(流媒体播放)，压缩'
    },
    image: {
      format: 'png, jpg, jpeg, gif, bmp, image', // 加image是为了兼容客户端传回来的内容
      size: 2,
      transcoding: '--'
    },
    doc: {
      format: 'doc, docx',
      size: 50,
      transcoding: '转图片'
    },
    xls: {
      format: 'xls, xlsx',
      size: 50,
      transcoding: '转图片'
    },
    pdf: {
      format: 'pdf',
      size: 50,
      transcoding: '转图片'
    },
    ppt: {
      format: 'ppt, pptx',
      size: 50,
      transcoding: '转图片'
    },
    zip: {
      format: 'zip, rar, 7z',
      size: 50,
      transcoding: '压缩'
    },
    txt: {
      format: 'txt',
      size: 50,
      transcoding: '文本'
    }
  }
  for (let type in types) {
    if (types.hasOwnProperty(type)) {
      const exts = types[type].format
      if (exts.indexOf(suffix) >= 0) {
        return type
      }
    }
  }
  return 'other'
}
export const getFileType =  (ext) => {
  var mime_types = [{
      title: "document",
      extensions: "doc,docx,xls,xlsx,ppt,pptx,pdf,txt",
    },
    {
      title: "image",
      extensions: "png,jpg,gif,jpeg,bmp",
    },
    {
      title: "video",
      extensions: "flv,mp4,avi,wmv,rm,rmvb,mpeg,mov,mpg,mkv",
    },
    {
      title: "audio",
      extensions: "mp3,wav,wma",
    },
    {
      title: "rar",
      extensions: "rar,zip,7z",
    },
  ];
  if (ext == "" || ext == null) {
    return "richtext";
  } else {
    ext = ext.toLowerCase();
  }
  for (var i = 0; i < mime_types.length; i++) {
    var extArr = mime_types[i].extensions.split(",");
    for (var j = 0; j < extArr.length; j++) {
      if (extArr[j] == ext) {
        return mime_types[i].title;
      }
    }
  }
  return "other";
}
// 根据给定的大小B转换成MB等单位
export const formatSize = (size) => {
  if (size === undefined || /\D/.test(size)) {
    return 'invalid data'
  }
  const round = (num, precision) => {
    return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision)
  }
  let boundary = Math.pow(1024, 4)
  // TB
  if (size > boundary) {
    return round(size / boundary, 1) + 'TB'
  }
  // GB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'GB'
  }
  // MB
  if (size > (boundary /= 1024)) {
    return round(size / boundary, 1) + 'MB'
  }
  // KB
  if (size > 1024) {
    return Math.round(size / 1024) + 'KB'
  }
  if (size === '') {
    return ''
  }
  return size + 'b'
}

export const fileIcon =(input) => {
  var icon = "icon-fujianleixingqita";
  var fileType = {
    img: ["png", "jpg", "gif", "jpeg", "bmp"],
    doc: ["ppt", "pptx", "doc", "docx", "xls", "xlsx", "pdf", "txt", "epub", "pps", "psd", "xmind", "dwg"],
    audio: ["mp3", "wav"],
    video: ["mp4", "avi", "wmv", "rm", "rmvb", "mpeg", "mpg", "mov", "flv", "mkv"],
    zip: ["zip", "rar", "7z"],
    other: ["swf", "html"],
  };
  var type = 50;
  if (fileType.doc.includes(input, )) {
    type = 2;
  } else if (fileType.img.includes(input)) {
    type = 3;
  } else if (fileType.video.includes(input)) {
    type = 4;
  } else if (fileType.audio.includes(input)) {
    type = 5;
  } else if (fileType.zip.includes(input)) {
    type = 6;
  }
  switch (type) {
    case 2:
      switch (input) {
        case "txt":
          icon = "icon-fujianleixingtxt";
          break;
        case "pdf":
          icon = "icon-fujianleixingpdf";
          break;
        case "doc":
          icon = "icon-fujianleixingword";
          break;
        case "docx":
          icon = "icon-fujianleixingword";
          break;
        case "ppt":
          icon = "icon-fujianleixingppt";
          break;
        case "pptx":
          icon = "icon-fujianleixingppt";
          break;
        case "xls":
          icon = "icon-fujianleixingexcel";
          break;
        case "xlsx":
          icon = "icon-fujianleixingexcel";
          break;
        default:
          icon = "icon-fujianleixingqita";
      }
      break;
    case 3:
      icon = "icon-fujianleixingtupian";
      break;
    case 4:
      icon = "icon-fujianleixingshipin";
      break;
    case 5:
      icon = "icon-fujianleixingyinpin";
      break;
    case 6:
      icon = "icon-fujianleixingyasuobao";
      break;
    case 50:
      icon = "icon-fujianleixingqita";
      break;
    default:
  }
  return icon;
}

// 限制上传类型
export function fileTypeLimit(suffix) {
  suffix = suffix.slice(suffix.lastIndexOf('.') + 1).toLowerCase()
  const MIME_TYPE = {
    document: "doc,docx,xls,xlsx,ppt,pptx,pdf,txt,epub,pps,psd,xmind,dwg",
    image: "png,jpg,gif,jpeg,bmp",
    video: "mp4,avi,wmv,rm,rmvb,mpeg,mpg,mkv,mov,flv",
    audio: "mp3,wav,wma",
    zip: "zip,rar,7z",
    flash: "swf",
    srt: "srt",
    favico: "ico"
  }
  for (const key in MIME_TYPE) {
    const element = MIME_TYPE[key];
    if (element.includes(suffix)) {
      return key
    }
  }
  return ''
}