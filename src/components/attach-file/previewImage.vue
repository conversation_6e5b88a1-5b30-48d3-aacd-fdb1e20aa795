<template>
  <div>
    <img class="image" :src="image" @click="preview" :preview="id" :style="styleObject">
  </div>
</template>
<script>
// import Vue from 'vue'
// import VuePreview from 'vue-photo-preview'
// import 'vue-photo-preview/dist/skin.css'

// /* eslint-disable no-new */
// Vue.use(VuePreview, {
//   fullscreenEl: false,
//   tapToClose: true,
//   tapToToggleControls: false,
//   showHideOpacity: true,
//   showAnimationDuration: 200,
//   hideAnimationDuration: 200,
//   bgOpacity: 0.85
// })
export default {
  props: ['src', 'id', 'markedImage', 'angle'],
  computed: {
    image () {
      if (this.markedImage) {
        return this.markedImage
      }
      return this.src
    },
    newSrc() {
      if (this.angle) {
        return this.src.split("?")[0] + "?imageslim|imageMogr2/rotate/" + this.angle;
      }
      return this.src;
    },
    // bgImage () {
    //   if (this.markedImage) {
    //     return this.src
    //   }
    //   return this.markedImage
    // },
    styleObject () {
      let obj = {}
      if (this.markedImage) {
        obj.backgroundImage = 'url(' + this.newSrc + ')'
      }
      return obj
    }
  },
  methods: {
    preview () {
      var pre = this.$createImagePreview({
        // imgs: [this.markedImage + '?imageView2/0/format/jpg']
        imgs: [this.image]
      }, (h) => {
        return h('div', {
          slot: 'footer'
        }, '')
      })
      pre.show()
      if (this.markedImage) {
        setTimeout(() => {
          var dom = pre.$el.getElementsByClassName('cube-image-preview-img')[0]
          dom.style.backgroundImage = 'url(' + this.newSrc + ')'
          dom.style.backgroundSize = 'contain'
        })
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.image
  background-position center
  background-repeat no-repeat
  background-size contain
</style>
