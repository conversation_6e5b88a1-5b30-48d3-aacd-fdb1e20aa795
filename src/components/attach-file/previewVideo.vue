<!--
 * @Date: 2020-06-10 10:13:34
 * @LastEditors: liuzhong
 * @LastEditTime: 2021-02-01 15:53:41
 * @FilePath: \es6e:\work-project\face\exam\branches\bugfix\src\components\attach-file\previewVideo.vue
-->
<template>
<div class="video-wrapper">
  <video :id="id" width="100%" height="100%" playsinline webkit-playsinline  class="custom-video" @error="errorFun">
    <source type="video/mp4" :src="mp4Url" />
  </video>
</div>
  
</template>
<script>
export default {
  props: ['src', 'id'],
  computed: {
    mp4Url () {
      return this.src.substring(0,this.src.lastIndexOf('.')) + '.mp4'
    }
  },
  methods: {
    errorFun(e) {
      this.$saveLog(`${this.src}加载失败_${e.target.error ? JSON.stringify(e.target.error) : '网络错误'}`)
    }
  }
}
</script>
<style lang="stylus" scoped>
.video-wrapper
  width 100%
  height 180px
</style>