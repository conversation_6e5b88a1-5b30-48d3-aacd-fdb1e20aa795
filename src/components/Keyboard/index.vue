<template>
  <transition name="slide-up">
    <div v-show="show" class="keyboard-body" :class="{ 'words-open': isOpen }" :style="{ 'z-index': zIndex }"
      @animationend="animationEnd" @touchstart.passive.stop @click.stop>
      <div class="finish-wrapper">
        <button type="button" class="button icon-wrap left" @click="changeGrid">
          <img class="icon" :src="require('./svg/grid.svg')" alt="grid" />
          <span class="text">{{ isJiuGrid ? '九' : '全' }}</span>
        </button>
        <div ref="inputContentScroll" class="input-content left">
          <div class="content">{{ inputValue }}</div>
        </div>
        <button type="button" class="button right" @click="press('', 'finish')">完成</button>
      </div>
      <div class="pinyins-wrapper" v-if="!isNativeApp && pinyinGroups.firstGroups && pinyinGroups.firstGroups.length > 0">
        <div class="pinyins" ref="pinyinScroll">
          <div class="pinyins-detail">
            <Key class="word" v-for="(pinyin, index) in pinyinGroups.firstGroups"
              :class="{ 'active': curPinyin === pinyin }" :key="'pinyin' + index" :text="pinyin"
              @press="changePinyin" />
          </div>
        </div>
      </div>
      <div v-if="lang === 'zh'" class="words-wrapper">
        <template v-if="words.length > 0">
          <div class="words" ref="scroll">
            <div class="words-detail">
              <Key class="word" v-for="(word, index) in wordList" :key="'word_' + index" :text="word.text || word"
                :type="word.type" :item="{ icon: wordIcons[index] }" @press="updateWord" />
            </div>
          </div>
          <img class="show-more" :src="require('./svg/zhankai.svg')" alt="zhankai" @click="isOpen = !isOpen" />
        </template>
      </div>
      <template v-if="!isOpen">
        <div class="keys-wrap" v-if="isJiuGrid && lang === 'zh' && mode !== 3">
          <div class="keys-column" v-for="(items, index) in keys" :key="'items' + index">
            <Key ref="keys" class="jiu-btn" v-for="(key, index) in items" :key="mode + index" :text="changeText(key)"
              :type="key.type" :item="key" :isPanend="true" @press="press" />
          </div>
        </div>
        <div class="keys" v-else>
          <Key ref="keys" v-for="(key, index) in keys" :key="mode + index" :text="changeText(key)" :type="key.type"
            :item="key" :isPanend="true" @press="press" />
        </div>
      </template>
    </div>
  </transition>
</template>
<script>
import { getPlatform } from '@/common/utils'
import Key from "./Key.vue"
import { getKeys, getWords, getSymbols, getPinyinGroups, getWordsBreak, throttle } from "./js/keyboard.js"
import BScroll from '@better-scroll/core'
import ScrollBar from '@better-scroll/scroll-bar'
import MouseWheel from '@better-scroll/mouse-wheel'
import {
  getKeysResult,
  switchInputMode,
  keysReset,
} from "@/lib/cordovaPlugin/mobilePlugin.js";
BScroll.use(ScrollBar)
BScroll.use(MouseWheel)

export default {
  name: "KeyboardComponent",
  components: {
    Key
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: Number,
      default: 100
    },
    value: {
      type: String,
      default: ''
    },
    // 是否可以换行
    canEnter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lang: "zh",
      isCaps: true,
      isJiuGrid: true,
      mode: 1, // 1 正常 2 数字 3 符号 4 九宫格数字模式更多
      words: [],
      wordList: [],
      isWordsChange: false,
      keys: [],
      inputValue: "",
      focus: false,
      isOpen: false,
      scroll: null,
      inputContentScroll: null,
      pinyinScroll: null,
      pinyinGroups: {},
      pinyinInputs: [],
      wordIcons: [],
      curPinyin: "",
      eventName: getPlatform().isMobile ? 'touchstart' : 'click',
      keyPressText: ""
    }
  },
  computed: {
    // 检查是否为原生应用环境
    isNativeApp() {
      return navigator.userAgent.includes('umoocApp')
    }
  },
  watch: {
    show: {
      handler() {
        if (this.show) {
          this.lang = 'zh';
          this.changeMode(1);
          this.clearData()
        } else {
          this.clearLongPress()
        }
      },
      immediate: true
    },
    inputValue: {
      handler(val, oldval) {

        console.log("inputValue", val);


        // 原生环境下不监听 inputValue 变化
        if (!this.isNativeApp && this.lang === 'zh' && val.length > 0) {
          if (val != "" && val.length > oldval.length) {
            // 中文词库
            !this.isJiuGrid && this.getWords()
          }

          // 添加滚动条（仅非原生环境）
          if (!this.isNativeApp) {
            this.$nextTick(() => {
              if (!this.inputContentScroll) {
                this.inputContentScroll = new BScroll(this.$refs.inputContentScroll, {
                  freeScroll: true,
                  scrollbar: true
                })
              } else {
                this.scrollRefresh('inputContentScroll')
              }
            })
          }
        } else {
          this.words = []
          this.wordList = []
          // 非原生环境才清空拼音分组
          if (!this.isNativeApp) {
            this.pinyinGroups = {}
          }
          this.scrollDestroy('inputContentScroll')
        }
      },
      immediate: true
    },
    pinyinInputs: {
      handler(newVal) {
        console.log("pinyinInputs", newVal);
        
        // 原生环境下跳过拼音处理
        if (this.isNativeApp) {
          return
        }
        
        // 非原生环境：执行JS拼音处理
        this.changePinyinGroups()

        if (this.pinyinGroups.firstGroups && this.pinyinGroups.firstGroups.length > 0) {
          this.$nextTick(() => {
            if (!this.pinyinScroll) {
              this.pinyinScroll = new BScroll(this.$refs.pinyinScroll, {
                freeScroll: true,
                scrollbar: true
              })
            } else {
              this.scrollRefresh('pinyinScroll')
            }
          })
        } else {
          this.inputValue = ''
          this.scrollDestroy('pinyinScroll')
        }
      },
      deep: true,
      immediate: true
    },
    isOpen: {
      handler() {
        // 原生环境下简化处理
        if (this.isNativeApp) {
          this.wordList = this.words
        } else {
          this.showAllwords()
        }
      }
    },
    words: {
      handler(val) {
        // 原生环境下简化处理
        if (this.isNativeApp) {
          return
        }
        
        if (val.length > 0) {
          this.isWordsChange = true
          this.$nextTick(() => {
            if (!this.scroll) {
              this.scroll = new BScroll(this.$refs.scroll, {
                freeScroll: true,
                scrollbar: true,
                mouseWheel: {
                  speed: 20,
                  invert: false,
                  easeTime: 300
                },
                click: true,
                taps: true,
                probeType: 3
              })
              this.scroll.on('scroll', () => {
                if (!this.isWordsChange && this.words.length !== this.wordList.length) {
                  this.showAllwords()
                }
              })
            } else {
              this.scrollRefresh('scroll', true)
            }
            this.isWordsChange = false
          })
        } else {
          this.scrollDestroy('scroll')
        }
      },
      immediate: true
    }
  },
  created() {
    this.keys = getKeys(this.mode, this.lang, this.isJiuGrid)
    document.addEventListener(this.eventName, this.blur)
  },
  mounted() {

  },
  methods: {
    changeGrid() {
      this.update(this.inputValue);
      this.words = [];
      this.isJiuGrid = !this.isJiuGrid;
      this.lang = 'zh';
      this.changeMode(1);
      if (navigator.userAgent.includes('umoocApp')) {
        this.keyPressText = ""
        switchInputMode(this.isJiuGrid ? [1] : [0], () => {
          console.log('switchInputMode success');
        }, err => {
          console.log('switchInputMode error', err);
        })
      }
    },
    changeText(key) {
      if (key.type === 'space') {
        return this.inputValue.length > 0 ? '选定' : '空格'
      }
      if (key.type === 'confirm') {
        return this.inputValue.length > 0 ? '确认' : '换行'
      }
      if (key.type === 'letter') {
        key.text = this.isCaps ? key.text.toUpperCase() : key.text.toLowerCase()
      }
      return key.text
    },
    animationEnd() {
      this.$emit(this.show ? 'show' : 'blur')
    },
    confirmValue() {
      if (this.inputValue.length > 0) {
        const value = this.words.length > 0 ? this.words[0] : this.inputValue
        this.update(value);
      }
    },
    changeMode(mode) {
      const startTime = performance.now()
      console.log(`🔧 changeMode开始: mode=${mode}, lang=${this.lang}, isJiuGrid=${this.isJiuGrid}`)
      
      this.mode = mode;
      this.isCaps = this.lang === 'zh';
      // 必须重新生成键盘布局，因为每次模式切换都可能改变布局
      this.keys = getKeys(this.mode, this.lang, this.isJiuGrid)
      
      console.log(`⚡ changeMode完成: ${performance.now() - startTime}ms, keys数量: ${this.keys.length}`)
    },
    press(text, type, value, taps) {
      const startTime = performance.now()
      console.log(`🚀 Press开始: ${text}, type: ${type}, 时间戳: ${startTime}`)

      const beforeSwitch = performance.now()
      console.log(`🔀 Switch开始: ${beforeSwitch - startTime}ms`)
      
      switch (type) {
        case 'caps':
          console.log(`⚡ Caps处理: ${performance.now() - startTime}ms`)
          this.isCaps = !this.isCaps;
          break;
        case 'delete': {
          const length = this.inputValue.length
          if (length > 0) {
            if (this.isNativeApp) {
              // 原生环境：真正异步删除，不阻塞用户操作
              console.log(`🔍 原生删除开始: ${performance.now() - startTime}ms`)
              setTimeout(() => {
                this.getKeysResultFn([type]).then(words => {
                  console.log(`✅ 原生删除完成: ${performance.now() - startTime}ms, 候选词数: ${words ? words.length : 0}`)
                  this.words = words
                  this.wordList = this.isOpen ? this.words : this.words.slice(0, 20)
                })
              }, 0)
            } else {
              // 非原生环境：使用JS拼音逻辑
              if (this.isJiuGrid) {
                this.curPinyin = ""
                this.pinyinInputs.splice(this.pinyinInputs.length - 1)
              } else {
                this.inputValue = this.inputValue.substring(0, length - 1)
              }
            }
          } else {
            this.$emit('delete')
          }
          break;
        }
        case 'lang':
          console.log(`🌐 语言切换开始: ${performance.now() - startTime}ms`)
          this.update(this.inputValue);
          this.words = [];
          this.lang = this.lang === 'zh' ? 'en' : 'zh';
          console.log(`🔧 调用changeMode前: ${performance.now() - startTime}ms`)
          this.changeMode(1);
          console.log(`✅ 语言切换完成: ${performance.now() - startTime}ms`)
          break;
        case 'en': {
          let value = ' '
          if (this.inputValue.length > 0) {
            value = this.words.length > 0 ? this.words[0] : this.inputValue
          }
          this.update(value);
          this.words = [];
          this.lang = 'en';
          this.changeMode(1);
          break;
        }
        case 'number':
          console.log(`🔢 数字模式切换: ${performance.now() - startTime}ms`)
          this.confirmValue();
          this.changeMode(2);
          console.log(`✅ 数字模式完成: ${performance.now() - startTime}ms`)
          break;
        case 'symbol':
          console.log(`🔣 符号模式切换: ${performance.now() - startTime}ms`)
          this.confirmValue();
          this.changeMode(3);
          console.log(`✅ 符号模式完成: ${performance.now() - startTime}ms`)
          break;
        case 'back':
          console.log(`🔙 返回模式切换: ${performance.now() - startTime}ms`)
          this.changeMode(1);
          console.log(`✅ 返回模式完成: ${performance.now() - startTime}ms`)
          break;
        case 'more':
          this.changeMode(this.mode === 2 ? 4 : 2);
          break;
        case 'space': {
          let value = ' '
          if (this.inputValue.length > 0) {
            value = this.words.length > 0 ? this.words[0] : this.inputValue
          }
          this.update(value);
          break;
        }
        case 'confirm':
          this.update(this.inputValue.length > 0 ? this.inputValue : this.canEnter ? '\n' : '');
          break;
        case 'finish':
          this.update(this.inputValue);
          this.blur()
          break;
        default:
          const beforeDefault = performance.now()
          console.log(`🎯 Default分支开始: ${beforeDefault - startTime}ms`)
          
          this.keyPressText = text
          console.log(`📝 设置keyPressText: ${performance.now() - startTime}ms`)
          
          if (value && value.length > 0) {
            console.log(`🔤 MultiValue处理: ${performance.now() - startTime}ms`)
            this.setMultiValue(value, taps)
            console.log(`✅ MultiValue完成: ${performance.now() - startTime}ms`)
          } else {
            if (this.isNativeApp && this.lang === 'zh') {
              // 原生环境：真正异步获取候选词，不阻塞用户输入
              const beforeNative = performance.now()
              console.log(`📝 原生输入开始: ${beforeNative - startTime}ms, 按键: ${this.keyPressText}`)
              
              // 使用setTimeout确保真正异步执行
              setTimeout(() => {
                this.getKeysResultFn([this.keyPressText]).then(words => {
                  console.log(`✅ 原生输入完成: ${performance.now() - startTime}ms, 候选词数: ${words ? words.length : 0}`)
                  this.words = words
                  this.wordList = this.isOpen ? this.words : this.words.slice(0, 20)
                }).catch(err => {
                  console.error(`❌ 原生输入出错: ${performance.now() - startTime}ms`, err)
                })
              }, 0)
              
              console.log(`🚀 原生接口调用完成(异步): ${performance.now() - startTime}ms`)
            } else {
              // 非原生环境：执行原有逻辑
              console.log(`🌐 非原生环境处理: ${performance.now() - startTime}ms`)
              if (this.lang === 'zh') {
                text = text.toLowerCase()
              }
              this.inputValue += (text || '');
              if (this.mode !== 1 || this.lang !== 'zh') {
                this.update(this.inputValue)
              }
            }
          }
          
          console.log(`✅ Default分支完成: ${performance.now() - startTime}ms`)
      }
      
      const afterSwitch = performance.now()
      console.log(`🔚 Switch完成: ${afterSwitch - startTime}ms`)
      
      if (type !== 'delete') {
        const beforeClear = performance.now()
        console.log(`🧹 ClearLongPress调用前: ${beforeClear - startTime}ms`)
        this.clearLongPress()
        const afterClear = performance.now()
        console.log(`✅ ClearLongPress调用后: ${afterClear - startTime}ms, 方法耗时: ${afterClear - beforeClear}ms`)
      }
      
      const endTime = performance.now()
      console.log(`🏁 Press总耗时: ${endTime - startTime}ms`)
    },
    clearLongPress() {
      const startTime = performance.now()
      console.log(`🧹 clearLongPress开始, 原生环境: ${this.isNativeApp}`)
      
      // 原生环境下简化处理，减少DOM遍历
      if (this.isNativeApp) {
        console.log(`⚡ clearLongPress跳过: ${performance.now() - startTime}ms`)
        return
      }
      try {
        const { keys } = this.$refs
        const keyCount = keys ? keys.length : 0
        console.log(`🔍 遍历按键数量: ${keyCount}`)
        
        keys && keys.forEach(it => {
          it.clearLongPress()
        })
        
        console.log(`⚡ clearLongPress完成: ${performance.now() - startTime}ms`)
      } catch (e) {
        console.log('clearLongPress error:', e)
      }
    },
    blur() {
      this.isOpen = false
      this.inputValue = ''
      this.show && this.$emit('blur')
    },
    async clearData() {
      const startTime = performance.now()
      console.log(`🧹 clearData开始`)
      
      this.inputValue = ''
      this.keyPressText = ""
      this.isOpen = false
      this.words = []
      this.wordList = []
      this.wordIcons = []
      console.log(`📝 数据清理完成: ${performance.now() - startTime}ms`)
      
      // 仅在非原生环境下清空拼音相关数据
      if (!this.isNativeApp) {
        this.pinyinGroups = {}
        this.pinyinInputs = []
        console.log(`🔤 拼音数据清理: ${performance.now() - startTime}ms`)
      }
      
      console.log(`✅ clearData完成: ${performance.now() - startTime}ms`)
    },
    update(item) {
      const startTime = performance.now()
      console.log(`🔄 update开始: item=${item}`)
      
      console.log(`📤 emit update前: ${performance.now() - startTime}ms`)
      this.$emit('update', item);
      console.log(`📤 emit update后: ${performance.now() - startTime}ms`)
      
      console.log(`🧹 调用clearData前: ${performance.now() - startTime}ms`)
      this.clearData()
      console.log(`✅ update完成: ${performance.now() - startTime}ms`)
    },
    async updateWord(text, type) {
      type === "symbol" && this.$emit('delete')
      this.update(text)
      if (type === "symbol" && text.length === 2 && text.indexOf('°') === -1 && text.indexOf('…') === -1) {
        this.$emit('positionBack')
      }
      if (navigator.userAgent.includes('umoocApp')) {
        this.keyPressText = ""
        await this.keyBoardResetFn()
        switchInputMode(this.isJiuGrid ? [1] : [0], () => {
          console.log('switchInputMode success');
        }, err => {
          console.log('switchInputMode error', err);
        })
      }
    },
    setMultiValue(value, taps) {
      const startTime = performance.now()
      console.log(`🔤 setMultiValue开始: taps=${taps}, value=${JSON.stringify(value)}`)
      
      if (taps > 0) {
        if (taps > 1) {
          console.log(`🔄 多次点击处理: ${performance.now() - startTime}ms`)
          const length = value.length
          const index = taps % length > 0 ? taps % length : length
          console.log(`📤 emit delete: ${performance.now() - startTime}ms`)
          this.$emit('delete')
          console.log(`🔄 调用update: ${performance.now() - startTime}ms`)
          this.update(value[index - 1])
          console.log(`✅ 多次点击完成: ${performance.now() - startTime}ms`)
        } else {
          console.log(`🔣 单次点击-获取符号: ${performance.now() - startTime}ms`)
          const data = getSymbols(this.mode)
          console.log(`✅ getSymbols完成: ${performance.now() - startTime}ms, 符号数: ${data.list ? data.list.length : 0}`)
          
          let curValue = ''
          if (this.inputValue.length > 0) {
            curValue = this.words.length > 0 ? this.words[0] : this.inputValue
          }
          console.log(`🔄 调用update前: ${performance.now() - startTime}ms`)
          this.update(curValue);
          console.log(`🔄 调用update后: ${performance.now() - startTime}ms`)
          
          this.$nextTick(() => {
            console.log(`⏰ $nextTick执行: ${performance.now() - startTime}ms`)
            this.words = data.list
            this.wordIcons = data.icons || []
            this.wordList = this.words.slice(0, 20)
            this.$emit('update', value[0]);
            console.log(`✅ $nextTick完成: ${performance.now() - startTime}ms`)
          })
        }
        this.inputValue = ''
      } else if (this.pinyinInputs.length > 0 || value[0] !== "'") {
        console.log(`🎯 拼音输入处理: ${performance.now() - startTime}ms`)
        this.curPinyin = ""
        if (this.isNativeApp) {
          // 原生环境：真正异步获取候选词
          console.log(`📱 原生环境异步调用: ${performance.now() - startTime}ms`)
          setTimeout(() => {
            this.getKeysResultFn([this.keyPressText]).then(words => {
              console.log(`✅ 原生异步完成: ${performance.now() - startTime}ms`)
              this.words = words
              this.wordList = this.isOpen ? this.words : this.words.slice(0, 20)
            })
          }, 0)
        } else {
          // 非原生环境：处理拼音输入
          console.log(`🌐 非原生拼音处理: ${performance.now() - startTime}ms`)
          this.pinyinInputs.push(value)
        }
      }
      
      console.log(`🏁 setMultiValue完成: ${performance.now() - startTime}ms`)
    },
    changePinyinGroups() {
      if (this.isJiuGrid && this.lang === 'zh' && this.mode === 1) {
        // 原生环境下跳过JS拼音分组处理
        if (this.isNativeApp) {
          return
        }
        
        if (this.pinyinInputs.length <= 24) {
          this.pinyinGroups = getPinyinGroups(this.pinyinInputs)
          this.getWords()
        } else {
          for (let i = this.inputValue.length; i < this.pinyinInputs.length; i++) {
            this.inputValue += this.pinyinInputs[i][0]
          }
        }
      }
    },
    changePinyin(value) {
      console.log("changePinyin", value);
      this.curPinyin = value
      
      // 原生环境下跳过JS拼音切换处理
      if (this.isNativeApp) {
        return
      }
      
      const groups = this.pinyinGroups.groups.filter(it => it.indexOf(value) === 0)
      this.getWords(groups, true)
    },

    keyBoardResetFn: () => {

      return new Promise((resolve, reject) => {
        keysReset(
          (res) => {
            return resolve(res);
          },
          (err) => {
            console.log("这是报错", err);
            return reject(err);
          }
        );
      });
    },
    getKeysResultFn: (data) => {
      return new Promise((resolve, reject) => {
        getKeysResult(
          data,
          (res) => {
            return resolve(res);
          },
          (err) => {
            console.log("这是报错", err);
            return reject(err);
          }
        );
      });
    },

    async getWords(groups, isChange) {
      // 原生环境不再使用这个方法，直接在按键处理中调用原生接口
      if (this.isNativeApp) {
        return
      }
      
      let list = []
      let words = {}
      
      // 非原生环境：使用JS拼音处理
      if (this.isJiuGrid) {
        list = groups || this.pinyinGroups.groups || []
        words = getWords(list, this.pinyinInputs)
        this.inputValue = words.firstPinyin || ""
      } else {
        list = getWordsBreak(this.inputValue) || []
        words = getWords(list)
      }
      this.words = words.words || []
      this.wordList = isChange && this.isOpen ? this.words : this.words.slice(0, 20)
    },
    showAllwords() {
      this.wordList = this.words
      // 原生环境下不需要滚动处理
      if (!this.isNativeApp) {
        this.$nextTick(() => {
          this.scrollRefresh('scroll')
        })
      }
    },
    scrollRefresh(scrollName, isInitPos) {
      if (this[scrollName]) {
        isInitPos && this[scrollName].scrollTo(0, 0, 0)
        this[scrollName].refresh()
      }
    },
    scrollDestroy(scrollName) {
      if (this[scrollName]) {
        this[scrollName].scrollTo(0, 0, 0)
        this[scrollName].destroy()
        this[scrollName] = null
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener(this.eventName, this.blur)
  }
}
</script>
<style lang="stylus" scoped>
@-webkit-keyframes slide-up-enter {  
  from {    
    -webkit-transform: translate3d(-50%, 100%, 0);            
    transform: translate3d(-50%, 100%, 0);  
  }
}
@keyframes slide-up-enter {  
  from {    
    -webkit-transform: translate3d(-50%, 100%, 0);            
    transform: translate3d(-50%, 100%, 0);  
  }
}
@-webkit-keyframes slide-up-leave {  
  to {    
    -webkit-transform: translate3d(-50%, 100%, 0);            
    transform: translate3d(-50%, 100%, 0);  
  }
}
@keyframes slide-up-leave {  
  to {    
    -webkit-transform: translate3d(-50%, 100%, 0);            
    transform: translate3d(-50%, 100%, 0);  
  }
}
.slide-up-enter-active {
  -webkit-animation: slide-up-enter 0.3s both ease-out;
  animation: slide-up-enter 0.3s both ease-out;
}
.slide-up-leave-active {
  -webkit-animation: slide-up-leave 0.3s both ease-in;
  animation: slide-up-leave 0.3s both ease-in;
}
.keyboard-body {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 680px;
  padding-bottom: 5px;
  background-color: #f5f5f5;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  .finish-wrapper {
    line-height: 1.2;
    background-color: #E3E3E3;
    &:before,
    &:after {
      content: " ";
      display: table;
    }
    &:after {
      clear: both;
    }
    .left {
      float: left;
    }
    .right {
      float: right;
    }
    .button {
      margin: 0;
      min-width: unset;
      height: auto;
      padding: 7px 12px;
      font-size: 16px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      cursor: pointer;
      background: transparent;
      border-color: transparent;
      transition: .1s;
      box-sizing: border-box;
      outline: none;
      user-select: none;
      color: #444444;
      &:focus {
        opacity: 0.6;
      }
      .icon {
        width: 16px;
        height: 16px;
        vertical-align: middle;
      }
      .text {
        margin-left: 2px;
        font-size: 14px;
        color: #969696;
        vertical-align: middle;
      }
    }
    .input-content {
      position: relative;
      max-width: calc(100% - 125px);
      overflow: hidden;
      .content {
        display: inline-block;
        padding: 8px 5px;
        box-sizing: border-box;
        font-size: 15px;
        letter-spacing: 1px;
      }
    }
  }
  .pinyins-wrapper {
    position: relative;
    min-height: 37px;
    padding: 5px 5px 0;
    border-bottom: 1px solid #e3e3e9;
    .pinyins {
      position: relative;
      width: 100%;
      line-height: 1.5;
      overflow: hidden;
      .pinyins-detail {
        display: inline-block;
        white-space: nowrap;
        font-size: 0;
      }
    }
  }
  .words-wrapper {
    position: relative;
    min-height: 37px;
    padding: 5px 5px 0;
    .words {
      position: relative;
      width: calc(100% - 30px);
      line-height: 1.5;
      overflow: hidden;
      .words-detail {
        display: inline-block;
        white-space: nowrap;
        font-size: 0;
      }
    }
    .show-more {
      position: absolute;
      right: 5px;
      top: 6px;
      padding: 5px;
      width: 18px;
      height: 18px;
      box-sizing: content-box;
      cursor: pointer;
    }
  }
  .keys {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0 2.5px;
  }
  .keys-wrap {
    display: flex;
    justify-content: center;
    .keys-column {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      justify-content: center;
      width: 20%;
      padding: 0 2.5px;
      &:nth-child(2) {
        flex-direction: row;
        width: 60%;
      }
    }
  }
  &.words-open {
    .words-wrapper {
      .words {
        width: 100%;
        height: 277px;
        padding-right: 0;
        .words-detail {
          padding-right: 28px;
          white-space: normal;
        }
      }
      .show-more {
        right: 10px;
        transform: rotateX(180deg);
      }
    }
    .keys {
      display: none;
    }
  }
}
</style>