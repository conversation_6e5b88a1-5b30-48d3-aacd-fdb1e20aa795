<template>
  <div  
    class="wrapper" 
    :class="[item.color, item.css]">
    <v-touch 
      tag="div"
      class="btn"
      :class="{'active': active}"
      @tap="onTap"
      @press="onPress"
      @pressup="end"
      @panstart="active = true"
      @panend="panend"
    >
      <img v-if="item.icon" class="icon" :src="require('./svg/' + item.icon + '.svg')" alt="svg" @contextmenu.prevent/>
      <span v-else>{{ text }}</span>
    </v-touch>
  </div>
</template>
<script>
import keycode from './js/keycode.js'
export default {
  name: "KeyboardKey",
  props: {
    type: {
      type: String,
      default: ''
    },
    text: {
      type: [Number, String],
      default: ""
    },
    item: {
      type: Object,
      default() {
        return {}
      }
    },
    isPanend: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      active: false,
      longPress: null,
      longPressInterval: null,
      taps: 0,
      timeout: null
    }
  },
  created() {
    document.addEventListener("keydown", this.onKeydown)
    document.addEventListener("keyup", this.end)
    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener("keydown", this.onKeydown)
    })
  },
  methods: {
    checkLongPress() {
      this.clearLongPress()
      this.longPress = setTimeout(() => {
        this.commitData()
        this.longPressInterval = setInterval(() => {
          this.commitData()
        }, 200)
      }, 500)
    },
    clearLongPress() {
      if (this.longPress) {
        clearTimeout(this.longPress)
        this.longPress = null
      }
      if (this.longPressInterval) {
        clearInterval(this.longPressInterval)
        this.longPressInterval = null
      }
    },
    end(e) {
      this.clearLongPress()
      if (this.active) {
        e.preventDefault()
        this.active = false
        this.commitData()
      }
    },
    panend(e) {
      if (this.isPanend) {
        this.end(e)
      } else {
        this.active = false
      }
    },
    onKeydown(e) {
      e = e || window.event;
      const key = e.which || e.keyCode || 0;
      let text = ''
      let type = ''
      let keyText = keycode.textKey[key]
      if (!keyText) {
        keyText = keycode.doubleTextKey[key]
        if (keyText) {
          text = e.shiftKey ? keyText.substring(1) : keyText.substring(0, 1)
        } else {
          keyText = keycode.actionKey[key]
          switch(keyText) {
            case "Enter": type = 'confirm'; break;
            case "BackSpace": type = 'delete'; break;
            case "Cape Lock": type = 'caps'; break;
            case "Spacebar": type = 'space'; break;
            case "Delete": type = 'delete'; break;
          }
        }
      } else {        
        text = keyText
      }
      if ((type && this.type === type) || (text && this.text.toUpperCase() === text)) {
        this.active = true
      }
      console.log("key", key)
    },
    onTap() {
      if (this.item.canEachChange) {
        this.taps++
        this.active = true
        this.commitData(this.taps)
        if (this.timeout) {
          clearTimeout(this.timeout)
          this.timeout = null
        }
        this.timeout = setTimeout(() => {
          this.active = false
          this.taps = 0
        }, 300)
      } else {
        this.active = true
        setTimeout(() => {
          this.active = false
          this.commitData()
        }, 100)
      }
    },
    onPress() {
      this.active = true
      if (this.type === 'delete') {
        this.checkLongPress()
      } else {
        this.clearLongPress()
      }
    },
    commitData(taps) {
      if (this.item.canEachChange) {
        taps = taps || 1
      }
      this.$emit('press', this.text, this.type, this.item.value, taps)
    }
  },
  beforeDestroy() {
    this.clearLongPress()
  }
}
</script>
<style lang="stylus" scoped>
.wrapper {
  margin: 5px 0;
  width: 10%;
  height: 50px;
  padding:0 2.5px;
  line-height: 50px;
  text-align: center;
  font-size: 22px;
  box-sizing: border-box;
  cursor: pointer;
  .btn {
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    &.active {
      background-color: #dbdbdb;
      color: #ffffff;
    }
    .icon {
      width: 50px;
      vertical-align: middle;
      pointer-events: none;
    }
  }
  &:nth-child(11) {
    margin-left: 2.5px;
  }
  &:nth-child(19) {
    margin-right: 2.5px;
  }
  &.wider {
    width: 40%;
  }
  &.gray {
    width: 15%;
    &.wider {
      width: 30%;
    }
    .btn {
      background-color: #dbdbdb;
    }
  }
  &.blue {
    width: 30%;
    .btn {
      background-color: #1989fa;
      color: #ffffff;
    }
  }
  &.gray,
  &.blue {
    .active {
      background-color: #ffffff;
      color: #444444;
    }
  }
  &.small-font {
    font-size: 18px;
  }
  &.text-indent {
    text-indent: 12px;
  }
  &.word {
    display: inline-block;
    margin: 0;
    width: auto;
    height: auto;
    padding: 2.5px;
    font-size: 18px;
    line-height: 1.2;
    .btn {
      padding: 5px;
      background-color: #f5f5f5;
      box-shadow: none;
      &.active {
        background-color: #ffffff;
        color: #444444;
      }
    }
  }
  &.jiu-btn {
    width: 33.333333%;
    &.gray {
      width: 100%;
    }
    &.long {
      width: 100%;
    }
    &.high {
      height: 110px;
      line-height: 110px;
    }
    &:nth-child(11) {
      margin-left: 0;
    }
    &.active .btn {
      background-color: #f5f5f5;
    }
  }
  &.active .btn {
    background-color: #ffffff;
  }
}
</style>