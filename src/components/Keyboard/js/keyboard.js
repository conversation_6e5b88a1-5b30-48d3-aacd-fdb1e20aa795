
const letter = "qwertyuiopasdfghjklzxcvbnm";
const number_zh = "1234567890-/:;()¥“”@。，、?!.";
const symbol_zh = "【】｛｝#%^*+=_-\\|~《》$&·…，？！”’";
const symbol_zh_icon = [
  "zhongkuohaozuo",
  "zhongkuohaoyou",
  "dakuohaozuo",
  "dakuohaoyou",
  "jinghao",
  "baifenhao",
  "charuhao",
  "xinghao",
  "jiahao",
  "dengyuhao",
  "xiahuaxian",
  "lianzifu",
  "fanxiegang",
  "shugang",
  "bolangxian",
  "shuminghaozuo",
  "shuminghaoyou",
  "meiyuan",
  "hehao",
  "dian",
  "",
  "",
  "wenhao",
  "gantanhao",
];
const number_en = "1234567890_-/:;()¥$@\".,?!'";
const symbol_en = "[]{}#%^*+=_\\|<>€£¥$•~.,?!'";

const get26Keys = (mode = 1, lang = "zh") => {
  let lett = letter;
  let key20 = {
    icon: "up",
    type: "caps",
    color: "gray",
  };
  if (lang === "zh") {
    key20 = {
      text: "#+=",
      type: "symbol",
      color: "gray",
      css: "small-font",
    };
  }
  if (mode === 2) {
    lett = lang === "zh" ? number_zh : number_en;
  }
  if (mode === 3) {
    lett = lang === "zh" ? symbol_zh : symbol_en;
    key20 = {
      text: "123",
      type: "number",
      color: "gray",
      css: "small-font",
    };
  }
  let keys = lett.split("").map((it, i) => {
    return {
      icon: mode === 3 && lang === "zh" ? symbol_zh_icon[i] : "",
      text: it,
      type: "letter",
    };
  });
  keys.splice(19, 0, key20);
  const actionKeys = [
    {
      icon: "back",
      type: "delete",
      color: "gray",
    },
    mode === 1
      ? {
          text: "123",
          type: "number",
          color: "gray",
          css: "small-font",
        }
      : {
          text: lang === "en" ? "ABC" : "拼音",
          type: "back",
          color: "gray",
          css: "small-font",
        },
    {
      text: lang === "en" ? "英" : "中",
      type: "lang",
      color: "gray",
      css: "small-font",
    },
    {
      text: "空格",
      type: "space",
      css: "wider small-font",
    },
    {
      text: "确认",
      type: "confirm",
      color: "gray",
      css: "wider small-font",
    },
  ];
  return keys.concat(actionKeys);
};

const left_action_keys = [
  {
    icon: "123",
    type: "number",
    color: "gray",
    css: "small-font",
  },
  {
    icon: "symbol1",
    type: "symbol",
    color: "gray",
    css: "small-font",
  },
  {
    icon: "ABC",
    type: "en",
    color: "gray",
    css: "small-font",
  },
  {
    text: "中",
    type: "lang",
    color: "gray",
    css: "small-font",
  },
];
const right_action_keys = [
  {
    icon: "back",
    type: "delete",
    color: "gray",
  },
  {
    text: "分隔",
    value: ["'"],
    color: "gray",
    css: "small-font",
  },
  {
    text: "换行",
    type: "confirm",
    color: "gray",
    css: "high small-font",
  },
];

const keys_mode_1 = [
  {
    icon: "symbol",
    value: ["，", "。", "？", "！"],
    canEachChange: true,
  },
  {
    icon: "ABC",
    text: "ABC",
    value: ["a", "b", "c"],
  },
  {
    icon: "DEF",
    text: "DEF",
    value: ["d", "e", "f"],
  },
  {
    icon: "GHI",
    text: "GHI",
    value: ["g", "h", "i"],
  },
  {
    icon: "JKL",
    text: "JKL",
    value: ["j", "k", "l"],
  },
  {
    icon: "MNO",
    text: "MNO",
    value: ["m", "n", "o"],
  },
  {
    icon: "PQRS",
    text: "PQRS",
    value: ["p", "q", "r", "s"],
  },
  {
    icon: "TUV",
    text: "TUV",
    value: ["t", "u", "v"],
  },
  {
    icon: "WXYZ",
    text: "WXYZ",
    value: ["w", "x", "y", "z"],
  },
  {
    text: "空格",
    type: "space",
    css: "long small-font",
  },
];
const keys_mode_2 = () => {
  return Array.from({ length: 10 }, (v, i) => {
    return { text: (i + 1).toString() };
  }).concat([
    {
      text: ".,:",
      value: [".", ",", ":"],
      canEachChange: true,
    },
    {
      text: "0",
    },
    {
      text: "空格",
      type: "space",
      css: "small-font",
    },
  ]);
};

const keys_mode_4 = () => {
  const symbol_numbers = "￥ °C % , + - : / = . _";
  let list = symbol_numbers.split(" ").map((it) => {
    return {
      text: it,
      type: "letter",
    };
  });
  list.push({
    text: "空格",
    type: "space",
    css: "small-font",
  });
  return list;
};
const get9Keys = (mode = 1) => {
  let leftKeys = [...left_action_keys];
  let rightKeys = [...right_action_keys];
  if (mode !== 1) {
    leftKeys[0] = {
      text: "拼音",
      type: "back",
      color: "gray",
      css: "small-font",
    };
    rightKeys[1] = {
      text: "更多",
      type: "more",
      color: "gray",
      css: `${mode === 4 ? "active " : ""}small-font`,
    };
  }
  let keys = [];
  keys.push(leftKeys);
  switch (mode) {
    case 1:
      keys.push(keys_mode_1);
      break;
    case 2:
      keys.push(keys_mode_2());
      break;
    case 4:
      keys.push(keys_mode_4());
      break;
    default:
  }
  keys.push(rightKeys);
  return keys;
};

export function getKeys(mode = 1, lang = "zh", isJiuGrid = false) {
  if (isJiuGrid && lang === "zh" && mode !== 3) {
    return get9Keys(mode);
  }
  return get26Keys(mode, lang);
}

const getNewSymbols = (str) => {
  return str.split(" ").map((it) => {
    return {
      text: it,
      type: "symbol",
    };
  });
};
const allNumberSymbol = getNewSymbols(
  ", : - / % ¥ $ @ # ^ + − × ÷ = () ° °C °F £ €"
);
const allSymbol = getNewSymbols(
  "， 。 ？ ！ …… ～ # ： 、 “” ‘’ （） - —— ； @ * _ ~ % & · • / \\ 《》 〈〉 「」 『』 〔〕 【】 ［］ [] ｛｝ {} () <> + − × ÷ = ^ ¥ $ £ € °C °F , . : ; ? ! ｜ | ← ↑ → ↓ \" ' … ￥ ＄ ￡ ＋ － ／ ＼ ＝ ° ＊ ＃ ＠ ％ ＆ ＿"
);
const allSymbolIcons = [];
export function getSymbols(mode) {
  return {
    list: mode === 1 ? allSymbol : allNumberSymbol,
    icons: mode === 1 ? allSymbolIcons : [],
  };
}

// 声母中 "zh", "ch", "sh" 去掉了
const sm = "b p m f d t n l g k h j q x r z c s y w".split(" ");
// 虽然拼音中on没有但是为了匹配后面的g加上了 （ang eng ing ong 去掉了）
// const ym = "a o e i u v ai ei ui ao ou iu ie ve er an en in un vn on".split(" ")
const getNextList = (value, nextList, list) => {
  const nextWordList = recurrence(
    "",
    [...nextList].splice(1),
    py.pinyins,
    [],
    []
  );
  nextWordList.forEach((it) => {
    const s = `${value} ${it}`;
    if (list.indexOf(s) === -1 && s.split(" ").length <= 4) {
      list.push(s);
    }
  });
};
const recurrence = (pinyin, nextList, dict, list, firstGroups) => {
  const item = nextList[0];
  for (let i = 0; item && i < item.length; i++) {
    if (item[i] !== "'") {
      const value = pinyin + item[i];
      const values = dict.filter((it) => it.indexOf(value) === 0);
      if (values.length > 0) {
        if (
          (values.indexOf(value) !== -1 ||
            (!pinyin && sm.indexOf(value) !== -1)) &&
          !list.indexOf(value) !== -1
        ) {
          list.push(value);
          firstGroups.push(value);
          pinyin && getNextList(value, nextList, list);
        }
        if (nextList.length > 1 && value.length < 6) {
          recurrence(value, [...nextList].splice(1), values, list, firstGroups);
        }
      }
    } else {
      getNextList(pinyin, nextList, list);
    }
  }
  return list;
};

export function getPinyinGroups(inputs) {
  let firstGroups = [];
  const groups = recurrence(
    "",
    [...inputs].splice(0, 24),
    py.pinyins,
    [],
    firstGroups
  );
  firstGroups = firstGroups.sort((a, b) => {
    return b.length - a.length;
  });
  return { firstGroups, groups };
}

// 26键盘分词
const backtrack = (s, length, wordSet, index, map) => {
  if (map.has(index)) {
    return map.get(index);
  }
  const wordBreaks = [];
  if (index === length) {
    wordBreaks.push([]);
  }
  for (let i = index + 1; i <= length; i++) {
    const word = s.substring(index, i);
    if (wordSet.has(word)) {
      const nextWordBreaks = backtrack(s, length, wordSet, i, map);
      if (nextWordBreaks.length > 0) {
        for (const nextWordBreak of nextWordBreaks) {
          const wordBreak = [word, ...nextWordBreak];
          wordBreaks.push(wordBreak);
        }
      } else {
        // 补全最后一个单词
        const lastWord = s.substring(i, length);
        const wordList = py.pinyins.filter((it) => it.indexOf(lastWord) === 0);
        for (const item of wordList) {
          wordBreaks.push([word, item]);
        }
      }
    }
  }
  if (index === 0 && wordBreaks.length === 0) {
    // 补全单词
    const wordList = py.pinyins.filter((it) => it.indexOf(s) === 0);
    if (wordList.length > 0) {
      for (const item of wordList) {
        wordBreaks.push([item]);
      }
    } else {
      // 取首字母
      wordBreaks.push([s.split("")]);
    }
  }
  map.set(index, wordBreaks);
  return wordBreaks;
};

export function getWordsBreak(inputs) {
  const s = inputs.toLowerCase();
  const map = new Map();
  const wordBreaks = backtrack(s, s.length, new Set(py.pinyins), 0, map);
  const breakList = [];
  for (const wordBreak of wordBreaks) {
    breakList.push(wordBreak.join(" "));
  }
  return breakList;
}

// 根据权重排序
const _sort = (a, b) => {
  return py.weights[b] - py.weights[a];
};
// 根据单词分词排序
const _sortByWord = (a, b) => {
  if (b.isNotFull) {
    if (a.isNotFull) {
      if (a.length === b.length) {
        return py.weights[b.list[0]] - py.weights[a.list[0]];
      }
      return b.length - a.length;
    }
    return -1;
  } else if (!a.isNotFull) {
    const aLength = a.value.replace(/\s/g, "").length;
    const bLength = b.value.replace(/\s/g, "").length;
    if (aLength === bLength) {
      if (a.length === b.length) {
        return py.weights[b.list[0]] - py.weights[a.list[0]];
      }
      return a.length - b.length;
    }
    return bLength - aLength;
  }
  return 1;
};
const pushList = (list, indexs, value, length, len, isNotFull) => {
  const result = [...indexs.sort(_sort)].splice(0, len);
  list.push({
    length: length,
    value: value,
    list: result,
    isNotFull: isNotFull,
  });
};
const setFirstValue = (value, groups, inputs) => {
  let str = "";
  groups.forEach((it) => {
    if (it.indexOf(value) === 0 && str.length < it.length) {
      str = it;
    }
  });
  let list = [];
  let strList = str.replace(/\s/g, "").split("");
  inputs
    .map((it) => it[0])
    .forEach((it, i) => {
      if (it === "'") {
        strList.splice(i, 0, "'");
      }
      list.push(strList[i] || it);
    });
  return list.join("");
};
export function getWords(groups, inputs) {
  console.log("keyboard.js ----- groups", groups);
  console.log("keyboard.js ----- inputs", inputs);
  let list = [];
  let smGroups = new Map();
  const len = Math.max(parseInt(300 / groups.length), 10);
  for (let i = 0; i < groups.length; i++) {
    const value = groups[i];
    const length = value.split(" ").length;
    const indexs = py.first_index[value];
    if (sm.indexOf(value) === -1 && indexs) {
      pushList(list, indexs, value, length, len, false);
    } else {
      const fullIndexs = py.pys[value];
      if (fullIndexs) {
        pushList(list, fullIndexs, value, length, len, false);
      } else {
        const smGroup = value
          .split(" ")
          .map((it) => it.substring(0, 1))
          .join("");
        if (!smGroups.has(smGroup)) {
          smGroups.set(smGroup, i);
          const smIndexs = py.first_index[smGroup]
            ? py.first_index[smGroup]
            : py.full_index[smGroup];
          if (smIndexs && smIndexs.length > 0) {
            pushList(list, smIndexs, value, length, len, true);
          }
        }
      }
    }
  }
  list = list.sort(_sortByWord);
  let firstPinyin = "";
  const newList = [];
  list.forEach((it) => {
    if (!firstPinyin && inputs) {
      firstPinyin = setFirstValue(it.value, groups, inputs);
    }
    it.list.forEach((it) => {
      if (newList.indexOf(it) === -1) {
        newList.push(it);
      }
    });
  });
  let words = [];
  const l = Math.min(newList.length, 300);
  for (let i = 0; i < l; i++) {
    words.push(py.words[newList[i]]);
  }
  console.log("py", py, py.words);

  console.log("这是返回的结果", words, firstPinyin);
  return { words, firstPinyin };
}

// getPinyinGroups([
//     [
//       "w",
//       "x",
//       "y",
//       "z"
//   ],
//   [
//       "g",
//       "h",
//       "i"
//   ],
//   [
//       "m",
//       "n",
//       "o"
//   ],
//   [
//       "m",
//       "n",
//       "o"
//   ],
//   [
//       "g",
//       "h",
//       "i"
//   ],
//   [
//       "g",
//       "h",
//       "i"
//   ],
//   [
//       "t",
//       "u",
//       "v"
//   ]
// ])

export function throttle(fn, delay) {
  let timeout;
  return function () {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}
