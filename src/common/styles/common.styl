.button {
  display: inline-block;
  min-width: 80px;
  height: 32px;
  padding: 0 10px;
  line-height: 30px;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  color: #fff;
  background-color: #ea5947;
  border: 1px solid #ea5947;
  vertical-align: middle;

  &:disabled {
    color: #bbb !important;
    background-color:#ededed !important;
    border-color:#ededed !important;
  }
  &.small {
    height: 28px;
    line-height: 26px;
  }
  &.large {
    height: 42px;
    line-height: 40px;
  }
}

.button-red-solid {
  color: #fff;
  background-color: #ea5947;
  border-color: #ea5947;

  &:hover,
  &:focus {
    background-color: #ee7a6c;
    border-color: #ee7a6c;
  }
  &:active {
    background-color: #ca3725;
    border-color: #ca3725;
  }
}
.button-blue-solid {
  color: #fff;
  background-color: #4c8be8;
  border-color: #4c8be8;
}
.button-red-hollow {
  color: #ea5947;
  background-color: #fff;
  border-color: #e3e3e9;

  &:hover,
  &:focus {
    border-color: #ea5947;
  }
  &:active {
    color: #fff;
    background-color: #ea5947;
    border-color: #ea5947;
  }
}

.button-black-hollow {
  color: #444;
  background-color: #fff;
  border-color: #e3e3e9;

  &:hover,
  &:focus {
    border-color: #ea5947;
  }
  &:active {
    color: #ea5947;
    border-color: #ea5947;
  }
}

.button-disabled {
  color: #bbb !important;
  background-color:#ededed !important;
  border-color:#ededed !important;
}

.button-radius {
  border-radius: 100px;
}

.button-icon {
  outline: none;
  border: none;
  padding: 0;
  background-color: transparent;
}

button {
  a,
  a:hover,
  a:focus {
    text-decoration: none;
    color: #ea5947;
  }
  &:active {
    a {
      color: #fff;
    }
  }
}

.uPage{
    .uHeader.ios{
      padding-top: 20px;
    }
  }

@supports (bottom: constant(safe-area-inset-top)) or (bottom: env(safe-area-inset-top)) {
  // #app {
  //   padding-top: constant(safe-area-inset-top); //为导航栏+状态栏的高度 88px
  //   padding-top: env(safe-area-inset-top); //为导航栏+状态栏的高度 88px
  //   padding-left: constant(safe-area-inset-left); //如果未竖屏时为0
  //   padding-left: env(safe-area-inset-left); //如果未竖屏时为0
  //   padding-right: constant(safe-area-inset-right); //如果未竖屏时为0
  //   padding-right: env(safe-area-inset-right); //如果未竖屏时为0
  //   padding-bottom: constant(safe-area-inset-bottom); //为底下圆弧的高度 34px
  //   padding-bottom: env(safe-area-inset-bottom); //为底下圆弧的高度 34px
  // }
  .uPage{
    .uHeader.ios{
      padding-top: constant(safe-area-inset-top); //为导航栏+状态栏的高度 88px
      padding-top: env(safe-area-inset-top); //为导航栏+状态栏的高度 88px
    }
    .uContentWrap{
      padding-bottom: constant(safe-area-inset-bottom); //为底下圆弧的高度 34px
      padding-bottom: env(safe-area-inset-bottom); //为底下圆弧的高度 34px
    }
  }
}

.icon-fujianleixingwenjianjia1 {
	color: #efcf4a;
  }
  
  .icon-fujianleixingppt {
	color: #f36666;
  }
  
  .icon-fujianleixingpdf {
	color: #f36666;
  }
  
  .icon-fujianleixingword {
	color: #529fff;
  }
  
  .icon-fujianleixingexcel {
	color: #7dccac;
  }
  
  .icon-fujianleixingtxt {
	color: #ae9ae2;
  }
  
  .icon-fujianleixingtuwen {
	color: #d1469c;
  }
  
  .icon-fujianleixinghtml {
	color: #71d0ff;
  }
  
  .icon-fujianleixingqita1 {
	color: #c97943;
  }
  
  .icon-weike {
	color: #5e9dfa;
  }
  
  .icon-weike1 {
	color: #8b50ff;
  }
  
  .icon-fujianleixingshipin {
	color: #efc756;
  }
  
  .icon-fujianleixingtupian {
	color: #d1469c;
  }
  
  .icon-fujianleixingyinpin {
	color: #859dd5;
  }
  
  .icon-fujianleixingyasuobao {
	color: #efce4a;
  }
  
  .icon-fujianleixingtxt {
	color: #ae9ae2;
  }
  .wm {
    pointer-events: none !important;
  }