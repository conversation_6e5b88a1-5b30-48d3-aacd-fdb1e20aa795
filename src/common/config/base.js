// 正式环境域名
var productionHost = '.ulearning.cn'

// 通过地址获取域名
var host = location.host
var isDev =
  host.indexOf('192.168') === 0 || host === '127.0.0.1' || host.indexOf('localhost') === 0
var protocol = isDev ? 'https://' : location.protocol + '//'
var hostDomain = isDev ? '.tongshike.cn' : host.substring(host.indexOf('.'))

var RESOURCE_SERVER_HOST = protocol + "leicloud" + productionHost
var CONFIG_QINIU_BASE64_URL = "https://up.qbox.me/putb64/-1"


export default{
  protocol,
  hostDomain,
  QINIU_RESOURCE_URL: RESOURCE_SERVER_HOST,
  BASE64_SAVEURL: CONFIG_QINIU_BASE64_URL,
};