window.cordova.define('cordova-plugin-common.device', function (
  require,
  exports,
  module
) {
  var exec = window.cordova.require('cordova/exec')

  function BasePlugin(pluginName) {
    // 插件名称对应原生配置插件名称  config plugin=name
    this.pluginName = pluginName
    // methodName原生方法名
    this.execNativeMethod = function (methodName, args, success, fail) {
      exec(success, fail, this.pluginName, methodName, args)
    }
  }

  function UApp() {
    this.method = BasePlugin
    this.method('UApp')
    delete this.method
    this.finish = function () {
      this.execNativeMethod('finish', [], null, null)
    }
    this.back = function () {
      this.execNativeMethod('back', [], null, null)
    }
    // 打开新的窗口
    this.openNewWindow = function (url) {
      this.execNativeMethod('openNewWindow', [{
        url: url
      }], null, null)
    }
    this.openUrl = function (url) {
      this.execNativeMethod('openUrl', [url], null, null)
    }
    // 检查网络状态  -1未知网络链接    0无网络  1 wifi   2 移动网络
    this.networkState = function (success) {
      this.execNativeMethod('networkState', [], success, null)
    }
  }

  function User() {
    this.method = BasePlugin
    this.method('User')
    delete this.method
    this.getUser = function (success) {
      this.execNativeMethod('getUser', [], success, null)
    }
  }

  function URecorder() {
    this.method = BasePlugin
    this.method('URecorder')
    delete this.method
    // 检测录音权限
    this.checkPermission = function (success, failure) {
      this.execNativeMethod('checkPermission', [], success, failure)
    }
    // 开始录音
    this.startRecord = function () {
      this.execNativeMethod('startRecord', [], null, null)
    }
    // 结束录音
    this.stopRecord = function () {
      this.execNativeMethod('stopRecord', [], null, null)
    }
    this.onStartRecord = function () {}
    this.onRecordComplete = function () {}
    var self = this
    // 开始录音
    window.startRecord = function () {
      self.onStartRecord && self.onStartRecord()
    }
    window.volume = function (vau) {}
    window.stopRecord = function () {}
    // 录音完成
    window.recordFile = function (filePath) {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      self.onRecordComplete && self.onRecordComplete(filePath)
    }
  }

  function UUploader() {
    this.method = BasePlugin
    this.method('UUploader')
    delete this.method

    var self = this
    window.custom = self

    // 上传
    this.upload = function (filePath) {
      var str = filePath.replace('file://', '')
      var timestamp = new Date().getTime()
      var fileName = filePath.split('.').pop()
      var random = Math.random(1000).toFixed(3) * 1000
      var random2 = Math.random(1000).toFixed(3) * 1000
      var remoteFile =
        'homework' +
        '/web/' +
        random +
        '/' +
        random2 +
        '/' +
        timestamp +
        '.' +
        fileName;

      // 上传进度
      window.onUploadProgress = function (file, progress) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadProgress && self.onUploadProgress(file, progress)
      }
      // 取消成功回调
      window.onUploadCancel = function (file) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadCancel && self.onUploadCancel(file)
      }
      // 上传成功回调
      window.onUploadSuccess = function (file, remotePath) {
        console.error(remotePath)
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadSuccess && self.onUploadSuccess(file, remotePath)
      }
      // 上传失败的回调
      window.onUploadFail = function (file) {
        if (!/^file/.test(file)) {
          file = 'file://' + file
        }
        self.onUploadFail && self.onUploadFail(file)
      }

      this.execNativeMethod('upload', [str, remoteFile], null, null)
    }
    // 取消上传
    this.cancel = function (filePath) {
      var str = filePath.replace('file://', '')
      this.execNativeMethod('cancel', [str], null, null)
    }
  }

  function UMedia() {
    this.method = BasePlugin
    this.method('UMedia')
    delete this.method
    const self = this

    this.play = function (audioFile, playEndFunc) {
      window.audioCompletion = typeof playEndFunc === 'function' ? playEndFunc : () => {}
      audioFile = audioFile.replace('file://', '')
      audioFile = audioFile.replace('cdvfile://umooc_cordova', '')
      audioFile = audioFile.replace('cdvfile://', '')
      this.execNativeMethod('playAudio', [audioFile], null, null)
      self.paused = false
    }
    this.stop = function () {
      this.execNativeMethod('stopAudio', [], null, null)
      self.paused = true
    }
  }

  if (!window.ULplugin) {
    window.ULplugin = {}

    function ULpluginReady() {}
  }
  window.ULplugin.UApp = new UApp()
  window.ULplugin.User = new User()
  window.ULplugin.URecorder = new URecorder()
  window.ULplugin.UUploader = new UUploader()
  window.ULplugin.UMedia = function () {
    return new UMedia()
  }

  if (window.pluginReady) {
    window.pluginReady()
  }
  module.exports = window.ULplugin
})