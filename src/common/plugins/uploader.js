import BasePlugin from './BasePlugin'
import {
  formatUrl
} from '../utils'

const formatPath = path => {
  if (!/^file\/\/\//.test(path)) {
    if (path[0] === '/') {
      path = 'file://' + path
    } else {
      path = 'file:///' + path
    }
  }
  return path
}

class Uploader extends BasePlugin {
  constructor() {
    super('UUploader')
  }
  upload(filePath, success, fail, progress) {
    // 上传进度
    window.onUploadProgress = (file, progress) => {
      typeof this.progress === 'function' && this.progress(formatPath(file), progress)
    }
    // 取消成功回调
    window.onUploadCancel = (file) => {
      typeof this.cancel === 'function' && this.cancel(formatPath(file))
    }
    // 上传成功回调
    window.onUploadSuccess = (file, remotePath) => {
      typeof this.success === 'function' && this.success(formatPath(file), formatUrl(remotePath))
    }
    // 上传失败的回调
    window.onUploadFail = (file) => {
      typeof this.fail === 'function' && this.fail(formatPath(file))
    }

    const str = filePath.replace('file://', '')
    const timestamp = new Date().getTime()
    const fileName = filePath.split('.').pop()
    const random = Math.random(1000).toFixed(3) * 1000
    const random2 = Math.random(1000).toFixed(3) * 1000
    const remoteFile = 'homework' + '/web/' + random + '/' + random2 + '/' + timestamp + '.' + fileName
    this.success = success
    this.progress = progress
    this.fail = fail

    return this.execNativeMethod('upload', [str, remoteFile]).catch(() => {})
  }
  cancel(filePath, cancel) {
    const str = filePath.replace('file://', '')
    this.cancel = cancel
    this.execNativeMethod('cancel', [str]).catch(() => {})
  }
}

export default new Uploader()