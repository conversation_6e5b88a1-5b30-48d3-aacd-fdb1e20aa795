
export default new (class {
  constructor () {
    this.PLUGIN_NAME = 'NativeLifeCycle'
  }
  // handel 的含义见文档： https://github.com/pliablepixels/cordova-plugin-multi-window
  registerOnStop (handle, onStopCallback) {
    const exec = window.cordova.require('cordova/exec')
    exec(onStopCallback, null, this.PLUGIN_NAME, 'registerOnStop', [handle])
  }
  deregisterOnStop (handle) {
    const exec = window.cordova.require('cordova/exec')
    exec(null, null, this.PLUGIN_NAME, 'deregisterOnStop', [handle])
  }
  registerOnStart (handle, onStartCallback) {
    const exec = window.cordova.require('cordova/exec')
    exec(onStartCallback, null, this.PLUGIN_NAME, 'registerOnStart', [handle])
  }
  deregisterOnStart (handle) {
    const exec = window.cordova.require('cordova/exec')
    exec(null, null, this.PLUGIN_NAME, 'deregisterOnStart', [handle])
  }
  deregisterAll () {
    const exec = window.cordova.require('cordova/exec')
    exec(null, null, this.PLUGIN_NAME, 'deregisterAll')
  }
})()
