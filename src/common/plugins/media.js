import BasePlugin from '@/common/plugins/BasePlugin'

class Media extends BasePlugin {
  constructor () {
    super('UMedia')
    window.audioCompletion = () => {
      typeof this.onEnded === 'function' && this.onEnded()
    }
    window.yxyAudioPlaying = (currentDuration, totalDuration) => {
      typeof this.onProgress === 'function' && this.onProgress(currentDuration, totalDuration)
    }
  }
  // 不支持传入回调 传入回调会报错 所以这里捕获之
  play (filePath, onEnded) {
    this.onEnded = onEnded
    // console.log(this.formatFilePath(filePath))
    return this.execNativeMethod('playAudio', [this.formatFilePath(filePath)])
  }
  pause (filePath) {
    return this.execNativeMethod('stopAudio', [this.formatFilePath(filePath)])
  }
  formatFilePath (filePath) {
    return filePath.replace(/^file:\/\//, '')
  }
}

export default new Media()
