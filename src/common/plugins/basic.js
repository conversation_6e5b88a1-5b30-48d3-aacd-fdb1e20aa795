// 原生APP提供的基础功能
import BasePlugin from './BasePlugin'

class Share extends BasePlugin {
  share (content) {
    return this.execNativeMethod('share', [content])
  }
}

export const share = new Share('UShare')

class App extends BasePlugin {
  // 退出webView
  finish () {
    return this.execNativeMethod('finish')
  }
  back () {
    return this.execNativeMethod('back')
  }
  // 打开新的窗口
  openNewWindow (url) {
    return this.execNativeMethod('openNewWindow', { url })
  }
  openUrl (url) {
    return this.execNativeMethod('openUrl', [url])
  }
  // 检查网络状态  -1未知网络链接    0无网络  1 wifi   2 移动网络
  networkState () {
    return this.execNativeMethod('networkState', [])
  }
}

export const app = new App('UApp')

class Message extends BasePlugin {
  goToChat (userName, name) {
    return this.execNativeMethod('goToChat', [{ userName, name }])
  }
}

export const message = new Message('UMessage')

class User extends BasePlugin {
  getUser () {
    if (process.env.NODE_ENV === 'development') {
      return import('./test').then(data => User.uniteUser(data['chenjun_tea']))
    }
    return this.execNativeMethod('getUser', []).then(user => {
      return User.uniteUser(user)
    })
  }
  static uniteUser (user) {
    const userID = parseInt(user.userID)
    const role = parseInt(user.role)
    let sex = 0
    switch (user.sex) {
      case '1':
      case 1:
      case true:
        sex = 1 // 男
        break
      case '2':
      case 2:
      case false:
        sex = 0 // 女
        break
    }
    return { ...user, userID, role, sex }
  }
}

export const user = new User('User')
