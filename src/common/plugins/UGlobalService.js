class GlobalModel {
  constructor ({ ub2, ocId = -1 }) {
    this.ub2 = ub2
    this.ocId = ocId
  }
}

class UGlobalService {
  constructor () {
    this.globalModel = {
      'ocId': -1
    }
  }

  setGlobal (option) {
    this.globalModel = new GlobalModel(option)
  }

  // 返回活动列表
  goBackListByPc (path) {
    let obj = this.getGlobal()
    if (obj && obj.ub2 === '1') {
      window.close()
    } else {
      if (!path) return
      window.location.replace(path)
    }
  }

  getRefreshNotice () {
    let obj = this.getGlobal()
    if (obj && obj.ub2 === '1') {
      return 'UNotificationNameCourseHomeworkChange'
    } else {
      return 'kLENotificationRefreshActivieList'
    }
  }

  getGlobal () {
    return this.globalModel
  }
}

export default new UGlobalService()
