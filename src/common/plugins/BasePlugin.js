// 使用Cordova提供的exec 方法，封装原生提供的API。
// 需要Cordova 先于vue加载执行，由于vue的执行是在deviceReady 事件监听器中
// 所以这一点可以保证

export default class BasePlugin {
  // 插件名称对应原生配置插件名称
  constructor (pluginName) {
    this.pluginName = pluginName
  }

  // methodName原生方法名
  execNativeMethod (methodName, args = []) {
    const exec = window.cordova.require('cordova/exec')
    return new Promise((resolve, reject) => {
      exec(
        (...args) => {
          if (args.length === 1) {
            resolve(args[0])
          } else {
            resolve(args)
          }
        },
        e => {
          reject(e)
        },
        this.pluginName,
        methodName,
        args
      )
    })
  }
}

// 单例模式
// export const singleton = (ClassName) => {
//   return (ClassName => {
//     let instance = null
//     return (...args) => {
//       if (instance) {
//         return instance
//       } else {
//         instance = new ClassName(...args)
//         return instance
//       }
//     }
//   })(ClassName)
// }
