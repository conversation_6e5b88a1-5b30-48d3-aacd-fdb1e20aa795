import BasePlugin from './BasePlugin'

// 录音是单例
class Recorder extends BasePlugin {
  constructor() {
    super('URecorder')
  }
  // 权限检测
  checkPermission() {
    return this.execNativeMethod('checkPermission', [])
  }
  startRecord(onComplete, onBeforeStartRecord) {
    // 回调 录音开始
    window.startRecord = () => {
      typeof this.onBeforeStartRecord === 'function' && this.onBeforeStartRecord()
    }
    // 回调 录音结束
    window.stopRecord = () => {
      typeof this.onBeforeStopRecord === 'function' && this.onBeforeStopRecord()
    }
    // 回调 声音
    window.volume = () => {}
    // 回调 录音完成
    window.recordFile = (filePath) => {
      if (!/^file/.test(filePath)) {
        filePath = 'file://' + filePath
      }
      typeof this.onComplete === 'function' && this.onComplete(filePath)
    }
    // 录音前请求权限
    this.onBeforeStartRecord = onBeforeStartRecord
    this.onComplete = onComplete
    // startRecord 不支持传入回调 传入回调会发生错误 因此这里捕获之
    return this.execNativeMethod('startRecord', []).catch(() => {})
  }
  // 结束录音 不是暂停
  stopRecord(onBeforeStopRecord) {
    this.onBeforeStopRecord = onBeforeStopRecord
    // stopRecord 不支持传入回调 传入回调会发生错误 因此这里捕获之
    return this.execNativeMethod('stopRecord', []).catch(() => {})
  }
}

export default new Recorder()