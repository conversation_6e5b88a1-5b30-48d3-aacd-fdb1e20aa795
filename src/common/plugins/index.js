// import UGlobalService from './UGlobalService'

// export const updateActiveList = (ub2) => {
//   const key = parseInt(ub2) === 1 ? 'UNotificationNameCourseDiscussionChange' : 'kLENotificationRefreshActivieList'
//   window.broad.fireNativeEvent(key, {}, () => {})
// }

export { app, share, user, message } from './basic'
export { default as recorder } from './recorder'
export { default as media } from './media'
export { default as uploader } from './uploader'
export { default as nativeLifeCycle } from './nativeLifeCycle'
export { default as notify } from './notify'
