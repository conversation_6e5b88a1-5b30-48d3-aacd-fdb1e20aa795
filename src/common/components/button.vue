// 设计和api参考自cube-ui
<script>
const map = {
  active: 'uButton-active',
  disabled: 'uButton-disabled',
  radius: 'uButton-radius',
  inline: 'uButton-inline',
  primary: 'uButton-primary',
  outline: 'uButton-outline'
}
export default {
  props: {
    type: {
      type: String,
      default: 'button'
    },
    icon: {
      type: String,
      default: ''
    },
    active: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    radius: {
      type: Boolean,
      default: false
    },
    inline: {
      type: Boolean,
      default: false
    },
    primary: {
      type: Boolean,
      default: false
    },
    outline: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    className () {
      let btn = 'uButton'
      for (let key in map) {
        if (map.hasOwnProperty(key) && this[key]) {
          btn += ' ' + map[key]
        }
      }
      return btn
    }
  },
  methods: {
    click (event) {
      this.$emit('click', event)
    }
  }
}
</script>

<template>
  <button :class="className" :type="type" @click="click">
    <i v-if="icon" :class="icon"></i>
    <slot></slot>
  </button>
</template>

<style lang="stylus">
.uButton
  display block
  width 100%
  outline none
  text-align center
  cursor pointer
  white-space nowrap
  font-size 16px
  line-height 1
  background-color #fff
  padding 16px
  border 1px solid #e3e3e9
  box-sizing border-box
  border-radius 4px
  color #444444
  
  &>i
    display inline-block
    font-size 100%

.uButton-inline
  display inline
  width auto
  font-size 12px
  padding 9px 10px

.uButton-radius
  border-radius 999px

.uButton-primary
  color #fff
  background-color $color-main
  border-color $color-main

.uButton-outline
  background-color transparent

.uButton:active, .uButton.uButton-active
  background #e3e3e9

.uButton-primary:active, .uButton-primary.uButton-active
  background rgb(214, 79, 66)
  border-color rgb(214, 79, 66)

.uButton-outline:active, .uButton-outline.uButton-active
  background-color rgba(0, 0, 0, 0.08)

.uButton.uButton-disabled
  // background-color rgb(204, 204, 204)
  background #fff
  border-color rgb(204, 204, 204)
  color #ccc
</style>
