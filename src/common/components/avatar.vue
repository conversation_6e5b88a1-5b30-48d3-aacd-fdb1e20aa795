<script>
import getFullsrc from './getDefaultAvatar'
export default {
  props: {
    user: {
      default () {
        return {}
      }
    },
    alt: {
      type: String,
      default: '头像'
    }
  },
  computed: {
    fullSrc () {
      return getFullsrc(this.user)
    }
  }
}
</script>

<template>
  <div class="ul-avatar">
    <img :src="fullSrc" :alt="alt">
  </div>
</template>

<style lang="stylus">
.ul-avatar
  overflow hidden
  width 44px
  height 44px
  border-radius 50%
  white-space nowrap

  img
    width 100%
    height 100%
</style>
