import teaAvartMale from './teacher_male.png'
import teaAvartFemale from './teacher_female.png'
import stuAvartGirl from './student_girl.png'
import stuAvartBoy from './student_boy.png'
import { formatUrl } from '../utils'

const replace = (src, sex, role) => {
  if (!src || typeof src !== 'string') {
    return getDefaultAvatar(sex, role)
  } else {
    return formatUrl(src)
  }
}

// 获取默认头像
const getDefaultAvatar = (sex, role) => {
  if (role !== 9) {
    //  是老师 可能包括助教等
    // 0 女 1 男 other 男
    if (parseInt(sex) === 0) {
      return teaAvartFemale
    } else {
      return teaAvartMale
    }
  } else {
    if (parseInt(sex) === 0) {
      return stuAvartGirl
    } else {
      return stuAvartBoy
    }
  }
}

export default ({ src = '', sex, role }) => {
  return replace(src, sex, role)
}
