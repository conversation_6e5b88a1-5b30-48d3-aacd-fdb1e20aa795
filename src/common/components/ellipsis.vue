<script>
import i18n from '@/i18n'

export default {
  props: {
    maxHeight: {
      type: [Number, String],
      default: 120
    },
    expandTxt: {
      type: String,
      default: i18n.t('expand') + '\u003e\u003e'
    },
    collapseTxt: {
      type: String,
      default: '\u003c\u003c' + i18n.t('collapse')
    },
    // 文本内容
    content: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      hasMaxHeight: false, // 默认没有最大高度限制
      expand: false, // 默认隐藏展开按钮
      collapse: true // 默认显示收起按钮
    }
  },
  computed: {
    computedHeight () {
      if (this.hasMaxHeight) {
        let height = this.maxHeight
        if (typeof height === 'number') height += 'px'
        return { 'max-height': height }
      } else {
        return {}
      }
    }
  },
  watch: {
    content: {
      immediate: true,
      handler (newVal) {
        this.refresh()
      }
    }
  },
  methods: {
    // 展开
    expandBtn () {
      if (this.hasMaxHeight === true) {
        this.hasMaxHeight = false
        this.expand = false
        this.collapse = true
      }
    },
    // 收起
    collapseBtn () {
      if (this.hasMaxHeight === false) {
        this.hasMaxHeight = true
        this.expand = true
        this.collapse = false
      }
    },
    refresh () {
      this.$nextTick(() => {
        const content = this.$refs.content
        if (content && content.offsetHeight <= this.maxHeight) {
          this.hasMaxHeight = false
          this.expand = false
          this.collapse = false
        } else {
          this.hasMaxHeight = true
          this.expand = true
          this.collapse = false
        }
      })
    }
  }
}
</script>

<template>
  <div class="uEllipContainer">
    <div>
      <slot name="expand">
        <span class="uEllipExpand" v-show="expand">
          ...
          <i class="uEllipBtn" @click="expandBtn">{{expandTxt}}</i>
        </span>
      </slot>
    </div>
    <div ref="content" :style="computedHeight">
      <slot>
        <p>{{content}}</p>
      </slot>
    </div>
    <div>
      <span
        class="uEllipBtn uEllipCollapse"
        v-show="collapse"
        @click="collapseBtn"
      >{{collapseTxt}}</span>
    </div>
  </div>
</template>

<style lang="stylus">
.uEllipContainer
  position relative
  overflow hidden

  /.uEllipExpand
    position absolute
    right 0
    bottom 0
    background-image linear-gradient(to right, rgba(255, 255, 255, 0.2) 0, #fff 1em)
    padding-left 1em

  .uEllipBtn
    font-style normal
    color $color-main
</style>
