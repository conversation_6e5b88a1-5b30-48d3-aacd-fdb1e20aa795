<template>
  <div class="component-popup">
    <div class="popup-mask"></div>
    <div class="popup-container">
      <div class="popup-content">
        <div class="lds-ring">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
        <div class="toast-tip">{{content}}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'upopup',
  props: {
    content: {
      type: String,
      default: 'Hello'
    }
  },
  methods: {
    clickHandler (e) {
      this.$emit('click', e)
    }
  }
}
</script>
<style lang="stylus" scoped>
.component-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  .popup-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(68, 68, 68, 0.7);
  }

  .popup-container {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
  }

  .popup-content {
    position: absolute;
    top: 260px;
    max-width: 80%;
    padding: 10px;
    color: #fff;
    text-align:center;
  }
  .toast-tip{
    margin-top: 20px;
    font-size: 14px;
    line-height: 20px;
  }
}

.lds-ring {
  display: inline-block;
  position: relative;
  width: 26px;
  height: 26px;
}

.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 26px;
  height: 26px;
  border: 2px solid #fff;
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #fff transparent transparent transparent;
}

.lds-ring div:nth-child(1) {
  animation-delay: -0.45s;
}

.lds-ring div:nth-child(2) {
  animation-delay: -0.3s;
}

.lds-ring div:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
