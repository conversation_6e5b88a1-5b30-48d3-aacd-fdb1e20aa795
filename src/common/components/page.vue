<script>
import { getPlatform } from '@/common/utils'
import { getUrlParam } from '@/utils/utils'
export default {
  props: {
    title: {
      default: '',
      type: String
    },
    show: {
      default: false,
      type: Boolean
    }
  },
  data () {
    return {
      platform: getPlatform().platform,
      name: '', // 路由的名字,为了方便起见,直接使用$route.name
      showHead: false
    }
  },
  created() {
    this.showHead = getUrlParam('mp') !== '1'
  },
  methods: {
    back () {
      if (this.$route.name !== this.name) return
      const back = this.$listeners.back
      if (!back || (back && back() !== false)) {
        window.history.back()
      }
    }
  },
  mounted () {
    this.name = this.$route.name
    document.addEventListener('backbutton', this.back, false)
  },
  destroyed () {
    document.removeEventListener('backbutton', this.back, false)
  }
}
</script>

<template>
  <div class="uPage">
    <header class="uHeader" :class="platform" v-if="showHead || show">
      <ul class="uHeaderList">
        <li class="uBackZone" @click="back">
          <i class="iconfont icon-yemian-fanhui uBackIcon"/>
          <slot name="left-attach"></slot>
        </li>
        <li class="uHeaderTitle">
          <slot name="title">
            <h1>{{title}}</h1>
          </slot>
        </li>
        <li class="right">
          <slot name="menu"/>
        </li>
      </ul>
    </header>
    <div class="uContentWrap">
      <slot />
    </div>
  </div>
</template>

<style lang="stylus">
// page结构
.uPage
  display flex
  flex-flow nowrap column
  overflow-y auto
  height 100vh
  background-color #fff

  /.uHeader
    flex 0 0 auto
    background-color #fff

    /.uBackZone
      display flex
      align-items center
      padding 0 16px
      margin-left -16px
      [dir=rtl] &
        transform scale(-1)

  /.uContentWrap
    flex 1 1 0
    height calc(100% - 65px) // 兼容ios低版本
    // transform rotate(0) // 如果滚动的内部内容溢出，溢出圆角的部分不会剪裁
    overflow-y hidden
    -webkit-overflow-scrolling touch
    // background url('~@/assets/images/list_pic.png') no-repeat center bottom / contain, url('~@/assets/images/list_bj.png') no-repeat 0 0 / cover

// header 结构
.uHeaderList
  display flex
  justify-content space-between
  align-items center
  padding 0 16px
  height 45px
  border-bottom 1px solid #e3e3e9
  line-height 44px
  position relative

  /.uBackIcon
    color #9b9b9b
  /.right
    font-size 14px
    a
      color #444
  /.uHeaderTitle
    position absolute
    left 50%
    transform translateX(-50%)
    max-width 60%

    h1
      letter-spacing 0
      font-weight 600
      white-space nowrap
      overflow hidden
      text-overflow ellipsis
      color #444
</style>
