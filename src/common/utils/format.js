// 严格区分大写和小写
// H: 0-23 h: am/pm 1-12
const generateSet = date => ({
  y: date.getFullYear(),
  M: date.getMonth() + 1,
  d: date.getDate(),
  H: date.getHours(),
  m: date.getMinutes(),
  s: date.getSeconds()
})

const padZero = (num, digits) => {
  const numDigits = String(num).length
  if (digits <= numDigits) {
    return num
  }
  return num < Math.pow(10, digits - 1) ? '0'.repeat(digits - numDigits) + num : num
}

export const formatDate = (d, fmt) => {
  const reg = /(y|M|d|H|m|s)+/g
  const date = new Date(d)
  const set = generateSet(date)
  let res
  while ((res = reg.exec(fmt)) !== null) {
    // console.log(res)
    fmt = fmt.replace(res[0], pad<PERSON>ero(set[res[1]], res[0].length))
  }
  return fmt
}

// 计时器支持 天 小时 分钟 秒 不支持月 年等
const regs = [/d+/, /h+/, /m+/, /s+/]
const intervals = { 'd': 60 * 60 * 24, 'h': 60 * 60, 'm': 60, 's': 0 }
const genSecondsSet = () => {
  const res = {}
  const fn = (seconds, interval) => {
    if (interval === 0) return [seconds, 0]
    const s = seconds % interval
    const remained = (seconds - s) / interval
    return [remained, s]
  }
  Object.entries(intervals).forEach(([key, interval]) => {
    res[key] = seconds => fn(seconds, interval)
  })
  return res
}
/**
 * @param { Integer } seconds
 */
export const formatTimer = (seconds, fmt) => {
  let temp = seconds
  const set = genSecondsSet()
  regs.forEach(it => {
    const res = fmt.match(it)
    if (res !== null) {
      const [r, s] = set[res[0].charAt(0)](temp)
      fmt = fmt.replace(res[0], padZero(r, res[0].length))
      temp = s
    }
  })
  return fmt
}
