// 判断平台
export const getPlatform = () => {
  let u = navigator.userAgent
  let versions = {
    // IE内核
    trident: u.indexOf('Trident') > -1,
    // opera内核
    presto: u.indexOf('Presto') > -1,
    // 苹果、谷歌内核
    webKit: u.indexOf('AppleWebKit') > -1,
    // 火狐内核
    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') === -1,
    // 是否为移动终端
    mobile: !!u.match(/AppleWebKit.*Mobile.*/),
    // ios终端
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
    // android终端或者uc浏览器
    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
    // 是否为iPhone或者QQHD浏览器
    iPhone: u.indexOf('iPhone') > -1,
    // 是否iPad
    iPad: u.indexOf('iPad') > -1,
    // 判断ios是否是ipad, ios13以上不包含ipad字段，只包含macintosh 
    isIPadOrMac: /macintosh|iPad/i.test(u),
    // 是否在app里面
    inApp: /umoocApp/i.test(u),
    // 是否web应该程序，没有头部与底部
    webApp: u.indexOf('Safari') === -1,
    isHarmony: u.toLowerCase().indexOf('harmonynextapp') > -1
  }
  let ua = navigator.userAgent
  let deviceInfo = 'iPhone'
  if (versions.android) {
    let sss = ua.split(';')
    let index = -1
    for (let i in sss) {
      if (sss[i].indexOf('Build/') > 0) {
        index = i
        break
      }
    }
    if (index > -1) {
      deviceInfo = sss[index].substring(0, sss[index].indexOf('Build/'))
    }
  }
  return {
    // language: (navigator.browserLanguage || navigator.language).toLowerCase(),
    isMobile: versions.mobile,
    isIOS: versions.ios,
    isAndroid: versions.android,
    platform: versions.mobile ? versions.ios ? 'ios' : 'android' : 'web',
    deviceInfo: deviceInfo,
    isIPadOrMac: versions.isIPadOrMac,
    inApp: versions.inApp,
    isHarmony: versions.isHarmony
  }
}
