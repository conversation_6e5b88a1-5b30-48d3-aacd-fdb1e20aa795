// format
// import { formatTimer } from './format'
export {
  formatDate,
  formatTimer
} from './format'

// debouncing throttle
export { debounce, throttle } from './throttle&debounce'

// 判断平台
export { getPlatform } from './platform'

// 数字转字母
export { decimal2Letter } from './decimalTrans'

export { formatUrl } from './formatCDNUrl'

// console.log(formatTimer(60, 'mm:ss'))
// console.log(formatTimer(120, 'mm:ss'))
// console.log(formatTimer(180, 'hh:ss'))
// console.log(formatTimer(240, 'hh:mm:ss'))
// console.log(formatTimer(360, 'ss'))
// console.log(formatTimer(480, 'hh'))
// console.log(formatTimer(3600, 'hh:mm:ss'))
// console.log(formatTimer(3600 * 24, 'dd:hh:mm:ss'))
// console.log(formatTimer(3600 * 24 - 1, 'dd:hh:mm:ss'))
// console.log(formatTimer(0, 'dd:hh:mm:ss'))
