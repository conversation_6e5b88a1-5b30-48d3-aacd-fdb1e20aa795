import { QINIU_RESOURCE_URL } from '@/common/config'
const httpReg = /^https?:\/\/|^\/\//
const oldUrlReg = /(^https?:\/\/|^\/\/)(tskcloud(\.ulearning|\.qiniudn)?|ulearning|qiniudn)\.(cn|com)/

export const formatUrl = (url) => {
  if (!url || typeof url !== 'string') {
    return url
  }
  if (typeof url === 'string' && url.indexOf('file:///') === 0) {
    return url
  }
  if (oldUrlReg.test(url)) {
    return QINIU_RESOURCE_URL + url.replace(oldUrlReg, '')
  } else if (httpReg.test(url)) {
    return url
  } else if (url[0] === '/') {
    return QINIU_RESOURCE_URL + url
  } else {
    return QINIU_RESOURCE_URL + '/' + url
  }
}

// test
// console.log(formatUrl('test.png') === 'https://leicloud.ulearning.cn/test.png')
// console.log(formatUrl('/test.png') === 'https://leicloud.ulearning.cn/test.png')
// console.log(formatUrl('//test.png') === '//test.png')
// console.log(formatUrl('https://www.baidu.com') === 'https://www.baidu.com')
// console.log(formatUrl('http://www.zhihu.com') === 'http://www.zhihu.com')
// console.log(formatUrl('//www.bilibili.com') === '//www.bilibili.com')
// console.log(formatUrl('https://tskcloud.ulearning.cn') === 'https://leicloud.ulearning.cn')
// console.log(formatUrl('https://tskcloud.qiniudn.cn/') === 'https://leicloud.ulearning.cn/')
// console.log(formatUrl('https://tskcloud.qiniudn.cn/test.jpg') === 'https://leicloud.ulearning.cn/test.jpg')
// console.log(formatUrl('http://tskcloud.qiniudn.cn/homework/test.jpg') === 'https://leicloud.ulearning.cn/homework/test.jpg')
// console.log(formatUrl('https://tskcloud.ulearning.cn/question/1/1131405/caada122-9d25-4b8a-8c14-fea208421d00.jpg'))
