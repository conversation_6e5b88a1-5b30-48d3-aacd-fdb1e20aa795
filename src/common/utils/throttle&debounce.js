export const throttle = (f, duration = 100) => {
  let lastTime = 0
  return (...args) => {
    let currTime = Date.now()
    let res
    if (currTime - lastTime >= duration) {
      res = f(...args)
      lastTime = currTime
    }
    return res
  }
}

export const debounce = (f, duration = 0) => {
  let timeoutID
  return (...args) => {
    clearTimeout(timeoutID)
    timeoutID = setTimeout(() => f(...args), duration)
  }
}
