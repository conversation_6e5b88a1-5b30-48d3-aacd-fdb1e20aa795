import { face, utest } from './config'
import store from '@/store/index'
let faceApi = {
  getFaceInfo: (query) => {
    return face('face/original', {
      params: query
    })
  },
  getUpToken: (query) => {
    return face('/files/getUptoken', {
      params: query
    })
  },
  compareFace: (query, data) => {
    return face.post('/face/compare', data, {
      params: query
    })
  },
  compareCode: (query) => {
    return face('/face/verificationCode/verify', {
      params: query
    })
  },
  testFace: (query) => {
    return face('/face/compare/test', {
      params: query
    })
  },
  saveFaceInfo: (data) => {
    return face.post('/face', data)
  },
  getExamConfig: (orgId) => {
    return face('/exams/orgExamConfig/' + orgId)
  },
  saveBehavior: (data) => {
    return utest.post(`/exams/setBehaviorTrace?userId=${data.userId}`, data)
  },
  saveSnap: (data) => {
    return face.post(`/face/snap`, data)
  },
  terminalCheck: (data) => {
    return face.post(`/exams/terminalVerification`, data)
  },
  getWxConfig: (data) => {
    return face.post('/ulearning/jsconfig', data)
  }
}
export default faceApi
face.interceptors.request.use((request) => {
  console.log(store)
  request.headers['Authorization'] = store.state.user.token
  return request
}, function (error) {
  // Do something with request error
  return Promise.reject(error)
})
// export const getFaceInfo = (query) => {
//   return face('getExamList', {
//     params: query
//   })
// }

// face.defaults.headers.common['Authorization'] = 'AE0CB038BBF667691D2BEEC1ACEAA323'
// face.interceptors.response.use(function (response) {
//   return response
// }, function (error) {
//   if (!error.config.url.includes('umooc/behaviour.do')) {
//     const { message, stack } = error
//     const { url, params, data } = error.config
//     saveError({ message, stack, location: window.location.href, url, params, data })
//   }
//   return Promise.reject(error)
// })
