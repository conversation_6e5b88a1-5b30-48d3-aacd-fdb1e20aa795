/*
 * @Date: 2021-01-27 15:09:16
 * @LastEditors: liuzhong
 * @LastEditTime: 2021-01-28 10:49:46
 * @FilePath: \es6e:\work-project\face\exam\branches\bugfix\src\api\config.js
 */
import {
  hostDomain,
  protocol
} from '@/common/config'
import axios from 'axios'
import i18n from '../i18n'

import {
  loggedOnOtherDevices,
  reLogin,
  exitApp,
  saveLog
} from '@/lib/cordovaPlugin/mobilePlugin'

export const exam = axios.create({
  baseURL: protocol + 'apps' + hostDomain + '/exam/'
})

export const www = axios.create({
  baseURL: protocol + 'www' + hostDomain
})

export const face = axios.create({
  baseURL: protocol + 'courseapi' + hostDomain
  // headers: { 'Authorization':  }
})

export const apps = axios.create({
  baseURL: protocol + 'apps' + hostDomain
})

export const user = axios.create({
  baseURL: protocol + 'courseapi' + hostDomain
  // headers: { 'Authorization':  }
})

export const utest = axios.create({
  baseURL: protocol + 'utestapi' + hostDomain
})

function initHttpConfig (service) {
  // 请求拦截器
  service.interceptors.request.use(
    config => {
      config.headers['Authorization'] = window.Authorization || "50E7A663EA0C7419E5DA65142BB2DE0D"
      config.headers['clientId'] = window.deviceID || ''

      config.params = Object.assign({}, config.params, { lang: i18n.locale, traceId: window.traceId })
      // config.headers['Authorization'] =  "04E5D68A3A8B35E2F96AE9E4C06B962E"
      return config
    }
  )

  // 响应拦截器
  service.interceptors.response.use(
    response => {
      return response
    },
    error => {
      if (error && error.response) {
        switch (error.response.status) {
          case 401:
            try {
              if (error.response.data.code == 2301) {
                loggedOnOtherDevices(function success () {

                }, function fail () {
                  alert('已在其他终端登录!')
                })
                setTimeout(function () {
                  exitApp()
                }, 1000)
              }
              if (!window.isReLoging && (error.response.data.code == 2201 || error.response.data.code == 2101)) {
                window.isReLoging = true
                saveLog(`触发reLogin，接口url=${error.response.config.url}，token=${error.response.config.headers.Authorization}`)
                reLogin()
              }
            } catch (error) {}
            break
          default:
            break
        }
      }

      return Promise.reject(error)
    }
  )
}

initHttpConfig(exam)
initHttpConfig(www)
initHttpConfig(face)
initHttpConfig(apps)
initHttpConfig(user)
initHttpConfig(utest)