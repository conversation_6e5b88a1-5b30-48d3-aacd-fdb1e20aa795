import { exam, face, www, utest } from './config'

export const getExamList = (query) => {
  return exam('getExamList', {
    params: query
  })
}

export const repayExam = (query) => {
  return exam('repayExam', {
    params: query
  })
}

export const getExamStatus = query => {
  return exam('getStuExamStatus', {
    params: query
  })
}

export const getExamInfo = (query) => {
  return exam('getExamInfo', {
    params: query
  })
}

export const getExamReport = (query) => {
  return exam(`getExamReport`, {
    params: query
  })
}

export const startExam = (query) => {
  return exam('startExam', {
    params: query
  })
}

export const getEndTime = (examRelationId, query) => {
  return face('exams/endTime/' + examRelationId, {
    params: query
  })
}

export const getPaper = (query) => {
  return exam('getPaperForStudent', {
    params: query
  })
}

export const getLastStuAnswer = (query) => {
  return exam('getTheLastAnswer', {
    params: query
  })
}

export const getReferAnswer = (query) => {
  return exam('getPaperAnswer', {
    params: query
  })
}

export const getStuAnswer = (query) => {
  return exam('getMyRecord', {
    params: query
  })
}

export const saveStuAnswer = (data) => {
  return exam.post('savePaperAnswerToMemcache', data)
}

export const submitStuAnswer = (data) => {
  return exam.post('savePaperAnswer', data)
}

export const getExamLimitCount = (query) => {
  return exam.get('getExamLimit.do', {
    params: query
  })
}

export const saveExamLimitTime = (query, data) => {
  return exam.get('saveExamLeaveTime.do', {
    params: query,
    data
  })
}
export const getExamLimitTime = (query, data) => {
  return exam.get('getExamLeaveTime.do', {
    params: query,
    data
  })
}
// 获取切屏限制时间考试的上一次离开考试时间
export const getExamLastLeaveTime = (query) => {
  return face.get('exams/getExamLastLeaveTime', {
    params: query
  })
}
export const saveExamLimitCount = (query, data) => {
  return exam.get('saveExamLimit.do', {
    params: query,
    data
  })
}

export const saveError = data => {
  return www({
    method: 'POST',
    url: 'umooc/behaviour.do?operation=insert',
    data
  })
}

export const login = data => {
  return face.post('/users/login', data)
}

export const getExamResult = () => {
  return face.get('/exams/rankAndScore')
}

export const logout = () => {
  return face.post('/users/logout')
}

export const getSignStatus = (query) => {
  return utest.get('/app/stu/exams/getSignStatus', {
    params: query
  })
}
export const checkSignCode = (query) => {
  return utest.get('/app/stu/exams/checkSignCode', {
    params: query
  })
}
export const signIn = (data) => {
  return utest.post('/app/stu/exams/signIn', data)
}
export const terminalCheck = (data) => {
  return utest.post(`/exams/terminalVerification`, data)
}
export const getEndTimeNew = (query) => {
  return utest.get('/exams/learner/getExamEndTime', {
    params: query
  })
}
// 通过验证码签到
export const signInByCode = (data) => {
  return utest.post('/app/stu/exams/codeSignIn', data)
}

//保存本地答题记录和本地行为轨迹
export const saveLocalCache = (params) => {
  return utest.post(`/exams/saveLocalCache`, params)
}
// 更新登录信息
export const refreshSession = (uaToken) => {
  return utest.get(`/users/login/refresh10Session?uaToken=${uaToken}`)
}

// 验证码校验token接口
export const getVerificationCodeToken = (params) => {
  return utest.get(`/captcha/verifyCode`, {params})
}
// 获取屏幕一场次数
export const getScreenExceptionsApi = (params) => {
  return utest.get(`/exams/examuser/screenExceptions`, {params})
}
exam.interceptors.response.use(function (response) {
  return response
}, function (error) {
  if (!error.config.url.includes('umooc/behaviour.do')) {
    const { message, stack } = error
    const { url, params, data } = error.config
    saveError({ message, stack, location: window.location.href, url, params, data })
  }

  return Promise.reject(error)
})
