module.exports = {
  baseUrl: './',
  // publicPath: './',
  productionSourceMap: process.env.NODE_ENV === 'dev',
  devServer: {
   // https: true
  },
  configureWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = false
    } else {
      config.devtool = 'source-map'
    }
  },
  chainWebpack: config => {
    console.log(process.env.NODE_ENV)
    config.when(process.env.NODE_ENV === 'production', config => {
      config.optimization.splitChunks({
        chunks: 'all', // 默认只作用于异步模块，为`all`时对所有模块生效,`initial`对同步模块有效
        minSize: 30000, // 合并前模块文件的体积
        // minChunks: 1, // 最少被引用次数
        // maxAsyncRequests: 5,
        // maxInitialRequests: 3,
        // automaticNameDelimiter: '~',
        cacheGroups: {
          vendors: {
            name: 'vender-chunk',
            chunks: 'initial',
            test: /node_modules/,
            minChunks: 1,
            priority: -10,
            maxSize: 1000000
          },
          // asyncVendors: {
          //   name: 'vender-async',
          //   chunks: 'async',
          //   test: /node_modules/,
          //   minChunks: 1,
          //   priority: -12,
          //   maxSize: 1000000
          // },
          common: {
            // name: 'vender-common',
            chunks: 'async',
            test: /node_modules/,
            minChunks: 1,
            priority: -15,
            maxSize: 1000000
          },
          default: {
            test: /src/,
            name: 'src-common',
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          }
        }
      })
      config.optimization.runtimeChunk({
        name: 'runtime'
      })
    })
    config.when(process.env.NODE_ENV === 'production' && process.env.NPM_CONFIG_ANALYZER === 'TRUE', config => {
      config
        .plugin('webpack-bundle-analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [{
          // analyzerMode: 'disabled', // 不启动展示打包报告的http服务器
          generateStatsFile: true // 是否生成stats.json文件
        }])
    })
  },
  css: {
    loaderOptions: {
      stylus: {
        'resolve url': true,
        import: ['./src/common/styles/variables.styl']
      }
    }
  },
  pluginOptions: {
    'cube-ui': {
      postCompile: true,
      theme: false
    }
  }
}
