{"name": "exam", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyzer": "vue-cli-service build --mode analyzer"}, "dependencies": {"@better-scroll/core": "^2.5.0", "@better-scroll/mouse-wheel": "^2.5.0", "@better-scroll/scroll-bar": "^2.5.0", "axios": "^0.18.0", "crypto-js": "^4.2.0", "exif-js": "^2.3.0", "lamejs": "^1.2.0", "lodash": "^4.17.11", "mediaelement": "^4.2.9", "msr": "^1.3.4", "postcss": "^7.0.36", "qiniu-js": "^2.5.4", "swiper": "^4.0.7", "vue": "^2.5.17", "vue-awesome-swiper": "^3.1.3", "vue-baidu-map": "^0.21.22", "vue-i18n": "^8.14.1", "vue-loader": "^15.7.1", "vue-photo-preview": "^1.1.2", "vue-router": "^3.0.1", "vue-touch": "^2.0.0-beta.4", "vuedraggable": "^2.17.0", "vuex": "^3.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.2.1", "@vue/cli-service": "^3.1.2", "@vue/eslint-config-standard": "^3.0.5", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-plugin-lodash": "^3.3.4", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "cube-ui": "^1.12.6", "mathjs": "^4.1.1", "postcss-rtl": "^1.7.3", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "vue-cli-plugin-cube-ui": "^0.2.2", "vue-template-compiler": "^2.5.17", "webpack": "^4.42.0"}, "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}}